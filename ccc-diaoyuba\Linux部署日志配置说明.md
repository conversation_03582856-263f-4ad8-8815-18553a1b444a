# Linux生产环境部署日志配置说明

## 📋 配置概述

已为您的Linux生产环境配置了完整的日志系统，日志将输出到 `/log` 目录并按天分隔。

## 🔧 修改内容

### 1. 日志配置文件修改
- **logback-spring.xml**: 配置日志输出到 `/log/diaoyuba.log`，按天滚动
- **application-pro.yaml**: 设置日志文件路径为 `/log/diaoyuba.log`

### 2. 日志文件结构
```
/log/
├── diaoyuba.log              # 主应用日志
├── diaoyuba-error.log        # 错误日志（仅ERROR级别）
├── diaoyuba.2024-01-15.1.log.gz  # 历史日志（按天压缩）
├── gc.log                    # GC日志
└── startup.log               # 启动日志
```

### 3. 日志滚动策略
- **按天滚动**: 每天生成新的日志文件
- **按大小滚动**: 单个文件超过100MB时分割
- **压缩存储**: 历史日志自动压缩为.gz格式
- **保留期限**: 普通日志保留30天，错误日志保留60天
- **总大小限制**: 最大10GB

## 🚀 启动方式

### 方式1: 使用您原来的命令（推荐修改版）
```bash
# 创建日志目录
mkdir -p /log
chmod 755 /log

# 启动应用（修改后的命令）
/usr/local/btjdk/jdk8/bin/java -jar \
  -Xmx1024M -Xms256M \
  -Dlogging.file.name=/log/diaoyuba.log \
  -Dlogging.file.path=/log \
  -Dfile.encoding=UTF-8 \
  -Duser.timezone=Asia/Shanghai \
  -XX:+UseG1GC \
  -XX:+PrintGCDetails \
  -XX:+PrintGCTimeStamps \
  -Xloggc:/log/gc.log \
  /www/wwwroot/yudao-server/yudao-server.jar \
  --server.port=48080 \
  --spring.profiles.active=pro
```

### 方式2: 使用提供的启动脚本
```bash
# 设置执行权限
chmod +x start-production.sh stop-production.sh status-check.sh log-manager.sh

# 启动应用
./start-production.sh

# 停止应用
./stop-production.sh

# 检查状态
./status-check.sh
```

## 📖 日志管理

### 查看日志
```bash
# 实时查看应用日志
tail -f /log/diaoyuba.log

# 查看错误日志
tail -f /log/diaoyuba-error.log

# 查看最近100行
tail -n 100 /log/diaoyuba.log

# 搜索关键词
grep "ERROR" /log/diaoyuba.log

# 使用日志管理脚本
./log-manager.sh tail 50      # 查看最后50行
./log-manager.sh error        # 查看错误日志
./log-manager.sh search ERROR # 搜索错误
./log-manager.sh size         # 查看日志大小
./log-manager.sh clean        # 清理旧日志
```

### 日志级别说明
- **INFO**: 一般信息日志
- **WARN**: 警告信息
- **ERROR**: 错误信息（同时写入错误日志文件）
- **DEBUG**: 调试信息（生产环境默认不输出）

## 🔍 监控和维护

### 应用状态检查
```bash
# 检查应用状态
./status-check.sh

# 检查进程
ps aux | grep yudao-server.jar

# 检查端口
netstat -tuln | grep 48080

# 检查HTTP服务
curl http://localhost:48080/actuator/health
```

### 日志维护
```bash
# 查看日志文件大小
du -sh /log/*

# 清理7天前的日志
find /log -name "*.log.gz" -mtime +7 -delete

# 归档当前日志
./log-manager.sh archive
```

## ⚠️ 注意事项

### 1. 权限设置
确保应用有权限写入 `/log` 目录：
```bash
mkdir -p /log
chmod 755 /log
chown -R $(whoami):$(whoami) /log
```

### 2. 磁盘空间监控
定期检查 `/log` 目录的磁盘使用情况：
```bash
df -h /log
du -sh /log
```

### 3. 日志轮转
系统已配置自动日志轮转，但建议定期检查：
- 单个日志文件不超过100MB
- 总日志大小不超过10GB
- 自动清理30天前的日志

### 4. 错误监控
建议设置错误日志监控：
```bash
# 监控错误日志
./log-manager.sh monitor

# 或者使用系统工具
tail -f /log/diaoyuba-error.log
```

## 🛠 故障排除

### 1. 日志文件不存在
- 检查 `/log` 目录权限
- 确认启动命令包含日志配置参数
- 查看启动日志: `cat /log/startup.log`

### 2. 日志没有按天分隔
- 检查 `logback-spring.xml` 配置
- 确认应用正常运行超过一天
- 查看日志配置是否生效

### 3. 磁盘空间不足
- 清理旧日志: `./log-manager.sh clean`
- 调整日志保留天数
- 增加磁盘空间

### 4. 应用无法启动
- 查看启动日志: `cat /log/startup.log`
- 检查Java环境: `/usr/local/btjdk/jdk8/bin/java -version`
- 确认JAR文件存在: `ls -la /www/wwwroot/yudao-server/yudao-server.jar`

## 📞 技术支持

如果遇到问题，请提供以下信息：
1. 错误日志: `/log/diaoyuba-error.log`
2. 启动日志: `/log/startup.log`
3. 系统信息: `uname -a`
4. Java版本: `/usr/local/btjdk/jdk8/bin/java -version`
5. 磁盘空间: `df -h`

现在您的应用日志将正确输出到 `/log/diaoyuba.log` 并按天自动分隔！
