import{ao as t,__tla as b}from"./index-97fffa0c.js";let r,e,s,l,o,u,i,n,d,g=Promise.all([(()=>{try{return b}catch{}})()]).then(async()=>{e=a=>t.get({url:"/infra/job/page",params:a}),i=a=>t.get({url:"/infra/job/get?id="+a}),s=a=>t.post({url:"/infra/job/create",data:a}),d=a=>t.put({url:"/infra/job/update",data:a}),u=a=>t.delete({url:"/infra/job/delete?id="+a}),o=a=>t.download({url:"/infra/job/export-excel",params:a}),l=(a,p)=>{const f={id:a,status:p};return t.put({url:"/infra/job/update-status",params:f})},n=a=>t.put({url:"/infra/job/trigger?id="+a}),r=a=>t.get({url:"/infra/job/get_next_times?id="+a})});export{g as __tla,r as a,e as b,s as c,l as d,o as e,u as f,i as g,n as r,d as u};
