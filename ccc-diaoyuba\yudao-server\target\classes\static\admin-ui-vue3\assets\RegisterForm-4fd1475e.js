import{_ as t,__tla as r}from"./RegisterForm.vue_vue_type_script_setup_true_lang-0655432b.js";import{__tla as _}from"./Form-abbdb81e.js";import{__tla as a}from"./index-97fffa0c.js";import{__tla as l}from"./el-virtual-list-404af680.js";import{__tla as o}from"./el-tree-select-9cc5ed33.js";import{__tla as m}from"./el-time-select-a903a952.js";import{__tla as c}from"./InputPassword-8eb3866f.js";import"./_plugin-vue_export-helper-1b428a4d.js";import{__tla as e}from"./style.css_vue_type_style_index_0_src_true_lang-2cb747d4.js";import{__tla as s}from"./UploadImg-33a9d58c.js";import{__tla as i}from"./el-image-viewer-fddfe81d.js";import{__tla as n}from"./useMessage-18385d4a.js";import{__tla as p}from"./UploadImgs-985b4279.js";import{__tla as f}from"./UploadImgs.vue_vue_type_style_index_0_scoped_ccffae08_lang-64d084ff.js";import{__tla as h}from"./UploadFile.vue_vue_type_style_index_0_scoped_73fc17ef_lang-cc46e8f9.js";import{__tla as u}from"./XButton-dd4d8780.js";import{__tla as y}from"./useForm-66271e88.js";import{__tla as d}from"./LoginFormTitle.vue_vue_type_script_setup_true_lang-f56773d4.js";let x=Promise.all([(()=>{try{return r}catch{}})(),(()=>{try{return _}catch{}})(),(()=>{try{return a}catch{}})(),(()=>{try{return l}catch{}})(),(()=>{try{return o}catch{}})(),(()=>{try{return m}catch{}})(),(()=>{try{return c}catch{}})(),(()=>{try{return e}catch{}})(),(()=>{try{return s}catch{}})(),(()=>{try{return i}catch{}})(),(()=>{try{return n}catch{}})(),(()=>{try{return p}catch{}})(),(()=>{try{return f}catch{}})(),(()=>{try{return h}catch{}})(),(()=>{try{return u}catch{}})(),(()=>{try{return y}catch{}})(),(()=>{try{return d}catch{}})()]).then(async()=>{});export{x as __tla,t as default};
