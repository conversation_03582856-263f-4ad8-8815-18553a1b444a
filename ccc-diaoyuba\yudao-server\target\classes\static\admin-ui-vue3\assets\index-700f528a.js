import{ao as y,d as G,r as u,f as J,O as L,o as k,c as Q,i as t,w as l,a as r,x as g,B as X,q as Y,j as m,a3 as $,F as aa,D as ea,I as ta,_ as ra,H as la,E as sa,n as oa,y as _a,z as ua,cj as na,__tla as ca}from"./index-97fffa0c.js";import{_ as pa,__tla as ia}from"./index-b39a19a1.js";import{_ as ma,__tla as da}from"./ReplyForm.vue_vue_type_script_setup_true_lang-4b6c3157.js";import{R as D,__tla as ya}from"./TabNews-4fdeb460.js";import{__tla as fa}from"./main-4dd868b0.js";import{__tla as va}from"./useUpload-36312237.js";import{u as ha,__tla as Ma}from"./useMessage-18385d4a.js";import{__tla as ga}from"./TabImage-cf1eaa22.js";import{__tla as Ia}from"./main-7292042e.js";import{__tla as Ua}from"./TabVoice-7070a31e.js";import{__tla as wa}from"./main.vue_vue_type_script_setup_true_lang-4cb32a33.js";import{__tla as ba}from"./TabVideo-5d569b78.js";import{__tla as Ta}from"./el-image-1637bc2a.js";import{__tla as xa}from"./el-image-viewer-fddfe81d.js";import{__tla as Va}from"./main-17919147.js";import{__tla as qa}from"./main-157d4130.js";import{_ as Ca,__tla as ka}from"./main.vue_vue_type_script_setup_true_lang-4906f08f.js";import{_ as K,__tla as Da}from"./ContentWrap.vue_vue_type_script_setup_true_lang-a574ccef.js";import{_ as Ka,__tla as Aa}from"./ReplyTable.vue_vue_type_script_setup_true_lang-f1af96e1.js";import{M as d}from"./types-5e186e8c.js";import"./_plugin-vue_export-helper-1b428a4d.js";import{__tla as Fa}from"./dict-6a82eb12.js";import{__tla as Na}from"./index.vue_vue_type_script_setup_true_lang-d31568cf.js";import{__tla as Ha}from"./index-8d6db4ce.js";import{__tla as Oa}from"./index-aa57e946.js";import{__tla as Ra}from"./index-3b46e2ef.js";import{__tla as ja}from"./formatTime-9d54d2c5.js";import{__tla as za}from"./TabText.vue_vue_type_script_setup_true_lang-36a4c895.js";import{__tla as Pa}from"./TabMusic.vue_vue_type_script_setup_true_lang-93761c9f.js";import{__tla as Ba}from"./index-f765db10.js";import{__tla as Ea}from"./el-card-6c7c099d.js";import{__tla as Sa}from"./DictTag.vue_vue_type_script_lang-5679ad7b.js";import"./color-a8b4eb58.js";import{__tla as Wa}from"./main-5b6ce8ab.js";import{__tla as Za}from"./el-link-f00f9c89.js";let A,Ga=Promise.all([(()=>{try{return ca}catch{}})(),(()=>{try{return ia}catch{}})(),(()=>{try{return da}catch{}})(),(()=>{try{return ya}catch{}})(),(()=>{try{return fa}catch{}})(),(()=>{try{return va}catch{}})(),(()=>{try{return Ma}catch{}})(),(()=>{try{return ga}catch{}})(),(()=>{try{return Ia}catch{}})(),(()=>{try{return Ua}catch{}})(),(()=>{try{return wa}catch{}})(),(()=>{try{return ba}catch{}})(),(()=>{try{return Ta}catch{}})(),(()=>{try{return xa}catch{}})(),(()=>{try{return Va}catch{}})(),(()=>{try{return qa}catch{}})(),(()=>{try{return ka}catch{}})(),(()=>{try{return Da}catch{}})(),(()=>{try{return Aa}catch{}})(),(()=>{try{return Fa}catch{}})(),(()=>{try{return Na}catch{}})(),(()=>{try{return Ha}catch{}})(),(()=>{try{return Oa}catch{}})(),(()=>{try{return Ra}catch{}})(),(()=>{try{return ja}catch{}})(),(()=>{try{return za}catch{}})(),(()=>{try{return Pa}catch{}})(),(()=>{try{return Ba}catch{}})(),(()=>{try{return Ea}catch{}})(),(()=>{try{return Sa}catch{}})(),(()=>{try{return Wa}catch{}})(),(()=>{try{return Za}catch{}})()]).then(async()=>{A=G({name:"MpAutoReply",__name:"index",setup(Ja){const f=ha(),C=u(-1),_=u(d.Keyword),I=u(!0),F=u(0),U=u([]),w=u(null),n=J({pageNo:1,pageSize:10,accountId:C}),b=u(!1),c=u(!1),o=u({}),s=u({type:D.Text,accountId:-1}),N=a=>{C.value=a,s.value.accountId=a,n.pageNo=1,v()},v=async()=>{I.value=!0;try{const e=await(a={...n,type:_.value},y.get({url:"/mp/auto-reply/page",params:a}));U.value=e.list,F.value=e.total}finally{I.value=!1}var a},H=a=>{_.value=a,n.pageNo=1,v()},O=()=>{T(),s.value={type:D.Text,accountId:n.accountId},b.value=!0,c.value=!0},R=async a=>{T();const e=await(i=>y.get({url:"/mp/auto-reply/get?id="+i}))(a);o.value={...e},delete o.value.responseMessageType,delete o.value.responseContent,delete o.value.responseMediaId,delete o.value.responseMediaUrl,delete o.value.responseDescription,delete o.value.responseArticles,s.value={type:e.responseMessageType,accountId:n.accountId,content:e.responseContent,mediaId:e.responseMediaId,url:e.responseMediaUrl,title:e.responseTitle,description:e.responseDescription,thumbMediaId:e.responseThumbMediaId,thumbMediaUrl:e.responseThumbMediaUrl,articles:e.responseArticles,musicUrl:e.responseMusicUrl,hqMusicUrl:e.responseHqMusicUrl},b.value=!1,c.value=!0},j=async a=>{await f.confirm("\u662F\u5426\u786E\u8BA4\u5220\u9664\u6B64\u6570\u636E?"),await(e=>y.delete({url:"/mp/auto-reply/delete?id="+e}))(a),await v(),f.success("\u5220\u9664\u6210\u529F")},z=async()=>{var i;await((i=w.value)==null?void 0:i.validate());const a={...o.value};var e;a.responseMessageType=s.value.type,a.responseContent=s.value.content,a.responseMediaId=s.value.mediaId,a.responseMediaUrl=s.value.url,a.responseTitle=s.value.title,a.responseDescription=s.value.description,a.responseThumbMediaId=s.value.thumbMediaId,a.responseThumbMediaUrl=s.value.thumbMediaUrl,a.responseArticles=s.value.articles,a.responseMusicUrl=s.value.musicUrl,a.responseHqMusicUrl=s.value.hqMusicUrl,o.value.id!==void 0?(await(e=a,y.put({url:"/mp/auto-reply/update",data:e})),f.success("\u4FEE\u6539\u6210\u529F")):(await(x=>y.post({url:"/mp/auto-reply/create",data:x}))(a),f.success("\u65B0\u589E\u6210\u529F")),c.value=!1,await v()},T=()=>{var a;o.value={id:void 0,accountId:n.accountId,type:_.value,requestKeyword:void 0,requestMatch:_.value===d.Keyword?1:void 0,requestMessageType:void 0},(a=w.value)==null||a.resetFields()},P=()=>{c.value=!1,T()};return(a,e)=>{const i=pa,x=ea,B=ta,h=ra,V=la,E=sa,M=oa,q=_a,S=ua,W=na,Z=L("hasPermi");return k(),Q(aa,null,[t(i,{title:"\u81EA\u52A8\u56DE\u590D",url:"https://doc.iocoder.cn/mp/auto-reply/"}),t(r(K),null,{default:l(()=>[t(B,{class:"-mb-15px",model:r(n),inline:!0,"label-width":"68px"},{default:l(()=>[t(x,{label:"\u516C\u4F17\u53F7",prop:"accountId"},{default:l(()=>[t(r(Ca),{onChange:N})]),_:1})]),_:1},8,["model"])]),_:1}),t(r(K),null,{default:l(()=>[t(S,{modelValue:r(_),"onUpdate:modelValue":e[0]||(e[0]=p=>g(_)?_.value=p:null),onTabChange:H},{default:l(()=>[t(M,{gutter:10,class:"mb8"},{default:l(()=>[t(E,{span:1.5},{default:l(()=>[r(_)!==r(d).Follow||r(U).length<=0?X((k(),Y(V,{key:0,type:"primary",plain:"",onClick:O},{default:l(()=>[t(h,{icon:"ep:plus"}),m("\u65B0\u589E ")]),_:1})),[[Z,["mp:auto-reply:create"]]]):$("",!0)]),_:1})]),_:1}),t(q,{name:r(d).Follow},{label:l(()=>[t(M,{align:"middle"},{default:l(()=>[t(h,{icon:"ep:star",class:"mr-2px"}),m(" \u5173\u6CE8\u65F6\u56DE\u590D")]),_:1})]),_:1},8,["name"]),t(q,{name:r(d).Message},{label:l(()=>[t(M,{align:"middle"},{default:l(()=>[t(h,{icon:"ep:chat-line-round",class:"mr-2px"}),m(" \u6D88\u606F\u56DE\u590D")]),_:1})]),_:1},8,["name"]),t(q,{name:r(d).Keyword},{label:l(()=>[t(M,{align:"middle"},{default:l(()=>[t(h,{icon:"fa:newspaper-o",class:"mr-2px"}),m(" \u5173\u952E\u8BCD\u56DE\u590D")]),_:1})]),_:1},8,["name"])]),_:1},8,["modelValue"]),t(Ka,{loading:r(I),list:r(U),"msg-type":r(_),onOnUpdate:R,onOnDelete:j},null,8,["loading","list","msg-type"]),t(W,{title:r(b)?"\u65B0\u589E\u81EA\u52A8\u56DE\u590D":"\u4FEE\u6539\u81EA\u52A8\u56DE\u590D",modelValue:r(c),"onUpdate:modelValue":e[3]||(e[3]=p=>g(c)?c.value=p:null),width:"800px","destroy-on-close":""},{footer:l(()=>[t(V,{onClick:P},{default:l(()=>[m("\u53D6 \u6D88")]),_:1}),t(V,{type:"primary",onClick:z},{default:l(()=>[m("\u786E \u5B9A")]),_:1})]),default:l(()=>[t(ma,{modelValue:r(o),"onUpdate:modelValue":e[1]||(e[1]=p=>g(o)?o.value=p:null),reply:r(s),"onUpdate:reply":e[2]||(e[2]=p=>g(s)?s.value=p:null),"msg-type":r(_),ref_key:"formRef",ref:w},null,8,["modelValue","reply","msg-type"])]),_:1},8,["title","modelValue"])]),_:1})],64)}}})});export{Ga as __tla,A as default};
