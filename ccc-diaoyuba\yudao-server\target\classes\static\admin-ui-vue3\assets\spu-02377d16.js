import{ao as a,__tla as g}from"./index-97fffa0c.js";let u,s,p,r,e,l,d,o,c,i,n=Promise.all([(()=>{try{return g}catch{}})()]).then(async()=>{s=t=>a.get({url:"/product/spu/page",params:t}),u=()=>a.get({url:"/product/spu/get-count"}),p=t=>a.post({url:"/product/spu/create",data:t}),i=t=>a.put({url:"/product/spu/update",data:t}),r=t=>a.put({url:"/product/spu/update-status",data:t}),d=t=>a.get({url:`/product/spu/get-detail?id=${t}`}),c=t=>a.get({url:`/product/spu/list?spuIds=${t}`}),e=t=>a.delete({url:`/product/spu/delete?id=${t}`}),l=async t=>await a.download({url:"/product/spu/export",params:t}),o=async()=>a.get({url:"/product/spu/list-all-simple"})});export{n as __tla,u as a,s as b,p as c,r as d,e,l as f,d as g,o as h,c as i,i as u};
