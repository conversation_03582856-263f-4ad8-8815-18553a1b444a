#!/bin/bash

# 网络诊断脚本 - 检查水位数据源连接问题

DOMAIN="xxfb.mwr.cn"
URL="http://$DOMAIN"

echo "=========================================="
echo "水位数据源网络诊断"
echo "=========================================="

echo "1. 基本网络信息:"
echo "   目标域名: $DOMAIN"
echo "   目标URL: $URL"
echo "   当前时间: $(date)"
echo ""

echo "2. DNS解析测试:"
echo "   nslookup结果:"
nslookup $DOMAIN | head -10
echo ""

echo "   dig结果:"
if command -v dig > /dev/null 2>&1; then
    dig $DOMAIN A +short
else
    echo "   dig命令不可用"
fi
echo ""

echo "3. 网络连通性测试:"
echo "   ping测试 (5次):"
ping -c 5 $DOMAIN 2>&1 | head -10
echo ""

echo "4. HTTP访问测试:"
echo "   curl测试:"
if command -v curl > /dev/null 2>&1; then
    echo "   HTTP头信息:"
    curl -I --connect-timeout 10 --max-time 30 $URL 2>&1 | head -10
    echo ""
    echo "   完整HTTP请求:"
    curl --connect-timeout 10 --max-time 30 -s $URL | head -5 | wc -l
    if [ $? -eq 0 ]; then
        echo "   ✅ HTTP请求成功"
    else
        echo "   ❌ HTTP请求失败"
    fi
else
    echo "   curl命令不可用"
fi
echo ""

echo "5. 网络路由测试:"
echo "   traceroute测试:"
if command -v traceroute > /dev/null 2>&1; then
    traceroute $DOMAIN 2>&1 | head -10
elif command -v tracert > /dev/null 2>&1; then
    tracert $DOMAIN 2>&1 | head -10
else
    echo "   traceroute命令不可用"
fi
echo ""

echo "6. DNS配置检查:"
echo "   /etc/resolv.conf内容:"
cat /etc/resolv.conf 2>/dev/null || echo "   无法读取DNS配置"
echo ""

echo "7. 防火墙检查:"
echo "   iptables规则 (出站HTTP):"
if command -v iptables > /dev/null 2>&1; then
    iptables -L OUTPUT | grep -i http || echo "   没有发现HTTP相关规则"
else
    echo "   无法检查iptables"
fi
echo ""

echo "8. 系统网络配置:"
echo "   网络接口:"
ip addr show 2>/dev/null | grep -E "inet |UP" | head -5 || ifconfig 2>/dev/null | grep -E "inet |UP" | head -5
echo ""

echo "9. 建议解决方案:"
echo "   如果DNS解析失败:"
echo "     1. 检查DNS服务器配置"
echo "     2. 尝试使用公共DNS (*******, ***************)"
echo "     3. 在/etc/hosts中添加IP映射"
echo ""
echo "   如果网络不通:"
echo "     1. 检查防火墙设置"
echo "     2. 检查网络代理配置"
echo "     3. 联系网络管理员"
echo ""

echo "10. 获取域名IP地址:"
echo "    在本地环境执行以下命令获取IP:"
echo "    nslookup $DOMAIN"
echo "    然后在生产环境的/etc/hosts中添加:"
echo "    IP地址 $DOMAIN"
echo ""

echo "=========================================="
echo "诊断完成"
echo "=========================================="
