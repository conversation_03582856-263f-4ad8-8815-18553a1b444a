#!/bin/bash

# 生产环境停止脚本 - 钓鱼吧应用

APP_NAME="diaoyuba"
PID_FILE="/var/run/diaoyuba.pid"
LOG_DIR="/log"

echo "=========================================="
echo "停止 $APP_NAME 应用"
echo "=========================================="

# 检查PID文件是否存在
if [ ! -f "$PID_FILE" ]; then
    echo "⚠️ PID文件不存在: $PID_FILE"
    echo "尝试查找运行中的进程..."
    
    # 通过进程名查找
    PID=$(ps aux | grep "yudao-server.jar" | grep -v grep | awk '{print $2}')
    if [ ! -z "$PID" ]; then
        echo "找到进程 PID: $PID"
    else
        echo "❌ 没有找到运行中的应用进程"
        exit 1
    fi
else
    PID=$(cat "$PID_FILE")
    echo "从PID文件读取 PID: $PID"
fi

# 检查进程是否存在
if ! ps -p $PID > /dev/null 2>&1; then
    echo "❌ 进程 $PID 不存在"
    rm -f "$PID_FILE"
    exit 1
fi

echo "🛑 正在停止应用 (PID: $PID)..."

# 优雅停止
kill -TERM $PID

# 等待进程停止
echo "等待进程停止..."
for i in {1..30}; do
    if ! ps -p $PID > /dev/null 2>&1; then
        echo "✅ 应用已停止"
        rm -f "$PID_FILE"
        
        # 记录停止时间到日志
        if [ -d "$LOG_DIR" ]; then
            echo "$(date '+%Y-%m-%d %H:%M:%S') - 应用已停止" >> "$LOG_DIR/diaoyuba.log"
        fi
        
        exit 0
    fi
    sleep 1
    echo -n "."
done

echo ""
echo "⚠️ 优雅停止超时，强制停止进程..."

# 强制停止
kill -9 $PID

# 再次检查
sleep 2
if ps -p $PID > /dev/null 2>&1; then
    echo "❌ 无法停止进程 $PID"
    exit 1
else
    echo "✅ 应用已强制停止"
    rm -f "$PID_FILE"
    
    # 记录强制停止到日志
    if [ -d "$LOG_DIR" ]; then
        echo "$(date '+%Y-%m-%d %H:%M:%S') - 应用已强制停止" >> "$LOG_DIR/diaoyuba.log"
    fi
fi
