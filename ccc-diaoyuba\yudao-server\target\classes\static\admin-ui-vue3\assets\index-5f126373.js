import{ao as r,__tla as n}from"./index-97fffa0c.js";let e,t,d,s,i,p,u,c,y,l,o,w=Promise.all([(()=>{try{return n}catch{}})()]).then(async()=>{i=async a=>await r.get({url:"/trade/order/page",params:a}),p=async a=>await r.get({url:"/trade/order/summary",params:a}),e=async a=>await r.get({url:"/trade/order/get-detail?id="+a}),u=async a=>await r.get({url:"/trade/order/get-express-track-list?id="+a}),s=async a=>await r.put({url:"/trade/order/delivery",data:a}),o=async a=>await r.put({url:"/trade/order/update-remark",data:a}),d=async a=>await r.put({url:"/trade/order/update-price",data:a}),t=async a=>await r.put({url:"/trade/order/update-address",data:a}),l=async a=>await r.put({url:`/trade/order/pick-up-by-id?id=${a}`}),c=async a=>await r.put({url:"/trade/order/pick-up-by-verify-code",params:{pickUpVerifyCode:a}}),y=async a=>await r.get({url:"/trade/order/get-by-pick-up-verify-code",params:{pickUpVerifyCode:a}})});export{w as __tla,e as a,t as b,d as c,s as d,i as e,p as f,u as g,c as h,y as i,l as p,o as u};
