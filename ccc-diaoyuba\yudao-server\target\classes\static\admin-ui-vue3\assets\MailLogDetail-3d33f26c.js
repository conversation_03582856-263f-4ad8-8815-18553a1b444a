import{_ as t,__tla as r}from"./MailLogDetail.vue_vue_type_script_setup_true_lang-2064d691.js";import{__tla as _}from"./Dialog.vue_vue_type_style_index_0_lang-5af0bc2b.js";import{__tla as a}from"./index-97fffa0c.js";import{__tla as l}from"./Descriptions-13090c1a.js";import{__tla as o}from"./Descriptions.vue_vue_type_style_index_0_scoped_76061901_lang-e5833478.js";import{__tla as m}from"./el-descriptions-item-5b1e935d.js";import{__tla as c}from"./DictTag.vue_vue_type_script_lang-5679ad7b.js";import"./color-a8b4eb58.js";import{__tla as e}from"./dict-6a82eb12.js";import"./_plugin-vue_export-helper-1b428a4d.js";import{__tla as i}from"./formatTime-9d54d2c5.js";import{__tla as p}from"./index-3c0a595a.js";import{__tla as s}from"./useCrudSchemas-6394b852.js";import"./tree-ebab458e.js";let n=Promise.all([(()=>{try{return r}catch{}})(),(()=>{try{return _}catch{}})(),(()=>{try{return a}catch{}})(),(()=>{try{return l}catch{}})(),(()=>{try{return o}catch{}})(),(()=>{try{return m}catch{}})(),(()=>{try{return c}catch{}})(),(()=>{try{return e}catch{}})(),(()=>{try{return i}catch{}})(),(()=>{try{return p}catch{}})(),(()=>{try{return s}catch{}})()]).then(async()=>{});export{n as __tla,t as default};
