import{d as T,u as q,r as s,f as F,A as H,ai as J,a as r,o as m,q as p,w as t,B as K,i as a,j as f,t as k,g as P,x as R,J as U,aj as z,_ as N,H as X,K as Y,E as Q,L as V,__tla as W}from"./index-97fffa0c.js";import{E as Z,__tla as $}from"./el-card-6c7c099d.js";import{_ as aa,__tla as ta}from"./ContentWrap.vue_vue_type_script_setup_true_lang-a574ccef.js";import{_ as ra,__tla as la}from"./DictTag.vue_vue_type_script_lang-5679ad7b.js";import{D as ea,__tla as _a}from"./dict-6a82eb12.js";import{b as oa,a as sa,__tla as na}from"./index-a2bf77df.js";import{c as ia,__tla as ca}from"./index-db126fd7.js";import{b as ua}from"./formCreate-a3356cdc.js";import{_ as ma,__tla as pa}from"./ProcessInstanceBpmnViewer.vue_vue_type_style_index_0_lang-bb764b8b.js";import{u as fa,__tla as ya}from"./useMessage-18385d4a.js";import"./color-a8b4eb58.js";import{__tla as da}from"./bpmn-embedded-5f95dcf2.js";import{__tla as ha}from"./Dialog.vue_vue_type_style_index_0_lang-5af0bc2b.js";import{__tla as va}from"./XTextButton-41b6d860.js";import"./_plugin-vue_export-helper-1b428a4d.js";import{__tla as ba}from"./XButton-dd4d8780.js";import{__tla as ga}from"./el-link-f00f9c89.js";import{__tla as Ca}from"./el-drawer-0535e62a.js";import{__tla as wa}from"./formatTime-9d54d2c5.js";let D,xa=Promise.all([(()=>{try{return W}catch{}})(),(()=>{try{return $}catch{}})(),(()=>{try{return ta}catch{}})(),(()=>{try{return la}catch{}})(),(()=>{try{return _a}catch{}})(),(()=>{try{return na}catch{}})(),(()=>{try{return ca}catch{}})(),(()=>{try{return pa}catch{}})(),(()=>{try{return ya}catch{}})(),(()=>{try{return da}catch{}})(),(()=>{try{return ha}catch{}})(),(()=>{try{return va}catch{}})(),(()=>{try{return ba}catch{}})(),(()=>{try{return ga}catch{}})(),(()=>{try{return Ca}catch{}})(),(()=>{try{return wa}catch{}})()]).then(async()=>{let y,d;y={class:"clearfix"},d={class:"el-icon-document"},D=T({name:"BpmProcessInstanceCreate",__name:"index",setup(ka){const h=q(),E=fa(),c=s(!0),v=s([]),A=F({suspensionState:1}),b=s(null),_=s(),u=s({rule:[],option:{}}),o=s(),B=async g=>{if(_.value&&o.value){_.value.btn.loading(!0);try{await ia({processDefinitionId:o.value.id,variables:g}),E.success("\u53D1\u8D77\u6D41\u7A0B\u6210\u529F"),h.go(-1)}finally{_.value.btn.loading(!1)}}};return H(()=>{(async()=>{c.value=!0;try{v.value=await oa(A)}finally{c.value=!1}})()}),(g,i)=>{const n=U,L=ra,O=z,C=N,w=X,j=Y,x=aa,G=J("form-create"),I=Q,M=Z,S=V;return r(o)?(m(),p(x,{key:1},{default:t(()=>[a(M,{class:"box-card"},{default:t(()=>[P("div",y,[P("span",d,"\u7533\u8BF7\u4FE1\u606F\u3010"+k(r(o).name)+"\u3011",1),a(w,{style:{float:"right"},type:"primary",onClick:i[0]||(i[0]=l=>o.value=void 0)},{default:t(()=>[a(C,{icon:"ep:delete"}),f(" \u9009\u62E9\u5176\u5B83\u6D41\u7A0B ")]),_:1})]),a(I,{span:16,offset:6,style:{"margin-top":"20px"}},{default:t(()=>[a(G,{rule:r(u).rule,api:r(_),"onUpdate:api":i[1]||(i[1]=l=>R(_)?_.value=l:null),option:r(u).option,onSubmit:B},null,8,["rule","api","option"])]),_:1})]),_:1}),a(ma,{"bpmn-xml":r(b)},null,8,["bpmn-xml"])]),_:1})):(m(),p(x,{key:0},{default:t(()=>[K((m(),p(j,{data:r(v)},{default:t(()=>[a(n,{label:"\u6D41\u7A0B\u540D\u79F0",align:"center",prop:"name"}),a(n,{label:"\u6D41\u7A0B\u5206\u7C7B",align:"center",prop:"category"},{default:t(l=>[a(L,{type:r(ea).BPM_MODEL_CATEGORY,value:l.row.category},null,8,["type","value"])]),_:1}),a(n,{label:"\u6D41\u7A0B\u7248\u672C",align:"center",prop:"version"},{default:t(l=>[a(O,null,{default:t(()=>[f("v"+k(l.row.version),1)]),_:2},1024)]),_:1}),a(n,{label:"\u6D41\u7A0B\u63CF\u8FF0",align:"center",prop:"description"}),a(n,{label:"\u64CD\u4F5C",align:"center"},{default:t(l=>[a(w,{link:"",type:"primary",onClick:Pa=>(async e=>{o.value=e,e.formType==10?(ua(u,e.formConf,e.formFields),b.value=await sa(e.id)):e.formCustomCreatePath&&await h.push({path:e.formCustomCreatePath})})(l.row)},{default:t(()=>[a(C,{icon:"ep:plus"}),f(" \u9009\u62E9 ")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[S,r(c)]])]),_:1}))}}})});export{xa as __tla,D as default};
