package cn.iocoder.yudao.module.diaoyuba.job;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSON;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.iocoder.yudao.framework.quartz.core.handler.JobHandler;
import cn.iocoder.yudao.module.diaoyuba.enums.WaterTypeEum;
import cn.iocoder.yudao.module.diaoyuba.service.waterstation.WaterStationService;
import cn.iocoder.yudao.module.diaoyuba.service.waterstationflow.WaterStationFlowService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.awt.*;
import java.io.*;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Description
 * <AUTHOR>
 * @Date Created in 2024/8/9
 */
@Slf4j
@Component
public class WaterLevelJob implements JobHandler {


    public static final String DOMAIN = "http://xxfb.mwr.cn";
    @Resource
    private WaterStationService waterStationService;

    @Override
    public String execute(String param) throws Exception {

        // 记录耗时
        long start = System.currentTimeMillis();
        log.info("WaterLevelJob开始执行");
        // 获取数据江河数据
        List<JSONObject> jhData = getDataList(WaterTypeEum.RIVER);
        // 获取耗时
        long end = System.currentTimeMillis();
        log.info("WaterLevelJob 江河数据获取 执行结束，耗时：" + (end - start) + "ms");
        log.info("WaterLevelJob江河数据条数：" + jhData.size());

        waterStationService.updateWaterStationJH(jhData);
        // 插入耗时
        log.info("WaterLevelJob插入耗时：" + (System.currentTimeMillis() - end) + "ms");


        List<JSONObject> hkData = getDataList(WaterTypeEum.LAKE);
        log.info("WaterLevelJob 江河数据获取 执行结束，耗时：" + (System.currentTimeMillis() - end) + "ms");

        log.info("WaterLevelJob湖库数据条数：" + hkData.size());
        waterStationService.updateWaterStationHK(hkData);
        log.info("WaterLevelJob插入耗时：" + (System.currentTimeMillis() - end) + "ms");


        return "ok";
    }

    @SneakyThrows
    public static List<JSONObject> getDataList(WaterTypeEum type) {
        List<JSONObject> jhData = null;
        try {
            jhData = getData(type);
        } catch (Exception e) {
            log.error("获取数据失败", e);
        }
        // 可能失败再获取一次
        if (jhData == null){
            Thread.sleep(20000);
            jhData = getData(type);
        }
        return jhData;
    }

    public static void main(String[] args) {



        testData();

//        List<JSONObject> data = getData(WaterTypeEum.LAKE);
//        System.out.println(data);
//
//        System.out.println("-------------------");
//        List<JSONObject> data1 = getData(WaterTypeEum.RIVER);
//        System.out.println(data1);


    }

    private static void testData() {
        // 自定义字体文件的路径
        String fontPath = "C:\\Users\\<USER>\\Downloads\\niU1PYSuyW_1723193235961.ttf";
        String filename = "D:\\project\\study\\ccc-diaoyuba\\file\\response.txt"; // 替换为实际的文件路径
        StringBuilder data =  new StringBuilder();
        try (BufferedReader reader = new BufferedReader(new FileReader(filename))) {
            String line;
            while ((line = reader.readLine()) != null) {
                data.append(line);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }

        HashMap<String, String> dictionaryMap = loadAndPrintChineseCharacters(fontPath, WaterTypeEum.RIVER);
        String responseBody = data.toString();
        JSON parse = JSONUtil.parse(responseBody);
        // 获取密文
        List<JSONObject> result = parse.getByPath("result", List.class);
        String addvnm0 = result.get(0).get("addvnm", String.class);
        // 得到css名称
        String cssFileName = extractName(addvnm0);
        String startCssFileName = "#" + cssFileName + "otltag";
        // 得到密文前缀
        int startIndex = startCssFileName.length();

        // 解密数据
        List<JSONObject> dataList = new ArrayList<>();

        for (JSONObject obj : result) {
            // 提取加密数据
            JSONObject item = new JSONObject();
            for (Map.Entry<String, Object> stringObjectEntry : obj) {
                Object value = stringObjectEntry.getValue();
                if(value != null){
                    String str = value.toString();
                    // 需要解密
                    if (str.contains(startCssFileName)){
                        item.putOpt(stringObjectEntry.getKey(), extractCiphertext(str, startIndex, dictionaryMap));
                    }else {
                        item.putOpt(stringObjectEntry.getKey(), value);
                    }
                }
            }
            dataList.add(item);

        }
    }

    private static List<JSONObject> getData(WaterTypeEum type) {
        List<JSONObject> result = getJHData(type.getUrl());

        String addvnm0 = result.get(0).get("addvnm", String.class);
        // 得到css名称
        String cssFileName = extractName(addvnm0);
        String startCssFileName = "#" + cssFileName + "otltag";
        // 得到密文前缀
        int startIndex = startCssFileName.length();
        // 获取字典
        HashMap<String, String> dictionaryMap = getStringStringHashMap(cssFileName, type);

        // 解密数据
        List<JSONObject> dataList = new ArrayList<>();

        for (JSONObject obj : result) {
            // 提取加密数据
            JSONObject item = new JSONObject();
            for (Map.Entry<String, Object> stringObjectEntry : obj) {
                Object value = stringObjectEntry.getValue();
                if(value != null){
                    String str = value.toString();
                    // 需要解密
                    if (str.contains(startCssFileName)){
                        item.putOpt(stringObjectEntry.getKey(), extractCiphertext(str, startIndex, dictionaryMap));
                    }else {
                        item.putOpt(stringObjectEntry.getKey(), value);
                    }
                }
            }
            dataList.add(item);

        }
        return dataList;
    }

    private static HashMap<String, String> getStringStringHashMap(String cssFileName, WaterTypeEum type) {
        // 获得css字典
        HttpRequest request = HttpUtil.createGet(DOMAIN + "/" + "ttf/" + cssFileName + ".ttf")
                .header("Accept", "*/*")
                .header("Accept-Language", "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7,en-GB;q=0.6")
                .header("Connection", "keep-alive")
                .header("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");

        HttpResponse response = request.execute();
        InputStream inputStream = response.bodyStream();
        // 获取字典
        HashMap<String, String> dictionaryMap = loadAndPrintChineseCharacters(inputStream, type);
        return dictionaryMap;
    }

    private static List<JSONObject> getJHData(String url) {
        // 获取动态uri
        String dynamicURI = getDynamicURI(url);
        // 获取加密数据
        HttpResponse response = HttpRequest.get(DOMAIN + dynamicURI)
                .header("Accept", "application/json, text/javascript, */*; q=0.01")
                .header("Accept-Language", "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7,en-GB;q=0.6")
                .header("Connection", "keep-alive")
                .header("Content-Type", "application/json")
                .header("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
                .header("X-Requested-With", "XMLHttpRequest")
                .execute();

        String responseBody = response.body();
        log.info(responseBody);
        JSON parse = JSONUtil.parse(responseBody);
        List<JSONObject> result = parse.getByPath("result", List.class);
        return result;
    }

    private static String extractCiphertext(String str, int startIndex, HashMap<String, String> dictionaryMap) {
        int endIndex = str.indexOf("#FontTag");
        if (startIndex != -1 && endIndex != -1) {
            StringBuilder sb = new StringBuilder();
            String sub = StrUtil.sub(str, startIndex, endIndex);
            char[] charArray = sub.toCharArray();
            for (char c : charArray) {
                // 46位之前的直接跳过
                if(c <= 46){
                    sb.append(c);
                    continue;
                }
                String s = convertToUnicode2(c);
                sb.append(dictionaryMap.get(s) == null ? c : dictionaryMap.get(s));
            }
            return sb.toString();
        } else {
            return "";
        }
    }

    private static String getDynamicURI(String url) {
        String dynamicURI = null;
        String html = HttpRequest.get(url)
                .header("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7")
                .header("Accept-Language", "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7,en-GB;q=0.6")
                .header("Cache-Control", "max-age=0")
                .header("Connection", "keep-alive")
                .header("Upgrade-Insecure-Requests", "1")
                .header("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
                .execute()
                .body();

        log.info(html);
        String uriPattern = "url:\\s*\"([^\"]+)\"";
        Pattern pattern = Pattern.compile(uriPattern);
        Matcher matcher = pattern.matcher(html);
        if (matcher.find()) {
            dynamicURI = matcher.group();
            log.info("Dynamic URI: " + dynamicURI);

        } else {
            log.info("Dynamic URI not found.");
        }

        int startIndex = dynamicURI.indexOf("\"");
        int endIndex = dynamicURI.lastIndexOf("\"");
        if (startIndex != -1 && endIndex != -1) {
            return StrUtil.sub(dynamicURI, startIndex + 1, endIndex);
        } else {
            return "";
        }
    }

    /**
     * <AUTHOR>
     * @description 获取字典map
     * @date 2024/8/9
     * @param
     * @return void
     **/
    public static HashMap<String, String> loadAndPrintChineseCharacters(InputStream inputStream,  WaterTypeEum type) {
        try {
            // 加载字体
            Font customFont = Font.createFont(Font.TRUETYPE_FONT, inputStream);
            return getDictionaryMap(customFont, type);


        } catch (FontFormatException | IOException e) {
            e.printStackTrace();
        }
        return new HashMap<>();
    }

    /**
     * <AUTHOR>
     * @description 获取字典map
     * @date 2024/8/9
     * @param
     * @return void
     **/
    public static HashMap<String, String> loadAndPrintChineseCharacters(String fontPath, WaterTypeEum type) {

        try {
            // 加载字体
            Font customFont = Font.createFont(Font.TRUETYPE_FONT, new File(fontPath));
            return getDictionaryMap(customFont, type);

        } catch (FontFormatException | IOException e) {
            e.printStackTrace();
        }
        return new HashMap<>();
    }

    /**
     * <AUTHOR>
     * @description 获取字典map
     * @date 2024/8/9
     * @param
     * @return void
     **/
    private static HashMap<String, String> getDictionaryMap(Font customFont, WaterTypeEum type) {
        HashMap<String, String> dictionaryMap = new HashMap<>();

        GraphicsEnvironment ge = GraphicsEnvironment.getLocalGraphicsEnvironment();
        ge.registerFont(customFont);

        char[] charArray = type.getDictionary().toCharArray();

        int i = 0;
        for (int codePoint = 0; codePoint <= Character.MAX_CODE_POINT; codePoint++) {
            if (customFont.canDisplay(codePoint)) {
                i++;
                if (i <= 20){
                    continue;
                }
                String character = new String(Character.toChars(codePoint));
                String s = convertToUnicode2(codePoint);
                int charIndex = i - 21;
                if (charIndex < charArray.length){
                    dictionaryMap.put(s, String.valueOf(charArray[charIndex]));
                }
            }
        }
        return dictionaryMap;
    }


    public static String convertToUnicode2(int value) {
        StringBuilder sb = new StringBuilder();

        if (value <= 0xFFFF) {
            sb.append("$")
                    .append(Integer.toHexString(value).toUpperCase());
        } else if (value <= 0x10FFFF) {
            int highSurrogate = 0xD800 + ((value - 0x10000) >> 10);
            int lowSurrogate = 0xDC00 + ((value - 0x10000) & 0x3FF);
            sb.append("$")
                    .append(Integer.toHexString(highSurrogate).toUpperCase())
                    .append(Integer.toHexString(lowSurrogate).toUpperCase());
        }

        return sb.toString();
    }

    // 提取css字体名称
    public static String extractName(String inputString) {
        int startIndex = inputString.indexOf("#") + 1;
        int endIndex = inputString.indexOf("otltag");
        if (startIndex != -1 && endIndex != -1) {
            return StrUtil.sub(inputString, startIndex, endIndex);
        } else {
            return "";
        }
    }
}
