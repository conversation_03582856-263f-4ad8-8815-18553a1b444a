@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

rem 钓鱼吧应用启动脚本 (Windows版本)
rem 确保日志目录存在并启动应用

set APP_NAME=diaoyuba
set LOG_DIR=C:\log
set JAR_FILE=yudao-server\target\yudao-server.jar
set PROFILE=pro

echo ==========================================
echo 启动 %APP_NAME% 应用
echo ==========================================

rem 检查并创建日志目录
echo 检查日志目录: %LOG_DIR%
if not exist "%LOG_DIR%" (
    echo 创建日志目录: %LOG_DIR%
    mkdir "%LOG_DIR%"
    echo ✅ 日志目录创建成功
) else (
    echo ✅ 日志目录已存在
)

rem 检查 JAR 文件是否存在
if not exist "%JAR_FILE%" (
    echo ❌ JAR 文件不存在: %JAR_FILE%
    echo 请先执行 mvn clean package 构建项目
    pause
    exit /b 1
)

echo ✅ JAR 文件存在: %JAR_FILE%

rem 检查是否已有进程在运行
for /f "tokens=2" %%i in ('tasklist /fi "imagename eq java.exe" /fo table /nh ^| findstr "%JAR_FILE%"') do (
    set EXISTING_PID=%%i
)

if defined EXISTING_PID (
    echo ⚠️ 发现已运行的Java进程
    set /p RESTART="是否停止现有进程并重新启动? (y/n): "
    if /i "!RESTART!"=="y" (
        echo 停止现有Java进程...
        taskkill /f /im java.exe >nul 2>&1
        timeout /t 2 >nul
    ) else (
        echo 取消启动
        pause
        exit /b 0
    )
)

rem 启动应用
echo 启动应用...
echo 配置文件: %PROFILE%
echo 日志目录: %LOG_DIR%
echo 日志文件: %LOG_DIR%\diaoyuba.log
echo 错误日志: %LOG_DIR%\diaoyuba-error.log
echo.

rem 启动 Java 应用
start /b java -jar ^
    -Dspring.profiles.active=%PROFILE% ^
    -Dlogging.file.name=%LOG_DIR%\diaoyuba.log ^
    -Dlogging.file.path=%LOG_DIR% ^
    -Xms512m ^
    -Xmx2g ^
    -XX:+UseG1GC ^
    "%JAR_FILE%"

echo 🚀 应用已启动！

rem 等待几秒钟
echo 等待应用启动...
timeout /t 5 >nul

echo ✅ 应用启动完成！
echo.
echo 📋 应用信息:
echo   配置: %PROFILE%
echo   日志: %LOG_DIR%\diaoyuba.log
echo   错误日志: %LOG_DIR%\diaoyuba-error.log
echo.
echo 📖 查看日志命令:
echo   type %LOG_DIR%\diaoyuba.log
echo   powershell Get-Content %LOG_DIR%\diaoyuba.log -Tail 50 -Wait
echo.
echo 🛑 停止应用:
echo   使用任务管理器或 taskkill /f /im java.exe
echo.

pause
