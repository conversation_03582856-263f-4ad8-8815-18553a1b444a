import{ao as t,__tla as u}from"./index-97fffa0c.js";let i,o,l,s,e,c,n,g=Promise.all([(()=>{try{return u}catch{}})()]).then(async()=>{o=async a=>await t.get({url:"/promotion/seckill-config/page",params:a}),i=async a=>await t.get({url:"/promotion/seckill-config/get?id="+a}),c=async()=>await t.get({url:"/promotion/seckill-config/list-all-simple"}),l=async a=>await t.post({url:"/promotion/seckill-config/create",data:a}),n=async a=>await t.put({url:"/promotion/seckill-config/update",data:a}),e=(a,r)=>{const p={id:a,status:r};return t.put({url:"/promotion/seckill-config/update-status",data:p})},s=async a=>await t.delete({url:"/promotion/seckill-config/delete?id="+a})});export{g as __tla,i as a,o as b,l as c,s as d,e,c as g,n as u};
