import{_ as p,__tla as f}from"./Dialog.vue_vue_type_style_index_0_lang-5af0bc2b.js";import{d as v,r as e,o as h,q as y,w as l,i as n,j as w,a as r,x,H as V,__tla as j}from"./index-97fffa0c.js";import{_ as k,__tla as q}from"./WalletTransactionList.vue_vue_type_script_setup_true_lang-94b1351f.js";let c,C=Promise.all([(()=>{try{return f}catch{}})(),(()=>{try{return j}catch{}})(),(()=>{try{return q}catch{}})()]).then(async()=>{c=v({__name:"WalletForm",setup(F,{expose:m}){const a=e(!1),s=e("");e(!1);const _=e(0);return m({open:async o=>{a.value=!0,s.value="\u94B1\u5305\u4F59\u989D\u660E\u7EC6",_.value=o}}),(o,t)=>{const i=V,d=p;return h(),y(d,{title:r(s),modelValue:r(a),"onUpdate:modelValue":t[1]||(t[1]=u=>x(a)?a.value=u:null),width:"800"},{footer:l(()=>[n(i,{onClick:t[0]||(t[0]=u=>a.value=!1)},{default:l(()=>[w("\u53D6 \u6D88")]),_:1})]),default:l(()=>[n(k,{"wallet-id":r(_)},null,8,["wallet-id"])]),_:1},8,["title","modelValue"])}}})});export{c as _,C as __tla};
