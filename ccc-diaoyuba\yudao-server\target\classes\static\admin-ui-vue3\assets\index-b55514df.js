import{d as u,r as _,A as p,o as l,c as h,i as n,w as y,a as r,B as f,q as d,a3 as v,F as x,L as b,__tla as g}from"./index-97fffa0c.js";import{_ as j,__tla as w}from"./ContentWrap.vue_vue_type_script_setup_true_lang-a574ccef.js";import{_ as A,__tla as k}from"./IFrame.vue_vue_type_script_setup_true_lang-f3d5b5e9.js";import{_ as q,__tla as B}from"./index-b39a19a1.js";import{b as F,__tla as I}from"./index-74e8f36e.js";import{__tla as L}from"./el-card-6c7c099d.js";import"./_plugin-vue_export-helper-1b428a4d.js";let e,P=Promise.all([(()=>{try{return g}catch{}})(),(()=>{try{return w}catch{}})(),(()=>{try{return k}catch{}})(),(()=>{try{return B}catch{}})(),(()=>{try{return I}catch{}})(),(()=>{try{return L}catch{}})()]).then(async()=>{e=u({name:"InfraAdminServer",__name:"index",setup(S){const t=_(!0),s=_("http://localhost:48080/admin/applications");return p(async()=>{try{const a=await F("url.spring-boot-admin");a&&a.length>0&&(s.value=a)}finally{t.value=!1}}),(a,z)=>{const o=q,c=A,i=j,m=b;return l(),h(x,null,[n(o,{title:"\u670D\u52A1\u76D1\u63A7",url:"https://doc.iocoder.cn/server-monitor/"}),n(i,null,{default:y(()=>[r(t)?v("",!0):f((l(),d(c,{key:0,src:r(s)},null,8,["src"])),[[m,r(t)]])]),_:1})],64)}}})});export{P as __tla,e as default};
