import{_ as t,__tla as r}from"./RoleForm.vue_vue_type_script_setup_true_lang-9b5384fe.js";import{__tla as _}from"./index-97fffa0c.js";import{__tla as a}from"./Dialog.vue_vue_type_style_index_0_lang-5af0bc2b.js";import{__tla as l}from"./dict-6a82eb12.js";import"./constants-3933cd3a.js";import{__tla as o}from"./index-a3eb7bf9.js";import{__tla as m}from"./useMessage-18385d4a.js";let c=Promise.all([(()=>{try{return r}catch{}})(),(()=>{try{return _}catch{}})(),(()=>{try{return a}catch{}})(),(()=>{try{return l}catch{}})(),(()=>{try{return o}catch{}})(),(()=>{try{return m}catch{}})()]).then(async()=>{});export{c as __tla,t as default};
