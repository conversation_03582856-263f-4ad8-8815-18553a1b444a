import{d as te,u as re,r as _,f as oe,ax as ue,A as de,o as u,c as i,i as l,w as t,a,F as n,k as y,q as p,a3 as C,P as pe,B as H,a2 as se,j as b,g as _e,M as ie,C as ne,D as ce,G as me,T as ye,_ as ve,H as fe,I as be,K as he,L as ke,__tla as Ve}from"./index-97fffa0c.js";import{_ as Ue,__tla as Ce}from"./index.vue_vue_type_script_setup_true_lang-d31568cf.js";import{E as we,a as Te,b as ge,__tla as xe}from"./el-dropdown-item-1342d280.js";import{_ as Ee,__tla as Ie}from"./ContentWrap.vue_vue_type_script_setup_true_lang-a574ccef.js";import{_ as Pe,__tla as De}from"./OrderDeliveryForm.vue_vue_type_script_setup_true_lang-9aedce92.js";import{_ as Se,__tla as Re}from"./OrderUpdateRemarkForm.vue_vue_type_script_setup_true_lang-1b1b4078.js";import{e as qe,__tla as Ne}from"./index-5f126373.js";import{a as Ae,__tla as Ye}from"./index-55bb84f3.js";import{a as w,D as k,e as Ke,__tla as Oe}from"./dict-6a82eb12.js";import{g as Fe,__tla as Le}from"./index-bdfdf090.js";import{D as T,T as Me}from"./constants-3933cd3a.js";import ze,{__tla as He}from"./OrderTableColumn-cefa677a.js";import{__tla as Xe}from"./index-8d6db4ce.js";import{__tla as je}from"./el-card-6c7c099d.js";import{__tla as Be}from"./Dialog.vue_vue_type_style_index_0_lang-5af0bc2b.js";import{__tla as Ge}from"./useMessage-18385d4a.js";import{__tla as Je}from"./el-image-1637bc2a.js";import{__tla as Qe}from"./el-image-viewer-fddfe81d.js";import{__tla as We}from"./DictTag.vue_vue_type_script_lang-5679ad7b.js";import"./color-a8b4eb58.js";import{__tla as Ze}from"./formatTime-9d54d2c5.js";import{__tla as $e}from"./index-75488397.js";import{__tla as ea}from"./ImageViewer.vue_vue_type_script_setup_true_lang-cdbc76a9.js";import"./_plugin-vue_export-helper-1b428a4d.js";let X,aa=Promise.all([(()=>{try{return Ve}catch{}})(),(()=>{try{return Ce}catch{}})(),(()=>{try{return xe}catch{}})(),(()=>{try{return Ie}catch{}})(),(()=>{try{return De}catch{}})(),(()=>{try{return Re}catch{}})(),(()=>{try{return Ne}catch{}})(),(()=>{try{return Ye}catch{}})(),(()=>{try{return Oe}catch{}})(),(()=>{try{return Le}catch{}})(),(()=>{try{return He}catch{}})(),(()=>{try{return Xe}catch{}})(),(()=>{try{return je}catch{}})(),(()=>{try{return Be}catch{}})(),(()=>{try{return Ge}catch{}})(),(()=>{try{return Je}catch{}})(),(()=>{try{return Qe}catch{}})(),(()=>{try{return We}catch{}})(),(()=>{try{return Ze}catch{}})(),(()=>{try{return $e}catch{}})(),(()=>{try{return ea}catch{}})()]).then(async()=>{let P;P={class:"flex items-center justify-center"},X=te({name:"TradeOrder",__name:"index",setup(la){const{currentRoute:j,push:B}=re(),g=_(!0),D=_(2),x=_([]),S=_(),r=_({pageNo:1,pageSize:10,status:void 0,payChannelCode:void 0,createTime:void 0,terminal:void 0,type:void 0,deliveryType:void 0,logisticsId:void 0,pickUpStoreId:void 0,pickUpVerifyCode:void 0}),h=oe({queryParam:""}),R=_([{value:"no",label:"\u8BA2\u5355\u53F7"},{value:"userId",label:"\u7528\u6237UID"},{value:"userNickname",label:"\u7528\u6237\u6635\u79F0"},{value:"userMobile",label:"\u7528\u6237\u7535\u8BDD"}]),G=c=>{var o;(o=R.value.filter(d=>d.value!==c))==null||o.forEach(d=>{r.value.hasOwnProperty(d.value)&&delete r.value[d.value]})},v=async()=>{g.value=!0;try{const c=await qe(a(r));x.value=c.list,D.value=c.total}finally{g.value=!1}},E=async()=>{r.value.pageNo=1,await v()},J=()=>{var c;(c=S.value)==null||c.resetFields(),r.value={pageNo:1,pageSize:10,status:void 0,payChannelCode:void 0,createTime:void 0,terminal:void 0,type:void 0,deliveryType:void 0,logisticsId:void 0,pickUpStoreId:void 0,pickUpVerifyCode:void 0},E()},q=_(),N=_();ue(()=>j.value,()=>{v()});const I=_([]),A=_([]);return de(async()=>{await v(),I.value=await Ae(),A.value=await Fe()}),(c,o)=>{const d=ie,m=ne,s=ce,Q=me,Y=ye,f=ve,V=fe,W=be,K=Ee,O=we,Z=Te,$=ge,ee=he,ae=Ue,le=ke;return u(),i(n,null,[l(K,null,{default:t(()=>[l(W,{ref_key:"queryFormRef",ref:S,inline:!0,model:a(r),class:"-mb-15px","label-width":"68px"},{default:t(()=>[l(s,{label:"\u8BA2\u5355\u72B6\u6001",prop:"status"},{default:t(()=>[l(m,{modelValue:a(r).status,"onUpdate:modelValue":o[0]||(o[0]=e=>a(r).status=e),class:"!w-280px",clearable:"",placeholder:"\u5168\u90E8"},{default:t(()=>[(u(!0),i(n,null,y(a(w)(a(k).TRADE_ORDER_STATUS),e=>(u(),p(d,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(s,{label:"\u652F\u4ED8\u65B9\u5F0F",prop:"payChannelCode"},{default:t(()=>[l(m,{modelValue:a(r).payChannelCode,"onUpdate:modelValue":o[1]||(o[1]=e=>a(r).payChannelCode=e),class:"!w-280px",clearable:"",placeholder:"\u5168\u90E8"},{default:t(()=>[(u(!0),i(n,null,y(a(Ke)(a(k).PAY_CHANNEL_CODE),e=>(u(),p(d,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(s,{label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime"},{default:t(()=>[l(Q,{modelValue:a(r).createTime,"onUpdate:modelValue":o[2]||(o[2]=e=>a(r).createTime=e),"default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],class:"!w-280px","end-placeholder":"\u81EA\u5B9A\u4E49\u65F6\u95F4","start-placeholder":"\u81EA\u5B9A\u4E49\u65F6\u95F4",type:"daterange","value-format":"YYYY-MM-DD HH:mm:ss"},null,8,["modelValue","default-time"])]),_:1}),l(s,{label:"\u8BA2\u5355\u6765\u6E90",prop:"terminal"},{default:t(()=>[l(m,{modelValue:a(r).terminal,"onUpdate:modelValue":o[3]||(o[3]=e=>a(r).terminal=e),class:"!w-280px",clearable:"",placeholder:"\u5168\u90E8"},{default:t(()=>[(u(!0),i(n,null,y(a(w)(a(k).TERMINAL),e=>(u(),p(d,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(s,{label:"\u8BA2\u5355\u7C7B\u578B",prop:"type"},{default:t(()=>[l(m,{modelValue:a(r).type,"onUpdate:modelValue":o[4]||(o[4]=e=>a(r).type=e),class:"!w-280px",clearable:"",placeholder:"\u5168\u90E8"},{default:t(()=>[(u(!0),i(n,null,y(a(w)(a(k).TRADE_ORDER_TYPE),e=>(u(),p(d,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(s,{label:"\u914D\u9001\u65B9\u5F0F",prop:"deliveryType"},{default:t(()=>[l(m,{modelValue:a(r).deliveryType,"onUpdate:modelValue":o[5]||(o[5]=e=>a(r).deliveryType=e),class:"!w-280px",clearable:"",placeholder:"\u5168\u90E8"},{default:t(()=>[(u(!0),i(n,null,y(a(w)(a(k).TRADE_DELIVERY_TYPE),e=>(u(),p(d,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(r).deliveryType===a(T).EXPRESS.type?(u(),p(s,{key:0,label:"\u5FEB\u9012\u516C\u53F8",prop:"logisticsId"},{default:t(()=>[l(m,{modelValue:a(r).logisticsId,"onUpdate:modelValue":o[6]||(o[6]=e=>a(r).logisticsId=e),class:"!w-280px",clearable:"",placeholder:"\u5168\u90E8"},{default:t(()=>[(u(!0),i(n,null,y(a(A),e=>(u(),p(d,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})):C("",!0),a(r).deliveryType===a(T).PICK_UP.type?(u(),p(s,{key:1,label:"\u81EA\u63D0\u95E8\u5E97",prop:"pickUpStoreId"},{default:t(()=>[l(m,{modelValue:a(r).pickUpStoreId,"onUpdate:modelValue":o[7]||(o[7]=e=>a(r).pickUpStoreId=e),class:"!w-280px",clearable:"",multiple:"",placeholder:"\u5168\u90E8"},{default:t(()=>[(u(!0),i(n,null,y(a(I),e=>(u(),p(d,{key:e.id,label:e.name,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})):C("",!0),a(r).deliveryType===a(T).PICK_UP.type?(u(),p(s,{key:2,label:"\u6838\u9500\u7801",prop:"pickUpVerifyCode"},{default:t(()=>[l(Y,{modelValue:a(r).pickUpVerifyCode,"onUpdate:modelValue":o[8]||(o[8]=e=>a(r).pickUpVerifyCode=e),class:"!w-280px",clearable:"",placeholder:"\u8BF7\u8F93\u5165\u81EA\u63D0\u6838\u9500\u7801",onKeyup:pe(E,["enter"])},null,8,["modelValue","onKeyup"])]),_:1})):C("",!0),l(s,{label:"\u805A\u5408\u641C\u7D22"},{default:t(()=>[H(l(Y,{modelValue:a(r)[a(h).queryParam],"onUpdate:modelValue":o[10]||(o[10]=e=>a(r)[a(h).queryParam]=e),class:"!w-280px",clearable:"",placeholder:"\u8BF7\u8F93\u5165",type:a(h).queryParam==="userId"?"number":"text"},{prepend:t(()=>[l(m,{modelValue:a(h).queryParam,"onUpdate:modelValue":o[9]||(o[9]=e=>a(h).queryParam=e),class:"!w-110px",clearable:"",placeholder:"\u5168\u90E8",onChange:G},{default:t(()=>[(u(!0),i(n,null,y(a(R),e=>(u(),p(d,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1},8,["modelValue","type"]),[[se,!0]])]),_:1}),l(s,null,{default:t(()=>[l(V,{onClick:E},{default:t(()=>[l(f,{class:"mr-5px",icon:"ep:search"}),b(" \u641C\u7D22 ")]),_:1}),l(V,{onClick:J},{default:t(()=>[l(f,{class:"mr-5px",icon:"ep:refresh"}),b(" \u91CD\u7F6E ")]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),l(K,null,{default:t(()=>[H((u(),p(ee,{data:a(x)},{default:t(()=>[l(a(ze),{list:a(x),"pick-up-store-list":a(I)},{default:t(({row:e})=>[_e("div",P,[l(V,{link:"",type:"primary",onClick:F=>{return U=e.id,void B({name:"TradeOrderDetail",params:{id:U}});var U}},{default:t(()=>[l(f,{icon:"ep:notification"}),b(" \u8BE6\u60C5 ")]),_:2},1032,["onClick"]),l($,{onCommand:F=>((U,L)=>{var M,z;switch(U){case"remark":(M=N.value)==null||M.open(L);break;case"delivery":(z=q.value)==null||z.open(L)}})(F,e)},{dropdown:t(()=>[l(Z,null,{default:t(()=>[e.deliveryType===a(T).EXPRESS.type&&e.status===a(Me).UNDELIVERED.status?(u(),p(O,{key:0,command:"delivery"},{default:t(()=>[l(f,{icon:"ep:takeaway-box"}),b(" \u53D1\u8D27 ")]),_:1})):C("",!0),l(O,{command:"remark"},{default:t(()=>[l(f,{icon:"ep:chat-line-square"}),b(" \u5907\u6CE8 ")]),_:1})]),_:2},1024)]),default:t(()=>[l(V,{link:"",type:"primary"},{default:t(()=>[l(f,{icon:"ep:d-arrow-right"}),b(" \u66F4\u591A ")]),_:1})]),_:2},1032,["onCommand"])])]),_:1},8,["list","pick-up-store-list"])]),_:1},8,["data"])),[[le,a(g)]]),l(ae,{limit:a(r).pageSize,"onUpdate:limit":o[11]||(o[11]=e=>a(r).pageSize=e),page:a(r).pageNo,"onUpdate:page":o[12]||(o[12]=e=>a(r).pageNo=e),total:a(D),onPagination:v},null,8,["limit","page","total"])]),_:1}),l(Pe,{ref_key:"deliveryFormRef",ref:q,onSuccess:v},null,512),l(Se,{ref_key:"updateRemarkForm",ref:N,onSuccess:v},null,512)],64)}}})});export{aa as __tla,X as default};
