<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.yudao.module.diaoyuba.dal.mysql.team.TeamMapper">

    <!-- 根据距离查询附近的组队，按距离远近排序 -->
    <select id="selectListByDistance" resultType="cn.iocoder.yudao.module.diaoyuba.dal.dataobject.team.TeamDO">
        SELECT t.*,
               (6371 * acos(cos(radians(#{latitude}))
                          * cos(radians(p.latitude))
                          * cos(radians(p.longitude) - radians(#{longitude}))
                          + sin(radians(#{latitude}))
                          * sin(radians(p.latitude)))) AS distance
        FROM diaoyuba_team t
        LEFT JOIN ccc_fish_position_info p ON t.position_id = p.id
        WHERE t.deleted = 0
          AND p.deleted = 0
          AND p.latitude IS NOT NULL
          AND p.longitude IS NOT NULL
          <if test="status != null and status != ''">
              AND t.status = #{status}
          </if>
        ORDER BY distance ASC, t.departure_time ASC
    </select>

    <!-- 更新组队人数 -->
    <update id="updateCurrentPeople">
        UPDATE diaoyuba_team 
        SET current_people = #{currentPeople},
            update_time = NOW()
        WHERE id = #{id} 
          AND deleted = 0
    </update>

    <!-- 增加查看次数 -->
    <update id="incrementViewCount">
        UPDATE diaoyuba_team 
        SET view_count = view_count + 1,
            update_time = NOW()
        WHERE id = #{id} 
          AND deleted = 0
    </update>

    <!-- 增加加入次数 -->
    <update id="incrementJoinCount">
        UPDATE diaoyuba_team
        SET join_count = join_count + 1,
            update_time = NOW()
        WHERE id = #{id}
          AND deleted = 0
    </update>

    <!-- 批量更新组队状态为已结束 -->
    <update id="updateTeamStatusToEnded">
        UPDATE diaoyuba_team
        SET status = 'ended',
            update_time = NOW()
        WHERE id IN
        <foreach collection="teamIds" item="teamId" open="(" separator="," close=")">
            #{teamId}
        </foreach>
        AND deleted = 0
    </update>

</mapper>
