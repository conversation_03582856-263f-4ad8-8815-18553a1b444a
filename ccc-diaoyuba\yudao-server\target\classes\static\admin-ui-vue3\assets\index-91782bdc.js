import{d as D,l as H,r as h,f as J,A as M,O,o as c,c as N,i as a,w as e,a as r,P as T,j as o,B as m,q as d,t as E,a3 as G,F as Q,T as W,D as X,_ as Y,H as Z,I as $,J as aa,K as ta,L as ea,__tla as la}from"./index-97fffa0c.js";import{_ as ra,__tla as na}from"./index.vue_vue_type_script_setup_true_lang-d31568cf.js";import{_ as ca,__tla as oa}from"./ContentWrap.vue_vue_type_script_setup_true_lang-a574ccef.js";import{_ as sa,__tla as pa}from"./index-b39a19a1.js";import{a as ia,d as _a,b as ua,e as ma,__tla as da}from"./index-f765db10.js";import{_ as fa,__tla as ya}from"./AccountForm.vue_vue_type_script_setup_true_lang-0cc4b34e.js";import{u as ha,__tla as ga}from"./useMessage-18385d4a.js";import{__tla as wa}from"./index-8d6db4ce.js";import{__tla as ka}from"./el-card-6c7c099d.js";import"./_plugin-vue_export-helper-1b428a4d.js";import{__tla as Ca}from"./Dialog.vue_vue_type_style_index_0_lang-5af0bc2b.js";let S,ba=Promise.all([(()=>{try{return la}catch{}})(),(()=>{try{return na}catch{}})(),(()=>{try{return oa}catch{}})(),(()=>{try{return pa}catch{}})(),(()=>{try{return da}catch{}})(),(()=>{try{return ya}catch{}})(),(()=>{try{return ga}catch{}})(),(()=>{try{return wa}catch{}})(),(()=>{try{return ka}catch{}})(),(()=>{try{return Ca}catch{}})()]).then(async()=>{let b;b=["src"],S=D({name:"MpAccount",__name:"index",setup(xa){const _=ha(),{t:z}=H(),g=h(!0),x=h(0),I=h([]),n=J({pageNo:1,pageSize:10,name:null,account:null,appId:null}),q=h(),u=async()=>{g.value=!0;try{const f=await ia(n);I.value=f.list,x.value=f.total}finally{g.value=!1}},w=()=>{n.pageNo=1,u()},F=()=>{q.value.resetFields(),w()},P=h(),U=(f,l)=>{P.value.open(f,l)};return M(()=>{u()}),(f,l)=>{const K=sa,L=W,v=X,k=Y,s=Z,R=$,A=ca,p=aa,V=ta,j=ra,y=O("hasPermi"),B=ea;return c(),N(Q,null,[a(K,{title:"\u516C\u4F17\u53F7\u63A5\u5165",url:"https://doc.iocoder.cn/mp/account/"}),a(A,null,{default:e(()=>[a(R,{class:"-mb-15px",model:r(n),ref_key:"queryFormRef",ref:q,inline:!0,"label-width":"68px"},{default:e(()=>[a(v,{label:"\u540D\u79F0",prop:"name"},{default:e(()=>[a(L,{modelValue:r(n).name,"onUpdate:modelValue":l[0]||(l[0]=t=>r(n).name=t),placeholder:"\u8BF7\u8F93\u5165\u540D\u79F0",clearable:"",onKeyup:T(w,["enter"]),class:"!w-240px"},null,8,["modelValue","onKeyup"])]),_:1}),a(v,null,{default:e(()=>[a(s,{onClick:w},{default:e(()=>[a(k,{icon:"ep:search",class:"mr-5px"}),o("\u641C\u7D22")]),_:1}),a(s,{onClick:F},{default:e(()=>[a(k,{icon:"ep:refresh",class:"mr-5px"}),o("\u91CD\u7F6E")]),_:1}),m((c(),d(s,{type:"primary",onClick:l[1]||(l[1]=t=>U("create"))},{default:e(()=>[a(k,{icon:"ep:plus",class:"mr-5px"}),o(" \u65B0\u589E ")]),_:1})),[[y,["mp:account:create"]]])]),_:1})]),_:1},8,["model"])]),_:1}),a(A,null,{default:e(()=>[m((c(),d(V,{data:r(I)},{default:e(()=>[a(p,{label:"\u540D\u79F0",align:"center",prop:"name"}),a(p,{label:"\u5FAE\u4FE1\u53F7",align:"center",prop:"account",width:"180"}),a(p,{label:"appId",align:"center",prop:"appId",width:"180"}),a(p,{label:"\u670D\u52A1\u5668\u5730\u5740(URL)",align:"center",prop:"appId",width:"360"},{default:e(t=>[o(E("http://\u670D\u52A1\u7AEF\u5730\u5740/mp/open/"+t.row.appId),1)]),_:1}),a(p,{label:"\u4E8C\u7EF4\u7801",align:"center",prop:"qrCodeUrl"},{default:e(t=>[t.row.qrCodeUrl?(c(),N("img",{key:0,src:t.row.qrCodeUrl,alt:"\u4E8C\u7EF4\u7801",style:{display:"inline-block",height:"100px"}},null,8,b)):G("",!0),m((c(),d(s,{link:"",type:"primary",onClick:C=>(async i=>{try{await _.confirm('\u662F\u5426\u786E\u8BA4\u751F\u6210\u516C\u4F17\u53F7\u8D26\u53F7\u7F16\u53F7\u4E3A"'+i.name+'"\u7684\u4E8C\u7EF4\u7801?'),await ua(i.id),_.success("\u751F\u6210\u4E8C\u7EF4\u7801\u6210\u529F"),await u()}catch{}})(t.row)},{default:e(()=>[o(" \u751F\u6210\u4E8C\u7EF4\u7801 ")]),_:2},1032,["onClick"])),[[y,["mp:account:qr-code"]]])]),_:1}),a(p,{label:"\u5907\u6CE8",align:"center",prop:"remark"}),a(p,{label:"\u64CD\u4F5C",align:"center"},{default:e(t=>[m((c(),d(s,{link:"",type:"primary",onClick:C=>U("update",t.row.id)},{default:e(()=>[o(" \u7F16\u8F91 ")]),_:2},1032,["onClick"])),[[y,["mp:account:update"]]]),m((c(),d(s,{link:"",type:"danger",onClick:C=>(async i=>{try{await _.delConfirm(),await _a(i),_.success(z("common.delSuccess")),await u()}catch{}})(t.row.id)},{default:e(()=>[o(" \u5220\u9664 ")]),_:2},1032,["onClick"])),[[y,["mp:account:delete"]]]),m((c(),d(s,{link:"",type:"danger",onClick:C=>(async i=>{try{await _.confirm('\u662F\u5426\u786E\u8BA4\u6E05\u7A7A\u751F\u6210\u516C\u4F17\u53F7\u8D26\u53F7\u7F16\u53F7\u4E3A"'+i.name+'"\u7684 API \u914D\u989D?'),await ma(i.id),_.success("\u6E05\u7A7A API \u914D\u989D\u6210\u529F")}catch{}})(t.row)},{default:e(()=>[o(" \u6E05\u7A7A API \u914D\u989D ")]),_:2},1032,["onClick"])),[[y,["mp:account:clear-quota"]]])]),_:1})]),_:1},8,["data"])),[[B,r(g)]]),a(j,{total:r(x),page:r(n).pageNo,"onUpdate:page":l[2]||(l[2]=t=>r(n).pageNo=t),limit:r(n).pageSize,"onUpdate:limit":l[3]||(l[3]=t=>r(n).pageSize=t),onPagination:u},null,8,["total","page","limit"])]),_:1}),a(fa,{ref_key:"formRef",ref:P,onSuccess:u},null,512)],64)}}})});export{ba as __tla,S as default};
