const n={id:"id",children:"children",pid:"pid"},r={children:"children",label:"name",value:"id",isLeaf:"leaf",emitPath:!1},e=r=>Object.assign({},n,r),t=(n,r={})=>{r=e(r);const{children:t}=r,i=[...n];for(let e=0;e<i.length;e++)i[e][t]&&i.splice(e+1,0,...i[e][t]);return i},i=(n,r,t={})=>{t=e(t);const i=[],c=[...n],o=new Set,{children:s}=t;for(;c.length;){const n=c[0];if(o.has(n))i.pop(),c.shift();else if(o.add(n),n[s]&&c.unshift(...n[s]),i.push(n),r(n))return i}return null},c=(n,r,t={})=>{const i=(t=e(t)).children;return function n(e){return e.map((n=>({...n}))).filter((e=>(e[i]=e[i]&&n(e[i]),r(e)||e[i]&&e[i].length)))}(n)},o=(n,r)=>n.map((n=>s(n,r))),s=(n,{children:r="children",conversion:e})=>{const t=Array.isArray(n[r])&&n[r].length>0,i=e(n)||{};return t?{...i,[r]:n[r].map((n=>s(n,{children:r,conversion:e})))}:{...i}},l=(n,r,e={})=>{n.forEach((n=>{const t=r(n,e)||n;n.children&&l(n.children,r,t)}))},a=(n,r,e,t)=>{if(!Array.isArray(n))return[];const i=r||"id",c=e||"parentId",o=t||"children",s={},l={},a=[];for(const d of n){const n=d[c];null==s[n]&&(s[n]=[]),l[d[i]]=d,s[n].push(d)}for(const d of n){null==l[d[c]]&&a.push(d)}for(const d of a)f(d);function f(n){if(null!==s[n[i]]&&(n[o]=s[n[i]]),n[o])for(const r of n[o])f(r)}return a},f=(n,r,e,t,i)=>{r=r||"id",e=e||"parentId",i=i||Math.min(...n.map((n=>n[e])))||0;const c=JSON.parse(JSON.stringify(n)),o=c.filter((n=>{const t=c.filter((t=>n[r]===t[e]));return t.length>0&&(n.children=t),n[e]===i}));return""!==o?o:n},d=(n,r)=>{if(void 0===n||!Array.isArray(n)||0===n.length)return"";const e=n.find((n=>n.id===r));if(void 0!==e)return e.name;let t="";function i(n){for(const e of n){if(e.id===r)return t+=` / ${e.name}`,!0;if(void 0!==e.children&&0!==e.children.length&&(t+=` / ${e.name}`,i(e.children)))return!0}return!1}for(const c of n)if(t=`${c.name}`,i(c.children))break;return t};export{o as a,f as b,i as c,r as d,l as e,c as f,t as g,a as h,d as t};
