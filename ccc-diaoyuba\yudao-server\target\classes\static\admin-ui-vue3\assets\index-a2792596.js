import{ao as t,__tla as u}from"./index-97fffa0c.js";let e,l,s,p,r,g,m,d=Promise.all([(()=>{try{return u}catch{}})()]).then(async()=>{s=a=>t.post({url:"/mp/tag/create",data:a}),m=a=>t.put({url:"/mp/tag/update",data:a}),p=a=>t.delete({url:"/mp/tag/delete?id="+a}),r=a=>t.get({url:"/mp/tag/get?id="+a}),e=a=>t.get({url:"/mp/tag/page",params:a}),l=()=>t.get({url:"/mp/tag/list-all-simple"}),g=a=>t.post({url:"/mp/tag/sync?accountId="+a})});export{d as __tla,e as a,l as b,s as c,p as d,r as g,g as s,m as u};
