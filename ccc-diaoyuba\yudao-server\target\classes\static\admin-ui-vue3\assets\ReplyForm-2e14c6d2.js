import{_ as t,__tla as r}from"./ReplyForm.vue_vue_type_script_setup_true_lang-4b6c3157.js";import{__tla as _}from"./index-97fffa0c.js";import{__tla as a}from"./main-157d4130.js";import{__tla as l}from"./TabNews-4fdeb460.js";import{__tla as o}from"./main-17919147.js";import{__tla as m}from"./el-image-1637bc2a.js";import{__tla as c}from"./el-image-viewer-fddfe81d.js";import"./_plugin-vue_export-helper-1b428a4d.js";import{__tla as e}from"./main-4dd868b0.js";import{__tla as s}from"./index.vue_vue_type_script_setup_true_lang-d31568cf.js";import{__tla as i}from"./index-8d6db4ce.js";import{__tla as p}from"./main-7292042e.js";import{__tla as n}from"./main.vue_vue_type_script_setup_true_lang-4cb32a33.js";import{__tla as f}from"./index-aa57e946.js";import{__tla as h}from"./index-3b46e2ef.js";import{__tla as u}from"./formatTime-9d54d2c5.js";import{__tla as y}from"./TabText.vue_vue_type_script_setup_true_lang-36a4c895.js";import{__tla as d}from"./TabImage-cf1eaa22.js";import{__tla as x}from"./useUpload-36312237.js";import{__tla as P}from"./useMessage-18385d4a.js";import{__tla as b}from"./TabVoice-7070a31e.js";import{__tla as g}from"./TabVideo-5d569b78.js";import{__tla as j}from"./TabMusic.vue_vue_type_script_setup_true_lang-93761c9f.js";import"./types-5e186e8c.js";import{__tla as k}from"./dict-6a82eb12.js";let q=Promise.all([(()=>{try{return r}catch{}})(),(()=>{try{return _}catch{}})(),(()=>{try{return a}catch{}})(),(()=>{try{return l}catch{}})(),(()=>{try{return o}catch{}})(),(()=>{try{return m}catch{}})(),(()=>{try{return c}catch{}})(),(()=>{try{return e}catch{}})(),(()=>{try{return s}catch{}})(),(()=>{try{return i}catch{}})(),(()=>{try{return p}catch{}})(),(()=>{try{return n}catch{}})(),(()=>{try{return f}catch{}})(),(()=>{try{return h}catch{}})(),(()=>{try{return u}catch{}})(),(()=>{try{return y}catch{}})(),(()=>{try{return d}catch{}})(),(()=>{try{return x}catch{}})(),(()=>{try{return P}catch{}})(),(()=>{try{return b}catch{}})(),(()=>{try{return g}catch{}})(),(()=>{try{return j}catch{}})(),(()=>{try{return k}catch{}})()]).then(async()=>{});export{q as __tla,t as default};
