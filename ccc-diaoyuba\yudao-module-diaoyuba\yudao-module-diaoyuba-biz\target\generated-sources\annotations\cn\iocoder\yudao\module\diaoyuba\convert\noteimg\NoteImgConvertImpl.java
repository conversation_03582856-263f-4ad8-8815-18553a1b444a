package cn.iocoder.yudao.module.diaoyuba.convert.noteimg;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.diaoyuba.controller.app.noteimg.vo.AppNoteImgCreateReqVO;
import cn.iocoder.yudao.module.diaoyuba.controller.app.noteimg.vo.AppNoteImgExcelVO;
import cn.iocoder.yudao.module.diaoyuba.controller.app.noteimg.vo.AppNoteImgRespVO;
import cn.iocoder.yudao.module.diaoyuba.controller.app.noteimg.vo.AppNoteImgUpdateReqVO;
import cn.iocoder.yudao.module.diaoyuba.dal.dataobject.noteimg.NoteImgDO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-21T09:34:05+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_181 (Oracle Corporation)"
)
public class NoteImgConvertImpl implements NoteImgConvert {

    @Override
    public NoteImgDO convert(AppNoteImgCreateReqVO bean) {
        if ( bean == null ) {
            return null;
        }

        NoteImgDO.NoteImgDOBuilder noteImgDO = NoteImgDO.builder();

        noteImgDO.noteId( bean.getNoteId() );
        noteImgDO.imagePath( bean.getImagePath() );
        noteImgDO.copyTime( bean.getCopyTime() );
        noteImgDO.sortNo( bean.getSortNo() );
        noteImgDO.type( bean.getType() );

        return noteImgDO.build();
    }

    @Override
    public NoteImgDO convert(AppNoteImgUpdateReqVO bean) {
        if ( bean == null ) {
            return null;
        }

        NoteImgDO.NoteImgDOBuilder noteImgDO = NoteImgDO.builder();

        noteImgDO.id( bean.getId() );
        noteImgDO.noteId( bean.getNoteId() );
        noteImgDO.imagePath( bean.getImagePath() );
        noteImgDO.copyTime( bean.getCopyTime() );
        noteImgDO.sortNo( bean.getSortNo() );
        noteImgDO.type( bean.getType() );

        return noteImgDO.build();
    }

    @Override
    public AppNoteImgRespVO convert(NoteImgDO bean) {
        if ( bean == null ) {
            return null;
        }

        AppNoteImgRespVO appNoteImgRespVO = new AppNoteImgRespVO();

        appNoteImgRespVO.setNoteId( bean.getNoteId() );
        appNoteImgRespVO.setImagePath( bean.getImagePath() );
        appNoteImgRespVO.setCopyTime( bean.getCopyTime() );
        appNoteImgRespVO.setSortNo( bean.getSortNo() );
        appNoteImgRespVO.setType( bean.getType() );
        appNoteImgRespVO.setId( bean.getId() );
        appNoteImgRespVO.setCreateTime( bean.getCreateTime() );

        return appNoteImgRespVO;
    }

    @Override
    public List<AppNoteImgRespVO> convertList(List<NoteImgDO> list) {
        if ( list == null ) {
            return null;
        }

        List<AppNoteImgRespVO> list1 = new ArrayList<AppNoteImgRespVO>( list.size() );
        for ( NoteImgDO noteImgDO : list ) {
            list1.add( convert( noteImgDO ) );
        }

        return list1;
    }

    @Override
    public PageResult<AppNoteImgRespVO> convertPage(PageResult<NoteImgDO> page) {
        if ( page == null ) {
            return null;
        }

        PageResult<AppNoteImgRespVO> pageResult = new PageResult<AppNoteImgRespVO>();

        pageResult.setList( convertList( page.getList() ) );
        pageResult.setTotal( page.getTotal() );

        return pageResult;
    }

    @Override
    public List<AppNoteImgExcelVO> convertList02(List<NoteImgDO> list) {
        if ( list == null ) {
            return null;
        }

        List<AppNoteImgExcelVO> list1 = new ArrayList<AppNoteImgExcelVO>( list.size() );
        for ( NoteImgDO noteImgDO : list ) {
            list1.add( noteImgDOToAppNoteImgExcelVO( noteImgDO ) );
        }

        return list1;
    }

    protected AppNoteImgExcelVO noteImgDOToAppNoteImgExcelVO(NoteImgDO noteImgDO) {
        if ( noteImgDO == null ) {
            return null;
        }

        AppNoteImgExcelVO appNoteImgExcelVO = new AppNoteImgExcelVO();

        appNoteImgExcelVO.setId( noteImgDO.getId() );
        appNoteImgExcelVO.setNoteId( noteImgDO.getNoteId() );
        appNoteImgExcelVO.setImagePath( noteImgDO.getImagePath() );
        appNoteImgExcelVO.setCopyTime( noteImgDO.getCopyTime() );
        appNoteImgExcelVO.setSortNo( noteImgDO.getSortNo() );
        appNoteImgExcelVO.setCreateTime( noteImgDO.getCreateTime() );
        appNoteImgExcelVO.setType( noteImgDO.getType() );

        return appNoteImgExcelVO;
    }
}
