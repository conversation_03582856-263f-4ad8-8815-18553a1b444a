import{_ as t,__tla as r}from"./TabMusic.vue_vue_type_script_setup_true_lang-93761c9f.js";import{__tla as _}from"./index-97fffa0c.js";import{__tla as a}from"./main-4dd868b0.js";import{__tla as l}from"./index.vue_vue_type_script_setup_true_lang-d31568cf.js";import{__tla as o}from"./index-8d6db4ce.js";import{__tla as m}from"./main-17919147.js";import{__tla as c}from"./el-image-1637bc2a.js";import{__tla as e}from"./el-image-viewer-fddfe81d.js";import"./_plugin-vue_export-helper-1b428a4d.js";import{__tla as s}from"./main-7292042e.js";import{__tla as i}from"./main.vue_vue_type_script_setup_true_lang-4cb32a33.js";import{__tla as n}from"./index-aa57e946.js";import{__tla as p}from"./index-3b46e2ef.js";import{__tla as f}from"./formatTime-9d54d2c5.js";import{__tla as h}from"./useUpload-36312237.js";import{__tla as u}from"./useMessage-18385d4a.js";let y=Promise.all([(()=>{try{return r}catch{}})(),(()=>{try{return _}catch{}})(),(()=>{try{return a}catch{}})(),(()=>{try{return l}catch{}})(),(()=>{try{return o}catch{}})(),(()=>{try{return m}catch{}})(),(()=>{try{return c}catch{}})(),(()=>{try{return e}catch{}})(),(()=>{try{return s}catch{}})(),(()=>{try{return i}catch{}})(),(()=>{try{return n}catch{}})(),(()=>{try{return p}catch{}})(),(()=>{try{return f}catch{}})(),(()=>{try{return h}catch{}})(),(()=>{try{return u}catch{}})()]).then(async()=>{});export{y as __tla,t as default};
