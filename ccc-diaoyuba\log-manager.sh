#!/bin/bash

# 日志管理脚本
# 提供日志查看、清理、监控等功能

LOG_DIR="/log"
APP_LOG="$LOG_DIR/diaoyuba.log"
ERROR_LOG="$LOG_DIR/diaoyuba-error.log"
GC_LOG="$LOG_DIR/gc.log"

# 显示帮助信息
show_help() {
    echo "日志管理脚本 - 钓鱼吧应用"
    echo ""
    echo "用法: $0 [命令] [参数]"
    echo ""
    echo "命令:"
    echo "  tail [n]     - 实时查看日志 (默认最后50行)"
    echo "  error [n]    - 查看错误日志 (默认最后50行)"
    echo "  search <关键词> - 搜索日志内容"
    echo "  size         - 查看日志文件大小"
    echo "  clean        - 清理旧日志文件"
    echo "  archive      - 归档当前日志"
    echo "  status       - 查看日志状态"
    echo "  monitor      - 监控错误日志"
    echo ""
    echo "示例:"
    echo "  $0 tail 100        # 查看最后100行日志"
    echo "  $0 error           # 查看错误日志"
    echo "  $0 search ERROR    # 搜索包含ERROR的日志"
    echo "  $0 clean           # 清理7天前的日志"
}

# 实时查看日志
tail_log() {
    local lines=${1:-50}
    echo "📖 实时查看应用日志 (最后 $lines 行)..."
    echo "按 Ctrl+C 退出"
    echo "----------------------------------------"
    tail -n $lines -f "$APP_LOG"
}

# 查看错误日志
error_log() {
    local lines=${1:-50}
    if [ -f "$ERROR_LOG" ]; then
        echo "🚨 错误日志 (最后 $lines 行)..."
        echo "----------------------------------------"
        tail -n $lines "$ERROR_LOG"
    else
        echo "✅ 没有错误日志文件"
    fi
}

# 搜索日志
search_log() {
    local keyword="$1"
    if [ -z "$keyword" ]; then
        echo "❌ 请提供搜索关键词"
        return 1
    fi
    
    echo "🔍 搜索关键词: $keyword"
    echo "----------------------------------------"
    grep -n --color=always "$keyword" "$APP_LOG" | tail -20
}

# 查看日志文件大小
log_size() {
    echo "📊 日志文件大小统计:"
    echo "----------------------------------------"
    if [ -f "$APP_LOG" ]; then
        echo "应用日志: $(du -h "$APP_LOG" | cut -f1)"
    fi
    if [ -f "$ERROR_LOG" ]; then
        echo "错误日志: $(du -h "$ERROR_LOG" | cut -f1)"
    fi
    if [ -f "$GC_LOG" ]; then
        echo "GC日志:   $(du -h "$GC_LOG" | cut -f1)"
    fi
    
    echo ""
    echo "日志目录总大小: $(du -sh "$LOG_DIR" | cut -f1)"
    echo "日志文件数量: $(find "$LOG_DIR" -name "*.log*" | wc -l)"
}

# 清理旧日志
clean_logs() {
    echo "🧹 清理7天前的日志文件..."
    
    # 清理压缩的日志文件
    find "$LOG_DIR" -name "*.log.gz" -mtime +7 -type f -exec rm -f {} \;
    
    # 清理旧的GC日志
    find "$LOG_DIR" -name "gc.log.*" -mtime +7 -type f -exec rm -f {} \;
    
    echo "✅ 清理完成"
    log_size
}

# 归档当前日志
archive_logs() {
    local timestamp=$(date +"%Y%m%d_%H%M%S")
    local archive_dir="$LOG_DIR/archive"
    
    echo "📦 归档当前日志..."
    
    # 创建归档目录
    mkdir -p "$archive_dir"
    
    # 归档应用日志
    if [ -f "$APP_LOG" ]; then
        cp "$APP_LOG" "$archive_dir/diaoyuba_$timestamp.log"
        echo "✅ 应用日志已归档: $archive_dir/diaoyuba_$timestamp.log"
    fi
    
    # 归档错误日志
    if [ -f "$ERROR_LOG" ]; then
        cp "$ERROR_LOG" "$archive_dir/diaoyuba-error_$timestamp.log"
        echo "✅ 错误日志已归档: $archive_dir/diaoyuba-error_$timestamp.log"
    fi
    
    # 压缩归档文件
    cd "$archive_dir"
    tar -czf "logs_$timestamp.tar.gz" *_$timestamp.log
    rm -f *_$timestamp.log
    
    echo "✅ 归档完成: $archive_dir/logs_$timestamp.tar.gz"
}

# 查看日志状态
log_status() {
    echo "📋 日志状态报告:"
    echo "----------------------------------------"
    
    # 检查日志文件是否存在
    if [ -f "$APP_LOG" ]; then
        echo "✅ 应用日志: 存在"
        echo "   最后修改: $(stat -c %y "$APP_LOG")"
        echo "   文件大小: $(du -h "$APP_LOG" | cut -f1)"
    else
        echo "❌ 应用日志: 不存在"
    fi
    
    if [ -f "$ERROR_LOG" ]; then
        echo "⚠️ 错误日志: 存在"
        echo "   最后修改: $(stat -c %y "$ERROR_LOG")"
        echo "   文件大小: $(du -h "$ERROR_LOG" | cut -f1)"
        echo "   错误数量: $(wc -l < "$ERROR_LOG")"
    else
        echo "✅ 错误日志: 不存在 (无错误)"
    fi
    
    # 检查应用是否在运行
    if pgrep -f "yudao-server.jar" > /dev/null; then
        echo "✅ 应用状态: 运行中"
    else
        echo "❌ 应用状态: 未运行"
    fi
}

# 监控错误日志
monitor_errors() {
    echo "🔍 监控错误日志..."
    echo "按 Ctrl+C 退出"
    echo "----------------------------------------"
    
    if [ -f "$ERROR_LOG" ]; then
        tail -f "$ERROR_LOG"
    else
        echo "✅ 暂无错误日志，等待错误发生..."
        # 等待错误日志文件创建
        while [ ! -f "$ERROR_LOG" ]; do
            sleep 1
        done
        tail -f "$ERROR_LOG"
    fi
}

# 主程序
case "$1" in
    "tail")
        tail_log "$2"
        ;;
    "error")
        error_log "$2"
        ;;
    "search")
        search_log "$2"
        ;;
    "size")
        log_size
        ;;
    "clean")
        clean_logs
        ;;
    "archive")
        archive_logs
        ;;
    "status")
        log_status
        ;;
    "monitor")
        monitor_errors
        ;;
    "help"|"-h"|"--help"|"")
        show_help
        ;;
    *)
        echo "❌ 未知命令: $1"
        echo "使用 $0 help 查看帮助"
        exit 1
        ;;
esac
