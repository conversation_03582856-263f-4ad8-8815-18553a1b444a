import{_ as r,__tla as s}from"./ContentWrap.vue_vue_type_script_setup_true_lang-a574ccef.js";import{_ as e,__tla as l}from"./IFrame.vue_vue_type_script_setup_true_lang-f3d5b5e9.js";import{d as o,o as n,q as c,w as m,i as u,__tla as i}from"./index-97fffa0c.js";import{__tla as p}from"./el-card-6c7c099d.js";let t,f=Promise.all([(()=>{try{return s}catch{}})(),(()=>{try{return l}catch{}})(),(()=>{try{return i}catch{}})(),(()=>{try{return p}catch{}})()]).then(async()=>{t=o({name:"GoView",__name:"index",setup:h=>(y,d)=>{const a=e,_=r;return n(),c(_,null,{default:m(()=>[u(a,{src:"http://127.0.0.1:3000"})]),_:1})}})});export{f as __tla,t as default};
