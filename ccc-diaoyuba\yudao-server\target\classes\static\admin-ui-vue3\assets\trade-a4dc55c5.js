import{ao as a,__tla as _}from"./index-97fffa0c.js";import{f as r,__tla as p}from"./formatTime-9d54d2c5.js";let e,i,c,l,m,o,d,g=Promise.all([(()=>{try{return _}catch{}})(),(()=>{try{return p}catch{}})()]).then(async()=>{let s;c=()=>a.get({url:"/statistics/trade/summary"}),l=t=>a.get({url:"/statistics/trade/trend/summary",params:s(t)}),m=t=>a.get({url:"/statistics/trade/list",params:s(t)}),o=t=>a.download({url:"/statistics/trade/export-excel",params:s(t)}),d=async()=>await a.get({url:"/statistics/trade/order-count"}),i=async()=>await a.get({url:"/statistics/trade/order-comparison"}),e=(t,n,u)=>a.get({url:"/statistics/trade/order-count-trend",params:{type:t,beginTime:r(n),endTime:r(u)}}),s=t=>({times:[r(t.times[0]),r(t.times[1])]})});export{g as __tla,e as a,i as b,c,l as d,m as e,o as f,d as g};
