import{u as x,_ as M,a as w,__tla as U}from"./useTable-d34691cf.js";import{_ as j,__tla as q}from"./ContentWrap.vue_vue_type_script_setup_true_lang-a574ccef.js";import{d as O,r as C,A as B,O as D,o as c,c as F,i as _,w as e,a as t,B as n,q as i,j as u,F as H,_ as E,H as G,__tla as I}from"./index-97fffa0c.js";import{_ as J,__tla as K}from"./index-b39a19a1.js";import{a as v,__tla as N}from"./account.data-d3aa5efe.js";import{a as Q,d as T,__tla as V}from"./index-3c0a595a.js";import{_ as W,__tla as X}from"./MailAccountForm.vue_vue_type_script_setup_true_lang-825998eb.js";import{_ as Y,__tla as Z}from"./MailAccountDetail.vue_vue_type_script_setup_true_lang-60440c1f.js";import{__tla as $}from"./Form-abbdb81e.js";import{__tla as tt}from"./el-virtual-list-404af680.js";import{__tla as at}from"./el-tree-select-9cc5ed33.js";import{__tla as rt}from"./el-time-select-a903a952.js";import{__tla as _t}from"./InputPassword-8eb3866f.js";import"./_plugin-vue_export-helper-1b428a4d.js";import{__tla as et}from"./style.css_vue_type_style_index_0_src_true_lang-2cb747d4.js";import{__tla as lt}from"./UploadImg-33a9d58c.js";import{__tla as ot}from"./el-image-viewer-fddfe81d.js";import{__tla as ct}from"./useMessage-18385d4a.js";import{__tla as st}from"./UploadImgs-985b4279.js";import{__tla as mt}from"./UploadImgs.vue_vue_type_style_index_0_scoped_ccffae08_lang-64d084ff.js";import{__tla as nt}from"./UploadFile.vue_vue_type_style_index_0_scoped_73fc17ef_lang-cc46e8f9.js";import{__tla as it}from"./index-8d6db4ce.js";import{__tla as ut}from"./useForm-66271e88.js";import"./download-20922b56.js";import{__tla as pt}from"./el-card-6c7c099d.js";import{__tla as yt}from"./formatTime-9d54d2c5.js";import{__tla as ft}from"./formRules-8010a921.js";import{__tla as ht}from"./dict-6a82eb12.js";import{__tla as dt}from"./useCrudSchemas-6394b852.js";import"./tree-ebab458e.js";import{__tla as gt}from"./DictTag.vue_vue_type_script_lang-5679ad7b.js";import"./color-a8b4eb58.js";import{__tla as St}from"./Dialog.vue_vue_type_style_index_0_lang-5af0bc2b.js";import{__tla as kt}from"./Descriptions-13090c1a.js";import{__tla as Pt}from"./Descriptions.vue_vue_type_style_index_0_scoped_76061901_lang-e5833478.js";import{__tla as Ct}from"./el-descriptions-item-5b1e935d.js";let b,vt=Promise.all([(()=>{try{return U}catch{}})(),(()=>{try{return q}catch{}})(),(()=>{try{return I}catch{}})(),(()=>{try{return K}catch{}})(),(()=>{try{return N}catch{}})(),(()=>{try{return V}catch{}})(),(()=>{try{return X}catch{}})(),(()=>{try{return Z}catch{}})(),(()=>{try{return $}catch{}})(),(()=>{try{return tt}catch{}})(),(()=>{try{return at}catch{}})(),(()=>{try{return rt}catch{}})(),(()=>{try{return _t}catch{}})(),(()=>{try{return et}catch{}})(),(()=>{try{return lt}catch{}})(),(()=>{try{return ot}catch{}})(),(()=>{try{return ct}catch{}})(),(()=>{try{return st}catch{}})(),(()=>{try{return mt}catch{}})(),(()=>{try{return nt}catch{}})(),(()=>{try{return it}catch{}})(),(()=>{try{return ut}catch{}})(),(()=>{try{return pt}catch{}})(),(()=>{try{return yt}catch{}})(),(()=>{try{return ft}catch{}})(),(()=>{try{return ht}catch{}})(),(()=>{try{return dt}catch{}})(),(()=>{try{return gt}catch{}})(),(()=>{try{return St}catch{}})(),(()=>{try{return kt}catch{}})(),(()=>{try{return Pt}catch{}})(),(()=>{try{return Ct}catch{}})()]).then(async()=>{b=O({name:"SystemMailAccount",__name:"index",setup(bt){const{tableObject:l,tableMethods:p}=x({getListApi:Q,delListApi:T}),{getList:y,setSearchParams:f}=p,h=C(),d=(S,a)=>{h.value.open(S,a)},g=C();return B(()=>{y()}),(S,a)=>{const z=J,L=E,s=G,A=M,k=j,R=w,m=D("hasPermi");return c(),F(H,null,[_(z,{title:"\u90AE\u4EF6\u914D\u7F6E",url:"https://doc.iocoder.cn/mail"}),_(k,null,{default:e(()=>[_(A,{schema:t(v).searchSchema,onSearch:t(f),onReset:t(f)},{actionMore:e(()=>[n((c(),i(s,{type:"primary",plain:"",onClick:a[0]||(a[0]=r=>d("create"))},{default:e(()=>[_(L,{icon:"ep:plus",class:"mr-5px"}),u(" \u65B0\u589E ")]),_:1})),[[m,["system:mail-account:create"]]])]),_:1},8,["schema","onSearch","onReset"])]),_:1}),_(k,null,{default:e(()=>[_(R,{columns:t(v).tableColumns,data:t(l).tableList,loading:t(l).loading,pagination:{total:t(l).total},pageSize:t(l).pageSize,"onUpdate:pageSize":a[1]||(a[1]=r=>t(l).pageSize=r),currentPage:t(l).currentPage,"onUpdate:currentPage":a[2]||(a[2]=r=>t(l).currentPage=r)},{action:e(({row:r})=>[n((c(),i(s,{link:"",type:"primary",onClick:P=>d("update",r.id)},{default:e(()=>[u(" \u7F16\u8F91 ")]),_:2},1032,["onClick"])),[[m,["system:mail-account:update"]]]),n((c(),i(s,{link:"",type:"primary",onClick:P=>{return o=r.id,void g.value.open(o);var o}},{default:e(()=>[u(" \u8BE6\u60C5 ")]),_:2},1032,["onClick"])),[[m,["system:mail-account:query"]]]),n((c(),i(s,{link:"",type:"danger",onClick:P=>{return o=r.id,void p.delList(o,!1);var o}},{default:e(()=>[u(" \u5220\u9664 ")]),_:2},1032,["onClick"])),[[m,["system:mail-account:delete"]]])]),_:1},8,["columns","data","loading","pagination","pageSize","currentPage"])]),_:1}),_(W,{ref_key:"formRef",ref:h,onSuccess:t(y)},null,8,["onSuccess"]),_(Y,{ref_key:"detailRef",ref:g},null,512)],64)}}})});export{vt as __tla,b as default};
