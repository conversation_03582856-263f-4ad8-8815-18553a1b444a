package cn.iocoder.yudao.module.diaoyuba.convert.team;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.diaoyuba.controller.app.team.vo.AppTeamCreateReqVO;
import cn.iocoder.yudao.module.diaoyuba.controller.app.team.vo.AppTeamRespVO;
import cn.iocoder.yudao.module.diaoyuba.dal.dataobject.team.TeamDO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-21T09:34:05+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_181 (Oracle Corporation)"
)
public class TeamConvertImpl implements TeamConvert {

    @Override
    public TeamDO convert(AppTeamCreateReqVO bean) {
        if ( bean == null ) {
            return null;
        }

        TeamDO.TeamDOBuilder teamDO = TeamDO.builder();

        teamDO.id( bean.getId() );
        teamDO.title( bean.getTitle() );
        teamDO.description( bean.getDescription() );
        teamDO.positionId( bean.getPositionId() );
        teamDO.departureLocation( bean.getDepartureLocation() );
        teamDO.departureTime( bean.getDepartureTime() );
        teamDO.endTime( bean.getEndTime() );
        teamDO.minPeople( bean.getMinPeople() );
        teamDO.maxPeople( bean.getMaxPeople() );
        teamDO.costType( bean.getCostType() );
        teamDO.costDescription( bean.getCostDescription() );
        List<String> list = bean.getEquipment();
        if ( list != null ) {
            teamDO.equipment( new ArrayList<String>( list ) );
        }
        teamDO.requirements( bean.getRequirements() );
        teamDO.contactPhone( bean.getContactPhone() );
        teamDO.wechatQRCode( bean.getWechatQRCode() );
        teamDO.showPhone( bean.getShowPhone() );
        teamDO.canPickup( bean.getCanPickup() );

        return teamDO.build();
    }

    @Override
    public AppTeamRespVO convert(TeamDO bean) {
        if ( bean == null ) {
            return null;
        }

        AppTeamRespVO appTeamRespVO = new AppTeamRespVO();

        appTeamRespVO.setId( bean.getId() );
        appTeamRespVO.setTitle( bean.getTitle() );
        appTeamRespVO.setDescription( bean.getDescription() );
        appTeamRespVO.setPositionId( bean.getPositionId() );
        appTeamRespVO.setLeaderId( bean.getLeaderId() );
        appTeamRespVO.setDepartureLocation( bean.getDepartureLocation() );
        appTeamRespVO.setDepartureTime( bean.getDepartureTime() );
        appTeamRespVO.setEndTime( bean.getEndTime() );
        appTeamRespVO.setMinPeople( bean.getMinPeople() );
        appTeamRespVO.setMaxPeople( bean.getMaxPeople() );
        appTeamRespVO.setCurrentPeople( bean.getCurrentPeople() );
        appTeamRespVO.setStatus( bean.getStatus() );
        appTeamRespVO.setCostType( bean.getCostType() );
        appTeamRespVO.setCostDescription( bean.getCostDescription() );
        List<String> list = bean.getEquipment();
        if ( list != null ) {
            appTeamRespVO.setEquipment( new ArrayList<String>( list ) );
        }
        appTeamRespVO.setRequirements( bean.getRequirements() );
        appTeamRespVO.setContactPhone( bean.getContactPhone() );
        appTeamRespVO.setWechatQRCode( bean.getWechatQRCode() );
        appTeamRespVO.setShowPhone( bean.getShowPhone() );
        appTeamRespVO.setCanPickup( bean.getCanPickup() );
        appTeamRespVO.setShareCode( bean.getShareCode() );
        appTeamRespVO.setViewCount( bean.getViewCount() );
        appTeamRespVO.setJoinCount( bean.getJoinCount() );
        appTeamRespVO.setCreateTime( bean.getCreateTime() );
        appTeamRespVO.setUpdateTime( bean.getUpdateTime() );

        return appTeamRespVO;
    }

    @Override
    public List<AppTeamRespVO> convertList(List<TeamDO> list) {
        if ( list == null ) {
            return null;
        }

        List<AppTeamRespVO> list1 = new ArrayList<AppTeamRespVO>( list.size() );
        for ( TeamDO teamDO : list ) {
            list1.add( convert( teamDO ) );
        }

        return list1;
    }

    @Override
    public PageResult<AppTeamRespVO> convertPage(PageResult<TeamDO> page) {
        if ( page == null ) {
            return null;
        }

        PageResult<AppTeamRespVO> pageResult = new PageResult<AppTeamRespVO>();

        pageResult.setList( convertList( page.getList() ) );
        pageResult.setTotal( page.getTotal() );

        return pageResult;
    }
}
