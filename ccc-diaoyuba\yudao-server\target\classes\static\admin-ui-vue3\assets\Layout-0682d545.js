import{cb as bl,r as _,cz as So,A as we,dS as je,dv as Po,d as j,aN as ue,b as f,o as M,q as ee,w as $,a as e,c as N,au as xl,V as A,bk as wl,aP as pe,i as o,aO as Xe,dT as Bo,a3 as Ie,Y as yl,aQ as $e,aR as wt,aL as Re,aM as Ue,bH as Qe,aS as Je,ak as et,bl as He,g as h,aG as _l,t as B,aW as tt,bq as fe,dU as lt,ah as Lo,dV as at,dW as yt,dX as zo,dY as Ao,dn as Oo,cU as Eo,ct as jo,f as ot,ax as re,aI as Cl,h as te,a$ as Se,b8 as kl,df as Ro,B as Ne,a2 as Uo,F as le,dZ as Ml,c2 as Ho,aF as No,bB as Fo,cI as qo,d_ as Do,bn as Wo,bv as Tl,al as _t,aU as Go,ai as nt,j as ce,Z as X,p as st,k as Fe,_ as ie,U as ne,x as Y,l as ye,d$ as qe,bN as Yo,T as Zo,$ as Vl,a0 as Il,e0 as $l,e1 as Ko,dA as Xo,aw as Sl,dD as Pl,dE as rt,m as Qo,H as Jo,e2 as it,e3 as ke,u as Pe,a1 as ut,ck as Me,a_ as Bl,ar as Ct,e4 as Ll,e5 as en,e6 as tn,y as ln,z as an,dR as on,e as nn,bY as sn,e7 as rn,X as un,O as ct,__tla as cn}from"./index-97fffa0c.js";import{E as dn,__tla as pn}from"./el-drawer-0535e62a.js";import{c as zl,l as Al,h as Ol}from"./color-a8b4eb58.js";import{T as mn,_ as vn,__tla as hn}from"./LocaleDropdown.vue_vue_type_script_setup_true_lang-742a53f5.js";import{_ as ge}from"./_plugin-vue_export-helper-1b428a4d.js";import{u as fn,a as El}from"./avatar-f3058573.js";import{c as gn,f as bn,g as xn}from"./tree-ebab458e.js";import{u as kt,__tla as wn}from"./tagsView-c5b6677c.js";import{E as Mt,a as Tt,b as Vt,__tla as yn}from"./el-dropdown-item-1342d280.js";import{_ as _n}from"./logo-13933b22.js";import{_ as Cn,__tla as kn}from"./XButton-dd4d8780.js";import{f as Mn,__tla as Tn}from"./formatTime-9d54d2c5.js";import{c as Vn,d as In,__tla as $n}from"./index-a57e1380.js";import{E as Sn,__tla as Pn}from"./el-avatar-c773bffa.js";import{__tla as Bn}from"./useIcon-4b1d730a.js";let jl,Ln=Promise.all([(()=>{try{return cn}catch{}})(),(()=>{try{return pn}catch{}})(),(()=>{try{return hn}catch{}})(),(()=>{try{return wn}catch{}})(),(()=>{try{return yn}catch{}})(),(()=>{try{return kn}catch{}})(),(()=>{try{return Tn}catch{}})(),(()=>{try{return $n}catch{}})(),(()=>{try{return Pn}catch{}})(),(()=>{try{return Bn}catch{}})()]).then(async()=>{const Rl={visibilityHeight:{type:Number,default:200},target:{type:String,default:""},right:{type:Number,default:40},bottom:{type:Number,default:40}},Ul={click:t=>t instanceof MouseEvent},It="ElBacktop",Hl=j({name:It}),Nl=wt($e(j({...Hl,props:Rl,emits:Ul,setup(t,{emit:l}){const a=t,n=ue("backtop"),{handleClick:s,visible:r}=((c,p,m)=>{const d=bl(),v=bl(),b=_(!1),V=()=>{d.value&&(b.value=d.value.scrollTop>=c.visibilityHeight)},I=Po(V,300,!0);return So(v,"scroll",I),we(()=>{var u;v.value=document,d.value=document.documentElement,c.target&&(d.value=(u=document.querySelector(c.target))!=null?u:void 0,d.value||je(m,`target does not exist: ${c.target}`),v.value=d.value),V()}),{visible:b,handleClick:u=>{var g;(g=d.value)==null||g.scrollTo({top:0,behavior:"smooth"}),p("click",u)}}})(a,l,It),i=f(()=>({right:`${a.right}px`,bottom:`${a.bottom}px`}));return(c,p)=>(M(),ee(yl,{name:`${e(n).namespace.value}-fade-in`},{default:$(()=>[e(r)?(M(),N("div",{key:0,style:xl(e(i)),class:A(e(n).b()),onClick:p[0]||(p[0]=wl((...m)=>e(s)&&e(s)(...m),["stop"]))},[pe(c.$slots,"default",{},()=>[o(e(Xe),{class:A(e(n).e("icon"))},{default:$(()=>[o(e(Bo))]),_:1},8,["class"])])],6)):Ie("v-if",!0)]),_:3},8,["name"]))}}),[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/backtop/src/backtop.vue"]])),$t=Symbol("breadcrumbKey"),Fl=Re({separator:{type:String,default:"/"},separatorIcon:{type:Ue}}),ql=j({name:"ElBreadcrumb"}),Dl=j({...ql,props:Fl,setup(t){const l=t,a=ue("breadcrumb"),n=_();return Qe($t,l),we(()=>{const s=n.value.querySelectorAll(`.${a.e("item")}`);s.length&&s[s.length-1].setAttribute("aria-current","page")}),(s,r)=>(M(),N("div",{ref_key:"breadcrumb",ref:n,class:A(e(a).b()),"aria-label":"Breadcrumb",role:"navigation"},[pe(s.$slots,"default")],2))}});var Wl=$e(Dl,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/breadcrumb/src/breadcrumb.vue"]]);const Gl=Re({to:{type:Je([String,Object]),default:""},replace:{type:Boolean,default:!1}}),Yl=j({name:"ElBreadcrumbItem"});var St=$e(j({...Yl,props:Gl,setup(t){const l=t,a=et(),n=He($t,void 0),s=ue("breadcrumb"),r=a.appContext.config.globalProperties.$router,i=_(),c=()=>{l.to&&r&&(l.replace?r.replace(l.to):r.push(l.to))};return(p,m)=>{var d,v;return M(),N("span",{class:A(e(s).e("item"))},[h("span",{ref_key:"link",ref:i,class:A([e(s).e("inner"),e(s).is("link",!!p.to)]),role:"link",onClick:c},[pe(p.$slots,"default")],2),(d=e(n))!=null&&d.separatorIcon?(M(),ee(e(Xe),{key:0,class:A(e(s).e("separator"))},{default:$(()=>[(M(),ee(_l(e(n).separatorIcon)))]),_:1},8,["class"])):(M(),N("span",{key:1,class:A(e(s).e("separator")),role:"presentation"},B((v=e(n))==null?void 0:v.separator),3))],2)}}}),[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/breadcrumb/src/breadcrumb-item.vue"]]);const Zl=wt(Wl,{BreadcrumbItem:St}),Kl=tt(St);let Xl=class{constructor(t,l){this.parent=t,this.domNode=l,this.subIndex=0,this.subIndex=0,this.init()}init(){this.subMenuItems=this.domNode.querySelectorAll("li"),this.addListeners()}gotoSubIndex(t){t===this.subMenuItems.length?t=0:t<0&&(t=this.subMenuItems.length-1),this.subMenuItems[t].focus(),this.subIndex=t}addListeners(){const t=this.parent.domNode;Array.prototype.forEach.call(this.subMenuItems,l=>{l.addEventListener("keydown",a=>{let n=!1;switch(a.code){case fe.down:this.gotoSubIndex(this.subIndex+1),n=!0;break;case fe.up:this.gotoSubIndex(this.subIndex-1),n=!0;break;case fe.tab:lt(t,"mouseleave");break;case fe.enter:case fe.space:n=!0,a.currentTarget.click()}return n&&(a.preventDefault(),a.stopPropagation()),!1})})}},Ql=class{constructor(t,l){this.domNode=t,this.submenu=null,this.submenu=null,this.init(l)}init(t){this.domNode.setAttribute("tabindex","0");const l=this.domNode.querySelector(`.${t}-menu`);l&&(this.submenu=new Xl(this,l)),this.addListeners()}addListeners(){this.domNode.addEventListener("keydown",t=>{let l=!1;switch(t.code){case fe.down:lt(t.currentTarget,"mouseenter"),this.submenu&&this.submenu.gotoSubIndex(0),l=!0;break;case fe.up:lt(t.currentTarget,"mouseenter"),this.submenu&&this.submenu.gotoSubIndex(this.submenu.subMenuItems.length-1),l=!0;break;case fe.tab:lt(t.currentTarget,"mouseleave");break;case fe.enter:case fe.space:l=!0,t.currentTarget.click()}l&&t.preventDefault()})}},Jl=class{constructor(t,l){this.domNode=t,this.init(l)}init(t){const l=this.domNode.childNodes;Array.from(l).forEach(a=>{a.nodeType===1&&new Ql(a,t)})}};var ea=$e(j({name:"ElMenuCollapseTransition",setup(){const t=ue("menu");return{listeners:{onBeforeEnter:l=>l.style.opacity="0.2",onEnter(l,a){at(l,`${t.namespace.value}-opacity-transition`),l.style.opacity="1",a()},onAfterEnter(l){yt(l,`${t.namespace.value}-opacity-transition`),l.style.opacity=""},onBeforeLeave(l){l.dataset||(l.dataset={}),zo(l,t.m("collapse"))?(yt(l,t.m("collapse")),l.dataset.oldOverflow=l.style.overflow,l.dataset.scrollWidth=l.clientWidth.toString(),at(l,t.m("collapse"))):(at(l,t.m("collapse")),l.dataset.oldOverflow=l.style.overflow,l.dataset.scrollWidth=l.clientWidth.toString(),yt(l,t.m("collapse"))),l.style.width=`${l.scrollWidth}px`,l.style.overflow="hidden"},onLeave(l){at(l,"horizontal-collapse-transition"),l.style.width=`${l.dataset.scrollWidth}px`}}}}}),[["render",function(t,l,a,n,s,r){return M(),ee(yl,Lo({mode:"out-in"},t.listeners),{default:$(()=>[pe(t.$slots,"default")]),_:3},16)}],["__file","/home/<USER>/work/element-plus/element-plus/packages/components/menu/src/menu-collapse-transition.vue"]]);function Pt(t,l){const a=f(()=>{let n=t.parent;const s=[l.value];for(;n.type.name!=="ElMenu";)n.props.index&&s.unshift(n.props.index),n=n.parent;return s});return{parentMenu:f(()=>{let n=t.parent;for(;n&&!["ElMenu","ElSubMenu"].includes(n.type.name);)n=n.parent;return n}),indexPath:a}}function ta(t){return f(()=>{const l=t.backgroundColor;return l?new Ao(l).shade(20).toString():""})}const Bt=(t,l)=>{const a=ue("menu");return f(()=>a.cssVarBlock({"text-color":t.textColor||"","hover-text-color":t.textColor||"","bg-color":t.backgroundColor||"","hover-bg-color":ta(t).value||"","active-color":t.activeTextColor||"",level:`${l}`}))},la=Re({index:{type:String,required:!0},showTimeout:{type:Number,default:300},hideTimeout:{type:Number,default:300},popperClass:String,disabled:Boolean,popperAppendToBody:{type:Boolean,default:void 0},teleported:{type:Boolean,default:void 0},popperOffset:{type:Number,default:6},expandCloseIcon:{type:Ue},expandOpenIcon:{type:Ue},collapseCloseIcon:{type:Ue},collapseOpenIcon:{type:Ue}}),De="ElSubMenu";var dt=j({name:De,props:la,setup(t,{slots:l,expose:a}){Oo({from:"popper-append-to-body",replacement:"teleported",scope:De,version:"2.3.0",ref:"https://element-plus.org/en-US/component/menu.html#submenu-attributes"},f(()=>t.popperAppendToBody!==void 0));const n=et(),{indexPath:s,parentMenu:r}=Pt(n,f(()=>t.index)),i=ue("menu"),c=ue("sub-menu"),p=He("rootMenu");p||je(De,"can not inject root menu");const m=He(`subMenu:${r.value.uid}`);m||je(De,"can not inject sub menu");const d=_({}),v=_({});let b;const V=_(!1),I=_(),u=_(null),g=f(()=>y.value==="horizontal"&&C.value?"bottom-start":"right-start"),k=f(()=>y.value==="horizontal"&&C.value||y.value==="vertical"&&!p.props.collapse?t.expandCloseIcon&&t.expandOpenIcon?K.value?t.expandOpenIcon:t.expandCloseIcon:Eo:t.collapseCloseIcon&&t.collapseOpenIcon?K.value?t.collapseOpenIcon:t.collapseCloseIcon:jo),C=f(()=>m.level===0),F=f(()=>{var E;const R=(E=t.teleported)!=null?E:t.popperAppendToBody;return R===void 0?C.value:R}),Z=f(()=>p.props.collapse?`${i.namespace.value}-zoom-in-left`:`${i.namespace.value}-zoom-in-top`),U=f(()=>y.value==="horizontal"&&C.value?["bottom-start","bottom-end","top-start","top-end","right-start","left-start"]:["right-start","right","right-end","left-start","bottom-start","bottom-end","top-start","top-end"]),K=f(()=>p.openedMenus.includes(t.index)),x=f(()=>{let E=!1;return Object.values(d.value).forEach(R=>{R.active&&(E=!0)}),Object.values(v.value).forEach(R=>{R.active&&(E=!0)}),E}),y=f(()=>p.props.mode),S=ot({index:t.index,indexPath:s,active:x}),H=Bt(p.props,m.level+1),D=E=>{var R,w,O;E||(O=(w=(R=u.value)==null?void 0:R.popperRef)==null?void 0:w.popperInstanceRef)==null||O.destroy()},se=()=>{p.props.menuTrigger==="hover"&&p.props.mode==="horizontal"||p.props.collapse&&p.props.mode==="vertical"||t.disabled||p.handleSubMenuClick({index:t.index,indexPath:s.value,active:x.value})},J=(E,R=t.showTimeout)=>{var w;E.type!=="focus"&&(p.props.menuTrigger==="click"&&p.props.mode==="horizontal"||!p.props.collapse&&p.props.mode==="vertical"||t.disabled||(m.mouseInChild.value=!0,b==null||b(),{stop:b}=Ml(()=>{p.openMenu(t.index,s.value)},R),F.value&&((w=r.value.vnode.el)==null||w.dispatchEvent(new MouseEvent("mouseenter")))))},ae=(E=!1)=>{var R,w;p.props.menuTrigger==="click"&&p.props.mode==="horizontal"||!p.props.collapse&&p.props.mode==="vertical"||(b==null||b(),m.mouseInChild.value=!1,{stop:b}=Ml(()=>!V.value&&p.closeMenu(t.index,s.value),t.hideTimeout),F.value&&E&&((R=n.parent)==null?void 0:R.type.name)==="ElSubMenu"&&((w=m.handleMouseleave)==null||w.call(m,!0)))};re(()=>p.props.collapse,E=>D(!!E));{const E=w=>{v.value[w.index]=w},R=w=>{delete v.value[w.index]};Qe(`subMenu:${n.uid}`,{addSubMenu:E,removeSubMenu:R,handleMouseleave:ae,mouseInChild:V,level:m.level+1})}return a({opened:K}),we(()=>{p.addSubMenu(S),m.addSubMenu(S)}),Cl(()=>{m.removeSubMenu(S),p.removeSubMenu(S)}),()=>{var E;const R=[(E=l.title)==null?void 0:E.call(l),te(Xe,{class:c.e("icon-arrow"),style:{transform:K.value?t.expandCloseIcon&&t.expandOpenIcon||t.collapseCloseIcon&&t.collapseOpenIcon&&p.props.collapse?"none":"rotateZ(180deg)":"none"}},{default:()=>Se(k.value)?te(n.appContext.components[k.value]):te(k.value)})],w=p.isMenuPopup?te(kl,{ref:u,visible:K.value,effect:"light",pure:!0,offset:t.popperOffset,showArrow:!1,persistent:!0,popperClass:t.popperClass,placement:g.value,teleported:F.value,fallbackPlacements:U.value,transition:Z.value,gpuAcceleration:!1},{content:()=>{var O;return te("div",{class:[i.m(y.value),i.m("popup-container"),t.popperClass],onMouseenter:q=>J(q,100),onMouseleave:()=>ae(!0),onFocus:q=>J(q,100)},[te("ul",{class:[i.b(),i.m("popup"),i.m(`popup-${g.value}`)],style:H.value},[(O=l.default)==null?void 0:O.call(l)])])},default:()=>te("div",{class:c.e("title"),onClick:se},R)}):te(le,{},[te("div",{class:c.e("title"),ref:I,onClick:se},R),te(Ro,{},{default:()=>{var O;return Ne(te("ul",{role:"menu",class:[i.b(),i.m("inline")],style:H.value},[(O=l.default)==null?void 0:O.call(l)]),[[Uo,K.value]])}})]);return te("li",{class:[c.b(),c.is("active",x.value),c.is("opened",K.value),c.is("disabled",t.disabled)],role:"menuitem",ariaHaspopup:!0,ariaExpanded:K.value,onMouseenter:J,onMouseleave:()=>ae(!0),onFocus:J},[w])}}});const aa=Re({mode:{type:String,values:["horizontal","vertical"],default:"vertical"},defaultActive:{type:String,default:""},defaultOpeneds:{type:Je(Array),default:()=>Ho([])},uniqueOpened:Boolean,router:Boolean,menuTrigger:{type:String,values:["hover","click"],default:"hover"},collapse:Boolean,backgroundColor:String,textColor:String,activeTextColor:String,collapseTransition:{type:Boolean,default:!0},ellipsis:{type:Boolean,default:!0},popperEffect:{type:String,values:["dark","light"],default:"dark"}}),pt=t=>Array.isArray(t)&&t.every(l=>Se(l));var oa=j({name:"ElMenu",props:aa,emits:{close:(t,l)=>Se(t)&&pt(l),open:(t,l)=>Se(t)&&pt(l),select:(t,l,a,n)=>Se(t)&&pt(l)&&Wo(a)&&(n===void 0||n instanceof Promise)},setup(t,{emit:l,slots:a,expose:n}){const s=et(),r=s.appContext.config.globalProperties.$router,i=_(),c=ue("menu"),p=ue("sub-menu"),m=_(-1),d=_(t.defaultOpeneds&&!t.collapse?t.defaultOpeneds.slice(0):[]),v=_(t.defaultActive),b=_({}),V=_({}),I=f(()=>t.mode==="horizontal"||t.mode==="vertical"&&t.collapse),u=(x,y)=>{d.value.includes(x)||(t.uniqueOpened&&(d.value=d.value.filter(S=>y.includes(S))),d.value.push(x),l("open",x,y))},g=x=>{const y=d.value.indexOf(x);y!==-1&&d.value.splice(y,1)},k=(x,y)=>{g(x),l("close",x,y)},C=({index:x,indexPath:y})=>{d.value.includes(x)?k(x,y):u(x,y)},F=x=>{(t.mode==="horizontal"||t.collapse)&&(d.value=[]);const{index:y,indexPath:S}=x;if(!Tl(y)&&!Tl(S))if(t.router&&r){const H=x.route||y,D=r.push(H).then(se=>(se||(v.value=y),se));l("select",y,S,{index:y,indexPath:S,route:H},D)}else v.value=y,l("select",y,S,{index:y,indexPath:S})};let Z=!0;const U=()=>{const x=()=>{m.value=-1,_t(()=>{m.value=(()=>{var y,S;if(!i.value)return-1;const H=Array.from((S=(y=i.value)==null?void 0:y.childNodes)!=null?S:[]).filter(R=>R.nodeName!=="#comment"&&(R.nodeName!=="#text"||R.nodeValue)),D=Number.parseInt(getComputedStyle(i.value).paddingLeft,10),se=Number.parseInt(getComputedStyle(i.value).paddingRight,10),J=i.value.clientWidth-D-se;let ae=0,E=0;return H.forEach((R,w)=>{ae+=R.offsetWidth||0,ae<=J-64&&(E=w+1)}),E===H.length?-1:E})()})};Z?x():((y,S=33.34)=>{let H;return()=>{H&&clearTimeout(H),H=setTimeout(()=>{y()},S)}})(x)(),Z=!1};let K;re(()=>t.defaultActive,x=>{b.value[x]||(v.value=""),(y=>{const S=b.value,H=S[y]||v.value&&S[v.value]||S[t.defaultActive];v.value=H?H.index:y})(x)}),re(()=>t.collapse,x=>{x&&(d.value=[])}),re(b.value,()=>{const x=v.value&&b.value[v.value];!x||t.mode==="horizontal"||t.collapse||x.indexPath.forEach(y=>{const S=V.value[y];S&&u(y,S.indexPath)})}),No(()=>{t.mode==="horizontal"&&t.ellipsis?K=Fo(i,U).stop:K==null||K()});{const x=D=>{V.value[D.index]=D},y=D=>{delete V.value[D.index]};Qe("rootMenu",ot({props:t,openedMenus:d,items:b,subMenus:V,activeIndex:v,isMenuPopup:I,addMenuItem:D=>{b.value[D.index]=D},removeMenuItem:D=>{delete b.value[D.index]},addSubMenu:x,removeSubMenu:y,openMenu:u,closeMenu:k,handleMenuItemClick:F,handleSubMenuClick:C})),Qe(`subMenu:${s.uid}`,{addSubMenu:x,removeSubMenu:y,mouseInChild:_(!1),level:0})}return we(()=>{t.mode==="horizontal"&&new Jl(s.vnode.el,c.namespace.value)}),n({open:x=>{const{indexPath:y}=V.value[x];y.forEach(S=>u(S,y))},close:g,handleResize:U}),()=>{var x,y;let S=(y=(x=a.default)==null?void 0:x.call(a))!=null?y:[];const H=[];if(t.mode==="horizontal"&&i.value){const J=qo(S),ae=m.value===-1?J:J.slice(0,m.value),E=m.value===-1?[]:J.slice(m.value);E!=null&&E.length&&t.ellipsis&&(S=ae,H.push(te(dt,{index:"sub-menu-more",class:p.e("hide-arrow")},{title:()=>te(Xe,{class:p.e("icon-more")},{default:()=>te(Do)}),default:()=>E})))}const D=Bt(t,0),se=te("ul",{key:String(t.collapse),role:"menubar",ref:i,style:D.value,class:{[c.b()]:!0,[c.m(t.mode)]:!0,[c.m("collapse")]:t.collapse}},[...S,...H]);return t.collapseTransition&&t.mode==="vertical"?te(ea,()=>se):se}}});const na=Re({index:{type:Je([String,null]),default:null},route:{type:Je([String,Object])},disabled:Boolean}),mt="ElMenuItem";var Lt=$e(j({name:mt,components:{ElTooltip:kl},props:na,emits:{click:t=>Se(t.index)&&Array.isArray(t.indexPath)},setup(t,{emit:l}){const a=et(),n=He("rootMenu"),s=ue("menu"),r=ue("menu-item");n||je(mt,"can not inject root menu");const{parentMenu:i,indexPath:c}=Pt(a,Go(t,"index")),p=He(`subMenu:${i.value.uid}`);p||je(mt,"can not inject sub menu");const m=f(()=>t.index===n.activeIndex),d=ot({index:t.index,indexPath:c,active:m});return we(()=>{p.addSubMenu(d),n.addMenuItem(d)}),Cl(()=>{p.removeSubMenu(d),n.removeMenuItem(d)}),{parentMenu:i,rootMenu:n,active:m,nsMenu:s,nsMenuItem:r,handleClick:()=>{t.disabled||(n.handleMenuItemClick({index:t.index,indexPath:c.value,route:t.route}),l("click",d))}}}}),[["render",function(t,l,a,n,s,r){const i=nt("el-tooltip");return M(),N("li",{class:A([t.nsMenuItem.b(),t.nsMenuItem.is("active",t.active),t.nsMenuItem.is("disabled",t.disabled)]),role:"menuitem",tabindex:"-1",onClick:l[0]||(l[0]=(...c)=>t.handleClick&&t.handleClick(...c))},[t.parentMenu.type.name==="ElMenu"&&t.rootMenu.props.collapse&&t.$slots.title?(M(),ee(i,{key:0,effect:t.rootMenu.props.popperEffect,placement:"right","fallback-placements":["left"],persistent:""},{content:$(()=>[pe(t.$slots,"title")]),default:$(()=>[h("div",{class:A(t.nsMenu.be("tooltip","trigger"))},[pe(t.$slots,"default")],2)]),_:3},8,["effect"])):(M(),N(le,{key:1},[pe(t.$slots,"default"),pe(t.$slots,"title")],64))],2)}],["__file","/home/<USER>/work/element-plus/element-plus/packages/components/menu/src/menu-item.vue"]]),zt=$e(j({name:"ElMenuItemGroup",props:{title:String},setup:()=>({ns:ue("menu-item-group")})}),[["render",function(t,l,a,n,s,r){return M(),N("li",{class:A(t.ns.b())},[h("div",{class:A(t.ns.e("title"))},[t.$slots.title?pe(t.$slots,"title",{key:1}):(M(),N(le,{key:0},[ce(B(t.title),1)],64))],2),h("ul",null,[pe(t.$slots,"default")])],2)}],["__file","/home/<USER>/work/element-plus/element-plus/packages/components/menu/src/menu-item-group.vue"]]);const sa=wt(oa,{MenuItem:Lt,MenuItemGroup:zt,SubMenu:dt}),ra=tt(Lt);tt(zt);const ia=tt(dt),ua=j({name:"BackTop",__name:"Backtop",setup(t){const{getPrefixCls:l,variables:a}=X(),n=l("backtop");return(s,r)=>(M(),ee(e(Nl),{class:A(`${e(n)}-backtop`),target:`.${e(a).namespace}-layout-content-scrollbar .${e(a).elNamespace}-scrollbar__wrap`},null,8,["class","target"]))}}),ca=["onClick"],da=j({name:"ColorRadioPicker",__name:"ColorRadioPicker",props:{schema:{type:Array,default:()=>[]},modelValue:st.string.def("")},emits:["update:modelValue","change"],setup(t,{emit:l}){const{getPrefixCls:a}=X(),n=a("color-radio-picker"),s=t,r=l,i=_(s.modelValue);return re(()=>s.modelValue,c=>{c!==e(i)&&(i.value=c)}),re(()=>i.value,c=>{r("update:modelValue",c),r("change",c)}),(c,p)=>{const m=ie;return M(),N("div",{class:A([e(n),"flex flex-wrap space-x-14px"])},[(M(!0),N(le,null,Fe(t.schema,(d,v)=>(M(),N("span",{key:`radio-${v}`,class:A([{"is-active":e(i)===d},"mb-5px h-20px w-20px cursor-pointer border-2px border-gray-300 rounded-2px border-solid text-center leading-20px"]),style:xl({background:d}),onClick:b=>i.value=d},[e(i)===d?(M(),ee(m,{key:0,size:16,color:"#fff",icon:"ep:check"})):Ie("",!0)],14,ca))),128))],2)}}}),vt=ge(da,[["__scopeId","data-v-61582eab"]]),pa={class:"flex items-center justify-between"},ma={class:"text-14px"},va={class:"flex items-center justify-between"},ha={class:"text-14px"},fa={class:"flex items-center justify-between"},ga={class:"text-14px"},ba={class:"flex items-center justify-between"},xa={class:"text-14px"},wa={class:"flex items-center justify-between"},ya={class:"text-14px"},_a={class:"flex items-center justify-between"},Ca={class:"text-14px"},ka={class:"flex items-center justify-between"},Ma={class:"text-14px"},Ta={class:"flex items-center justify-between"},Va={class:"text-14px"},Ia={class:"flex items-center justify-between"},$a={class:"text-14px"},Sa={class:"flex items-center justify-between"},Pa={class:"text-14px"},Ba={class:"flex items-center justify-between"},La={class:"text-14px"},za={class:"flex items-center justify-between"},Aa={class:"text-14px"},Oa={class:"flex items-center justify-between"},Ea={class:"text-14px"},ja={class:"flex items-center justify-between"},Ra={class:"text-14px"},Ua={class:"flex items-center justify-between"},Ha={class:"text-14px"},Na={class:"flex items-center justify-between"},Fa={class:"text-14px"},qa=j({name:"InterfaceDisplay",__name:"InterfaceDisplay",setup(t){const{t:l}=ye(),{getPrefixCls:a}=X(),{setWatermark:n}=fn(),s=a("interface-display"),r=ne(),i=_(),c=_(r.getBreadcrumb),p=P=>{r.setBreadcrumb(P)},m=_(r.getBreadcrumbIcon),d=P=>{r.setBreadcrumbIcon(P)},v=_(r.getHamburger),b=P=>{r.setHamburger(P)},V=_(r.getScreenfull),I=P=>{r.setScreenfull(P)},u=_(r.getSize),g=P=>{r.setSize(P)},k=_(r.getLocale),C=P=>{r.setLocale(P)},F=_(r.getMessage),Z=P=>{r.setMessage(P)},U=_(r.getTagsView),K=P=>{qe("--tags-view-height",P?"35px":"0px"),r.setTagsView(P)},x=_(r.getTagsViewIcon),y=P=>{r.setTagsViewIcon(P)},S=_(r.getLogo),H=P=>{r.setLogo(P)},D=_(r.getUniqueOpened),se=P=>{r.setUniqueOpened(P)},J=_(r.getFixedHeader),ae=P=>{r.setFixedHeader(P)},E=_(r.getFooter),R=P=>{r.setFooter(P)},w=_(r.getGreyMode),O=P=>{r.setGreyMode(P)},q=_(r.getFixedMenu),oe=P=>{r.setFixedMenu(P)},ve=f(()=>r.getLayout);return re(()=>ve.value,P=>{P==="top"&&r.setCollapse(!1)}),(P,T)=>{const G=Yo,Ve=Zo;return M(),N("div",{class:A(e(s))},[h("div",pa,[h("span",ma,B(e(l)("setting.breadcrumb")),1),o(G,{modelValue:e(c),"onUpdate:modelValue":T[0]||(T[0]=L=>Y(c)?c.value=L:null),onChange:p},null,8,["modelValue"])]),h("div",va,[h("span",ha,B(e(l)("setting.breadcrumbIcon")),1),o(G,{modelValue:e(m),"onUpdate:modelValue":T[1]||(T[1]=L=>Y(m)?m.value=L:null),onChange:d},null,8,["modelValue"])]),h("div",fa,[h("span",ga,B(e(l)("setting.hamburgerIcon")),1),o(G,{modelValue:e(v),"onUpdate:modelValue":T[2]||(T[2]=L=>Y(v)?v.value=L:null),onChange:b},null,8,["modelValue"])]),h("div",ba,[h("span",xa,B(e(l)("setting.screenfullIcon")),1),o(G,{modelValue:e(V),"onUpdate:modelValue":T[3]||(T[3]=L=>Y(V)?V.value=L:null),onChange:I},null,8,["modelValue"])]),h("div",wa,[h("span",ya,B(e(l)("setting.sizeIcon")),1),o(G,{modelValue:e(u),"onUpdate:modelValue":T[4]||(T[4]=L=>Y(u)?u.value=L:null),onChange:g},null,8,["modelValue"])]),h("div",_a,[h("span",Ca,B(e(l)("setting.localeIcon")),1),o(G,{modelValue:e(k),"onUpdate:modelValue":T[5]||(T[5]=L=>Y(k)?k.value=L:null),onChange:C},null,8,["modelValue"])]),h("div",ka,[h("span",Ma,B(e(l)("setting.messageIcon")),1),o(G,{modelValue:e(F),"onUpdate:modelValue":T[6]||(T[6]=L=>Y(F)?F.value=L:null),onChange:Z},null,8,["modelValue"])]),h("div",Ta,[h("span",Va,B(e(l)("setting.tagsView")),1),o(G,{modelValue:e(U),"onUpdate:modelValue":T[7]||(T[7]=L=>Y(U)?U.value=L:null),onChange:K},null,8,["modelValue"])]),h("div",Ia,[h("span",$a,B(e(l)("setting.tagsViewIcon")),1),o(G,{modelValue:e(x),"onUpdate:modelValue":T[8]||(T[8]=L=>Y(x)?x.value=L:null),onChange:y},null,8,["modelValue"])]),h("div",Sa,[h("span",Pa,B(e(l)("setting.logo")),1),o(G,{modelValue:e(S),"onUpdate:modelValue":T[9]||(T[9]=L=>Y(S)?S.value=L:null),onChange:H},null,8,["modelValue"])]),h("div",Ba,[h("span",La,B(e(l)("setting.uniqueOpened")),1),o(G,{modelValue:e(D),"onUpdate:modelValue":T[10]||(T[10]=L=>Y(D)?D.value=L:null),onChange:se},null,8,["modelValue"])]),h("div",za,[h("span",Aa,B(e(l)("setting.fixedHeader")),1),o(G,{modelValue:e(J),"onUpdate:modelValue":T[11]||(T[11]=L=>Y(J)?J.value=L:null),onChange:ae},null,8,["modelValue"])]),h("div",Oa,[h("span",Ea,B(e(l)("setting.footer")),1),o(G,{modelValue:e(E),"onUpdate:modelValue":T[12]||(T[12]=L=>Y(E)?E.value=L:null),onChange:R},null,8,["modelValue"])]),h("div",ja,[h("span",Ra,B(e(l)("setting.greyMode")),1),o(G,{modelValue:e(w),"onUpdate:modelValue":T[13]||(T[13]=L=>Y(w)?w.value=L:null),onChange:O},null,8,["modelValue"])]),h("div",Ua,[h("span",Ha,B(e(l)("setting.fixedMenu")),1),o(G,{modelValue:e(q),"onUpdate:modelValue":T[14]||(T[14]=L=>Y(q)?q.value=L:null),onChange:oe},null,8,["modelValue"])]),h("div",Na,[h("span",Fa,B(e(l)("watermark.watermark")),1),o(Ve,{modelValue:e(i),"onUpdate:modelValue":T[15]||(T[15]=L=>Y(i)?i.value=L:null),class:"right-1 w-20",onChange:T[16]||(T[16]=L=>{n(i.value)})},null,8,["modelValue"])])],2)}}}),Da=[(t=>(Vl("data-v-d0ae4a8f"),t=t(),Il(),t))(()=>h("div",{class:"absolute left-[10%] top-0 h-full w-[33%] bg-gray-200"},null,-1))],Wa=j({name:"LayoutRadioPicker",__name:"LayoutRadioPicker",setup(t){const{getPrefixCls:l}=X(),a=l("layout-radio-picker"),n=ne(),s=f(()=>n.getLayout);return(r,i)=>(M(),N("div",{class:A([e(a),"flex flex-wrap space-x-14px"])},[h("div",{class:A([`${e(a)}__classic`,"relative w-56px h-48px cursor-pointer bg-gray-300",{"is-acitve":e(s)==="classic"}]),onClick:i[0]||(i[0]=c=>e(n).setLayout("classic"))},null,2),h("div",{class:A([`${e(a)}__top-left`,"relative w-56px h-48px cursor-pointer bg-gray-300",{"is-acitve":e(s)==="topLeft"}]),onClick:i[1]||(i[1]=c=>e(n).setLayout("topLeft"))},null,2),h("div",{class:A([`${e(a)}__top`,"relative w-56px h-48px cursor-pointer bg-gray-300",{"is-acitve":e(s)==="top"}]),onClick:i[2]||(i[2]=c=>e(n).setLayout("top"))},null,2),h("div",{class:A([`${e(a)}__cut-menu`,"relative w-56px h-48px cursor-pointer bg-gray-300",{"is-acitve":e(s)==="cutMenu"}]),onClick:i[3]||(i[3]=c=>e(n).setLayout("cutMenu"))},Da,2)],2))}}),Ga=ge(Wa,[["__scopeId","data-v-d0ae4a8f"]]),Ya={class:"text-16px font-700"},Za={class:"text-center"},Ka={class:"mt-5px"},Xa=j({name:"Setting",__name:"Setting",setup(t){const{t:l}=ye(),a=ne(),{getPrefixCls:n}=X(),s=n("setting"),r=f(()=>a.getLayout),i=_(!1),c=_(a.getTheme.elColorPrimary),p=u=>{qe("--el-color-primary",u),a.setTheme({elColorPrimary:u});const g=$l("--left-menu-bg-color",document.documentElement);b(Ko(e(g)))},m=_(a.getTheme.topHeaderBgColor||""),d=u=>{const g=zl(u),k=g?"#fff":"inherit",C=g?Al(u,6):"#f6f6f6",F=g?u:"#eee";qe("--top-header-bg-color",u),qe("--top-header-text-color",k),qe("--top-header-hover-color",C),a.setTheme({topHeaderBgColor:u,topHeaderTextColor:k,topHeaderHoverColor:C,topToolBorderColor:F}),e(r)==="top"&&b(u)},v=_(a.getTheme.leftMenuBgColor||""),b=u=>{const g=$l("--el-color-primary",document.documentElement),k=zl(u),C={leftMenuBorderColor:k?"inherit":"#eee",leftMenuBgColor:u,leftMenuBgLightColor:k?Al(u,6):u,leftMenuBgActiveColor:k?"var(--el-color-primary)":Ol(e(g),.1),leftMenuCollapseBgActiveColor:k?"var(--el-color-primary)":Ol(e(g),.1),leftMenuTextColor:k?"#bfcbd9":"#333",leftMenuTextActiveColor:k?"#fff":"var(--el-color-primary)",logoTitleTextColor:k?"#fff":"inherit",logoBorderColor:k?u:"#eee"};a.setTheme(C),a.setCssVarTheme()};r.value!=="top"||a.getIsDark||(m.value="#fff",d("#fff")),re(()=>r.value,u=>{u!=="top"||a.getIsDark?b(e(v)):(m.value="#fff",d("#fff"))});const V=async()=>{const{copy:u,copied:g,isSupported:k}=Xo({source:`
      // \u9762\u5305\u5C51
      breadcrumb: ${a.getBreadcrumb},
      // \u9762\u5305\u5C51\u56FE\u6807
      breadcrumbIcon: ${a.getBreadcrumbIcon},
      // \u6298\u53E0\u56FE\u6807
      hamburger: ${a.getHamburger},
      // \u5168\u5C4F\u56FE\u6807
      screenfull: ${a.getScreenfull},
      // \u5C3A\u5BF8\u56FE\u6807
      size: ${a.getSize},
      // \u591A\u8BED\u8A00\u56FE\u6807
      locale: ${a.getLocale},
      // \u6D88\u606F\u56FE\u6807
      message: ${a.getMessage},
      // \u6807\u7B7E\u9875
      tagsView: ${a.getTagsView},
      // \u6807\u7B7E\u9875\u56FE\u6807
      getTagsViewIcon: ${a.getTagsViewIcon},
      // logo
      logo: ${a.getLogo},
      // \u83DC\u5355\u624B\u98CE\u7434
      uniqueOpened: ${a.getUniqueOpened},
      // \u56FA\u5B9Aheader
      fixedHeader: ${a.getFixedHeader},
      // \u9875\u811A
      footer: ${a.getFooter},
      // \u7070\u8272\u6A21\u5F0F
      greyMode: ${a.getGreyMode},
      // layout\u5E03\u5C40
      layout: '${a.getLayout}',
      // \u6697\u9ED1\u6A21\u5F0F
      isDark: ${a.getIsDark},
      // \u7EC4\u4EF6\u5C3A\u5BF8
      currentSize: '${a.getCurrentSize}',
      // \u4E3B\u9898\u76F8\u5173
      theme: {
        // \u4E3B\u9898\u8272
        elColorPrimary: '${a.getTheme.elColorPrimary}',
        // \u5DE6\u4FA7\u83DC\u5355\u8FB9\u6846\u989C\u8272
        leftMenuBorderColor: '${a.getTheme.leftMenuBorderColor}',
        // \u5DE6\u4FA7\u83DC\u5355\u80CC\u666F\u989C\u8272
        leftMenuBgColor: '${a.getTheme.leftMenuBgColor}',
        // \u5DE6\u4FA7\u83DC\u5355\u6D45\u8272\u80CC\u666F\u989C\u8272
        leftMenuBgLightColor: '${a.getTheme.leftMenuBgLightColor}',
        // \u5DE6\u4FA7\u83DC\u5355\u9009\u4E2D\u80CC\u666F\u989C\u8272
        leftMenuBgActiveColor: '${a.getTheme.leftMenuBgActiveColor}',
        // \u5DE6\u4FA7\u83DC\u5355\u6536\u8D77\u9009\u4E2D\u80CC\u666F\u989C\u8272
        leftMenuCollapseBgActiveColor: '${a.getTheme.leftMenuCollapseBgActiveColor}',
        // \u5DE6\u4FA7\u83DC\u5355\u5B57\u4F53\u989C\u8272
        leftMenuTextColor: '${a.getTheme.leftMenuTextColor}',
        // \u5DE6\u4FA7\u83DC\u5355\u9009\u4E2D\u5B57\u4F53\u989C\u8272
        leftMenuTextActiveColor: '${a.getTheme.leftMenuTextActiveColor}',
        // logo\u5B57\u4F53\u989C\u8272
        logoTitleTextColor: '${a.getTheme.logoTitleTextColor}',
        // logo\u8FB9\u6846\u989C\u8272
        logoBorderColor: '${a.getTheme.logoBorderColor}',
        // \u5934\u90E8\u80CC\u666F\u989C\u8272
        topHeaderBgColor: '${a.getTheme.topHeaderBgColor}',
        // \u5934\u90E8\u5B57\u4F53\u989C\u8272
        topHeaderTextColor: '${a.getTheme.topHeaderTextColor}',
        // \u5934\u90E8\u60AC\u505C\u989C\u8272
        topHeaderHoverColor: '${a.getTheme.topHeaderHoverColor}',
        // \u5934\u90E8\u8FB9\u6846\u989C\u8272
        topToolBorderColor: '${a.getTheme.topToolBorderColor}'
      }
    `});k?(await u(),e(g)&&Sl.success(l("setting.copySuccess"))):Sl.error(l("setting.copyFailed"))},I=()=>{const{wsCache:u}=Pl();u.delete(rt.LAYOUT),u.delete(rt.THEME),u.delete(rt.IS_DARK),window.location.reload()};return(u,g)=>{const k=ie,C=Qo,F=Jo,Z=dn;return M(),N(le,null,[h("div",{class:A([e(s),"fixed right-0 top-[45%] h-40px w-40px cursor-pointer bg-[var(--el-color-primary)] text-center leading-40px"]),onClick:g[0]||(g[0]=U=>i.value=!0)},[o(k,{color:"#fff",icon:"ep:setting"})],2),o(Z,{modelValue:e(i),"onUpdate:modelValue":g[4]||(g[4]=U=>Y(i)?i.value=U:null),"z-index":4e3,direction:"rtl",size:"350px"},{header:$(()=>[h("span",Ya,B(e(l)("setting.projectSetting")),1)]),default:$(()=>[h("div",Za,[o(C,null,{default:$(()=>[ce(B(e(l)("setting.theme")),1)]),_:1}),o(e(mn)),o(C,null,{default:$(()=>[ce(B(e(l)("setting.layout")),1)]),_:1}),o(Ga),o(C,null,{default:$(()=>[ce(B(e(l)("setting.systemTheme")),1)]),_:1}),o(vt,{modelValue:e(c),"onUpdate:modelValue":g[1]||(g[1]=U=>Y(c)?c.value=U:null),schema:["#409eff","#009688","#536dfe","#ff5c93","#ee4f12","#0096c7","#9c27b0","#ff9800"],onChange:p},null,8,["modelValue"]),o(C,null,{default:$(()=>[ce(B(e(l)("setting.headerTheme")),1)]),_:1}),o(vt,{modelValue:e(m),"onUpdate:modelValue":g[2]||(g[2]=U=>Y(m)?m.value=U:null),schema:["#fff","#151515","#5172dc","#e74c3c","#24292e","#394664","#009688","#383f45"],onChange:d},null,8,["modelValue"]),e(r)!=="top"?(M(),N(le,{key:0},[o(C,null,{default:$(()=>[ce(B(e(l)("setting.menuTheme")),1)]),_:1}),o(vt,{modelValue:e(v),"onUpdate:modelValue":g[3]||(g[3]=U=>Y(v)?v.value=U:null),schema:["#fff","#001529","#212121","#273352","#191b24","#383f45","#001628","#344058"],onChange:b},null,8,["modelValue"])],64)):Ie("",!0)]),o(C,null,{default:$(()=>[ce(B(e(l)("setting.interfaceDisplay")),1)]),_:1}),o(qa),o(C),h("div",null,[o(F,{class:"w-full",type:"primary",onClick:V},{default:$(()=>[ce(B(e(l)("setting.copy")),1)]),_:1})]),h("div",Ka,[o(F,{class:"w-full",type:"danger",onClick:I},{default:$(()=>[ce(B(e(l)("setting.clearAndReset")),1)]),_:1})])]),_:1},8,["modelValue"])],64)}}}),Qa=ge(Xa,[["__scopeId","data-v-40388337"]]),Ja=(t,l)=>(gn(t,a=>a.path===l)||[]).map(a=>a.path),eo=t=>{const l=(a,n="/")=>a.map(s=>{const r=s.meta??{};if(!r.hidden){const{oneShowingChild:i,onlyOneChild:c}=((d=[],v)=>{const b=_(),V=d.filter(I=>!(I.meta??{}).hidden&&(b.value=I,!0));return V.length===1?{oneShowingChild:!0,onlyOneChild:e(b)}:V.length?{oneShowingChild:!1,onlyOneChild:e(b)}:(b.value={...v,path:"",noShowingChildren:!0},{oneShowingChild:!0,onlyOneChild:e(b)})})(s.children,s),p=it(s.path)?s.path:ke(n,s.path),{renderMenuTitle:m}={renderMenuTitle:d=>{const{t:v}=ye(),{title:b="Please set title",icon:V}=d;return V?o(le,null,[o(ie,{icon:d.icon},null),o("span",{class:"v-menu__title"},[v(b)])]):o("span",{class:"v-menu__title"},[v(b)])}};if(!i||c!=null&&c.children&&!(c!=null&&c.noShowingChildren)||r!=null&&r.alwaysShow){const{getPrefixCls:d}=X(),v=d("menu-popper");return o(ia,{index:p,popperClass:t==="vertical"?`${v}--vertical`:`${v}--horizontal`},{title:()=>m(r),default:()=>l(s.children,p)})}return o(ra,{index:c?ke(p,c.path):p},{default:()=>m(c?c==null?void 0:c.meta:r)})}});return{renderMenuItem:l}},{getPrefixCls:to}=X(),ht=to("menu"),lo=j({name:"Menu",props:{menuSelect:{type:Function,default:void 0}},setup(t){const l=ne(),a=f(()=>l.getLayout),{push:n,currentRoute:s}=Pe(),r=ut(),i=f(()=>["classic","topLeft","cutMenu"].includes(e(a))?"vertical":"horizontal"),c=f(()=>e(a)==="cutMenu"?r.getMenuTabRouters:r.getRouters),p=f(()=>l.getCollapse),m=f(()=>l.getUniqueOpened),d=f(()=>{const{meta:I,path:u}=e(s);return I.activeMenu?I.activeMenu:u}),v=I=>{t.menuSelect&&t.menuSelect(I),it(I)?window.open(I):n(I)},b=()=>{if(e(a)==="top")return V();{let u;return o(Me,null,typeof(I=u=V())=="function"||Object.prototype.toString.call(I)==="[object Object]"&&!Bl(I)?u:{default:()=>[u]})}var I},V=()=>o(sa,{defaultActive:e(d),mode:e(i),collapse:e(a)!=="top"&&e(a)!=="cutMenu"&&e(p),uniqueOpened:e(a)!=="top"&&e(m),backgroundColor:"var(--left-menu-bg-color)",textColor:"var(--left-menu-text-color)",activeTextColor:"var(--left-menu-text-active-color)",onSelect:v},{default:()=>{const{renderMenuItem:I}=eo(e(i));return I(e(c))}});return()=>o("div",{id:ht,class:[`${ht} ${ht}__${e(i)}`,"h-[100%] overflow-hidden flex-col bg-[var(--left-menu-bg-color)]",{"w-[var(--left-menu-min-width)]":e(p)&&e(a)!=="cutMenu","w-[var(--left-menu-max-width)]":!e(p)&&e(a)!=="cutMenu"}]},[b()])}}),We=ge(lo,[["__scopeId","data-v-545cecd6"]]),Ge=ot({}),At=(t,l)=>{const a=[];for(const n of t){let s=null;const r=n.meta??{};if(!r.hidden||r.canTo){const i=Ja(l,n.path),c=it(n.path)?n.path:i.join("/");s=Ct(n),s.path=c,n.children&&s&&(s.children=At(n.children,l)),s&&a.push(s),i.length&&Reflect.has(Ge,i[0])&&Ge[i[0]].push(c)}}return a},{getPrefixCls:ao,variables:oo}=X(),ft=ao("tab-menu"),no=j({name:"TabMenu",setup(){const{push:t,currentRoute:l}=Pe(),{t:a}=ye(),n=ne(),s=f(()=>n.getCollapse),r=f(()=>n.getFixedMenu),i=ut(),c=f(()=>i.getRouters),p=f(()=>e(c).filter(u=>{var g;return!((g=u==null?void 0:u.meta)!=null&&g.hidden)})),m=()=>{n.setCollapse(!e(s))};we(()=>{var u;if(e(r)){const g=`/${e(l).path.split("/")[1]}`,k=(u=e(p).find(C=>{var F,Z,U;return(((F=C.meta)==null?void 0:F.alwaysShow)||((Z=C==null?void 0:C.children)==null?void 0:Z.length)&&((U=C==null?void 0:C.children)==null?void 0:U.length)>1)&&C.path===g}))==null?void 0:u.children;b.value=g,k&&i.setMenuTabRouters(Ct(k).map(C=>(C.path=ke(e(b),C.path),C)))}}),re(()=>c.value,u=>{(g=>{for(const k of g){const C=k.meta??{};C!=null&&C.hidden||(Ge[k.path]=[])}})(u),At(u,u)},{immediate:!0,deep:!0});const d=_(!0);re(()=>s.value,u=>{u?d.value=!u:setTimeout(()=>{d.value=!u},200)});const v=_(!!e(r)),b=_(""),V=u=>{const{path:g}=e(l);return!!Ge[u].includes(g)},I=()=>{e(v)&&!e(r)&&(v.value=!1)};return()=>o("div",{id:`${oo.namespace}-menu`,class:[ft,"relative bg-[var(--left-menu-bg-color)] top-1px z-3000 layout-border__right",{"w-[var(--tab-menu-max-width)]":!e(s),"w-[var(--tab-menu-min-width)]":e(s)}],onMouseleave:I},[o(Me,{class:"!h-[calc(100%-var(--tab-menu-collapse-height)-1px)]"},{default:()=>[o("div",null,{default:()=>e(p).map(u=>{var k,C,F,Z,U,K;const g=(k=u.meta)!=null&&k.alwaysShow||(C=u==null?void 0:u.children)!=null&&C.length&&((F=u==null?void 0:u.children)==null?void 0:F.length)>1?u:{...(u==null?void 0:u.children)&&(u==null?void 0:u.children[0]),path:ke(u.path,(Z=(u==null?void 0:u.children)&&(u==null?void 0:u.children[0]))==null?void 0:Z.path)};return o("div",{class:[`${ft}__item`,"text-center text-12px relative py-12px cursor-pointer",{"is-active":V(u.path)}],onClick:()=>{(x=>{if(it(x.path))return void window.open(x.path);const y=x.children?x.path:x.path.split("/")[0],S=e(b);b.value=x.children?x.path:x.path.split("/")[0],x.children?(y!==S&&e(v)||(v.value=!!e(r)||!e(v)),e(v)&&i.setMenuTabRouters(Ct(x.children).map(H=>(H.path=ke(e(b),H.path),H)))):(t(x.path),i.setMenuTabRouters([]),v.value=!1)})(g)}},[o("div",null,[o(ie,{icon:(U=g==null?void 0:g.meta)==null?void 0:U.icon},null)]),e(d)?o("p",{class:"mt-5px break-words px-2px"},[a((K=g.meta)==null?void 0:K.title)]):void 0])})})]}),o("div",{class:[`${ft}--collapse`,"text-center h-[var(--tab-menu-collapse-height)] leading-[var(--tab-menu-collapse-height)] cursor-pointer"],onClick:m},[o(ie,{icon:e(s)?"ep:d-arrow-right":"ep:d-arrow-left"},null)]),o(We,{class:["!absolute top-0",{"!left-[var(--tab-menu-min-width)]":e(s),"!left-[var(--tab-menu-max-width)]":!e(s),"!w-[calc(var(--left-menu-max-width)+1px)]":e(v)||e(r),"!w-0":!e(v)&&!e(r)}],style:"transition: width var(--transition-time-02), left var(--transition-time-02);"},null)])}}),so=ge(no,[["__scopeId","data-v-bff6aa91"]]),Ot=(t,l="")=>{let a=[];return t.forEach(n=>{const s=n.meta,r=ke(l,n.path);if(s!=null&&s.affix&&a.push({...n,path:r,fullPath:r}),n.children){const i=Ot(n.children,r);i.length>=1&&(a=[...a,...i])}}),a},Et=j({name:"ContextMenu",__name:"ContextMenu",props:{schema:{type:Array,default:()=>[]},trigger:{type:String,default:"contextmenu"},tagItem:{type:Object,default:()=>({})}},emits:["visibleChange"],setup(t,{expose:l,emit:a}){const{getPrefixCls:n}=X(),s=n("context-menu"),{t:r}=ye(),i=a,c=t,p=v=>{v.command&&v.command(v)},m=v=>{i("visibleChange",v,c.tagItem)},d=_();return l({elDropdownMenuRef:d,tagItem:c.tagItem}),(v,b)=>{const V=ie,I=Mt,u=Tt,g=Vt;return M(),ee(g,{ref_key:"elDropdownMenuRef",ref:d,class:A(e(s)),trigger:t.trigger,placement:"bottom-start","popper-class":"v-context-menu-popper",onCommand:p,onVisibleChange:m},{dropdown:$(()=>[o(u,null,{default:$(()=>[(M(!0),N(le,null,Fe(t.schema,(k,C)=>(M(),ee(I,{key:`dropdown${C}`,command:k,disabled:k.disabled,divided:k.divided},{default:$(()=>[o(V,{icon:k.icon},null,8,["icon"]),ce(" "+B(e(r)(k.label)),1)]),_:2},1032,["command","disabled","divided"]))),128))]),_:1})]),default:$(()=>[pe(v.$slots,"default")]),_:3},8,["class","trigger"])}}});function Be({el:t,position:l="scrollLeft",to:a,duration:n=500,callback:s}){const r=_(!1),i=t[l],c=a-i,p=20;let m=0;function d(){if(!e(r))return;m+=p;const v=(b=m,V=i,I=c,(b/=n/2)<1?I/2*b*b+V:-I/2*(--b*(b-2)-1)+V);var b,V,I;((u,g,k)=>{u[g]=k})(t,l,v),m<n&&e(r)?requestAnimationFrame(d):s&&s()}return{start:function(){r.value=!0,d()},stop:function(){r.value=!1}}}const ro=["id"],io={class:"flex-1 overflow-hidden"},uo={class:"h-full flex"},co=["onClick"],po=j({__name:"TagsView",setup(t){const{getPrefixCls:l}=X(),a=l("tags-view"),{t:n}=ye(),{currentRoute:s,push:r,replace:i}=Pe(),c=ut(),p=f(()=>c.getRouters),m=kt(),d=f(()=>m.getVisitedViews),v=_([]),b=ne(),V=f(()=>b.getTagsViewIcon),I=f(()=>b.getIsDark),u=_(),g=()=>{const{name:w}=e(s);return w&&(u.value=e(s),m.addView(e(s))),!1},k=w=>{var O;(O=w==null?void 0:w.meta)!=null&&O.affix||(m.delView(w),H(w)&&x())},C=()=>{m.delAllViews(),x()},F=()=>{m.delOthersViews(e(u))},Z=async w=>{if(!w)return;m.delCachedView();const{path:O,query:q}=w;await _t(),i({path:"/redirect"+O,query:q})},U=()=>{m.delLeftViews(e(u))},K=()=>{m.delRightViews(e(u))},x=()=>{const w=m.getVisitedViews.slice(-1)[0];if(w)r(w);else{if(e(s).path===c.getAddRouters[0].path||e(s).path===c.getAddRouters[0].redirect)return void g();r("/")}},y=Ll(),S=w=>{var P;const O=(P=e(J))==null?void 0:P.wrapRef;let q=null,oe=null;const ve=e(y);if(ve.length>0&&(q=ve[0],oe=ve[ve.length-1]),(q==null?void 0:q.to).fullPath===w.fullPath){const{start:T}=Be({el:O,position:"scrollLeft",to:0,duration:500});T()}else if((oe==null?void 0:oe.to).fullPath===w.fullPath){const{start:T}=Be({el:O,position:"scrollLeft",to:O.scrollWidth-O.offsetWidth,duration:500});T()}else{const T=ve.findIndex(he=>(he==null?void 0:he.to).fullPath===w.fullPath),G=document.getElementsByClassName(`${a}__item`),Ve=G[T-1],L=G[T+1],z=L.offsetLeft+L.offsetWidth+4,Ee=Ve.offsetLeft-4;if(z>e(ae)+O.offsetWidth){const{start:he}=Be({el:O,position:"scrollLeft",to:z-O.offsetWidth,duration:500});he()}else if(Ee<e(ae)){const{start:he}=Be({el:O,position:"scrollLeft",to:Ee,duration:500});he()}}},H=w=>w.path===e(s).path,D=Ll(),se=(w,O)=>{if(w)for(const q of e(D)){const oe=q.elDropdownMenuRef;O.fullPath!==q.tagItem.fullPath&&(oe==null||oe.handleClose())}},J=_(),ae=_(0),E=({scrollLeft:w})=>{ae.value=w},R=w=>{var oe;const O=(oe=e(J))==null?void 0:oe.wrapRef,{start:q}=Be({el:O,position:"scrollLeft",to:e(ae)+w,duration:500});q()};return we(()=>{(()=>{v.value=Ot(e(p));for(const w of e(v))w.name&&m.addVisitedView(w)})(),g()}),re(()=>s.value,()=>{g(),(async()=>{await _t();for(const w of e(d))if(w.fullPath===e(s).path){S(w),w.fullPath!==e(s).fullPath&&m.updateVisitedView(e(s));break}})()}),(w,O)=>{var ve,P,T,G,Ve,L;const q=ie,oe=nt("router-link");return M(),N("div",{id:e(a),class:A([e(a),"relative w-full flex bg-[#fff] dark:bg-[var(--el-bg-color)]"])},[h("span",{class:A([`${e(a)}__tool ${e(a)}__tool--first`,"h-[var(--tags-view-height)] w-[var(--tags-view-height)] flex cursor-pointer items-center justify-center"]),onClick:O[0]||(O[0]=z=>R(-200))},[o(q,{icon:"ep:d-arrow-left",color:"var(--el-text-color-placeholder)","hover-color":I.value?"#fff":"var(--el-color-black)"},null,8,["hover-color"])],2),h("div",io,[o(e(Me),{ref_key:"scrollbarRef",ref:J,class:"h-full",onScroll:E},{default:$(()=>[h("div",uo,[(M(!0),N(le,null,Fe(d.value,z=>{var Ee,he,sl,rl,il,ul,cl,dl,pl;return M(),ee(e(Et),{ref_for:!0,ref:e(D).set,schema:[{icon:"ep:refresh",label:e(n)("common.reload"),disabled:((Ee=u.value)==null?void 0:Ee.fullPath)!==z.fullPath,command:()=>{Z(z)}},{icon:"ep:close",label:e(n)("common.closeTab"),disabled:!!((he=d.value)!=null&&he.length)&&((sl=u.value)==null?void 0:sl.meta.affix),command:()=>{k(z)}},{divided:!0,icon:"ep:d-arrow-left",label:e(n)("common.closeTheLeftTab"),disabled:!!((rl=d.value)!=null&&rl.length)&&(z.fullPath===d.value[0].fullPath||((il=u.value)==null?void 0:il.fullPath)!==z.fullPath),command:()=>{U()}},{icon:"ep:d-arrow-right",label:e(n)("common.closeTheRightTab"),disabled:!!((ul=d.value)!=null&&ul.length)&&(z.fullPath===d.value[d.value.length-1].fullPath||((cl=u.value)==null?void 0:cl.fullPath)!==z.fullPath),command:()=>{K()}},{divided:!0,icon:"ep:discount",label:e(n)("common.closeOther"),disabled:((dl=u.value)==null?void 0:dl.fullPath)!==z.fullPath,command:()=>{F()}},{icon:"ep:minus",label:e(n)("common.closeAll"),command:()=>{C()}}],key:z.fullPath,"tag-item":z,class:A([`${e(a)}__item`,(pl=z==null?void 0:z.meta)!=null&&pl.affix?`${e(a)}__item--affix`:"",{"is-active":H(z)}]),onVisibleChange:se},{default:$(()=>[h("div",null,[o(oe,{ref_for:!0,ref:e(y).set,to:{...z},custom:""},{default:$(({navigate:$o})=>{var ml,vl,hl,fl,gl;return[h("div",{onClick:$o,class:"h-full flex items-center justify-center whitespace-nowrap pl-15px"},[z!=null&&z.matched&&(z!=null&&z.matched[1])&&((vl=(ml=z==null?void 0:z.matched[1])==null?void 0:ml.meta)!=null&&vl.icon)&&V.value?(M(),ee(q,{key:0,icon:(fl=(hl=z==null?void 0:z.matched[1])==null?void 0:hl.meta)==null?void 0:fl.icon,size:12,class:"mr-5px"},null,8,["icon"])):Ie("",!0),ce(" "+B(e(n)((gl=z==null?void 0:z.meta)==null?void 0:gl.title))+" ",1),o(q,{class:A(`${e(a)}__item--close`),color:"#333",icon:"ep:close",size:12,onClick:wl(zn=>k(z),["prevent","stop"])},null,8,["class","onClick"])],8,co)]}),_:2},1032,["to"])])]),_:2},1032,["schema","tag-item","class"])}),128))])]),_:1},512)]),h("span",{class:A([`${e(a)}__tool`,"h-[var(--tags-view-height)] w-[var(--tags-view-height)] flex cursor-pointer items-center justify-center"]),onClick:O[1]||(O[1]=z=>R(200))},[o(q,{icon:"ep:d-arrow-right",color:"var(--el-text-color-placeholder)","hover-color":I.value?"#fff":"var(--el-color-black)"},null,8,["hover-color"])],2),h("span",{class:A([`${e(a)}__tool`,"h-[var(--tags-view-height)] w-[var(--tags-view-height)] flex cursor-pointer items-center justify-center"]),onClick:O[2]||(O[2]=z=>Z(u.value))},[o(q,{icon:"ep:refresh-right",color:"var(--el-text-color-placeholder)","hover-color":I.value?"#fff":"var(--el-color-black)"},null,8,["hover-color"])],2),o(e(Et),{trigger:"click",schema:[{icon:"ep:refresh",label:e(n)("common.reload"),command:()=>{Z(u.value)}},{icon:"ep:close",label:e(n)("common.closeTab"),disabled:!!((ve=d.value)!=null&&ve.length)&&((P=u.value)==null?void 0:P.meta.affix),command:()=>{k(u.value)}},{divided:!0,icon:"ep:d-arrow-left",label:e(n)("common.closeTheLeftTab"),disabled:!!((T=d.value)!=null&&T.length)&&((G=u.value)==null?void 0:G.fullPath)===d.value[0].fullPath,command:()=>{U()}},{icon:"ep:d-arrow-right",label:e(n)("common.closeTheRightTab"),disabled:!!((Ve=d.value)!=null&&Ve.length)&&((L=u.value)==null?void 0:L.fullPath)===d.value[d.value.length-1].fullPath,command:()=>{K()}},{divided:!0,icon:"ep:discount",label:e(n)("common.closeOther"),command:()=>{F()}},{icon:"ep:minus",label:e(n)("common.closeAll"),command:()=>{C()}}]},{default:$(()=>[h("span",{class:A([`${e(a)}__tool`,"block h-[var(--tags-view-height)] w-[var(--tags-view-height)] flex cursor-pointer items-center justify-center"])},[o(q,{icon:"ep:menu",color:"var(--el-text-color-placeholder)","hover-color":I.value?"#fff":"var(--el-color-black)"},null,8,["hover-color"])],2)]),_:1},8,["schema"])],10,ro)}}}),Ye=ge(po,[["__scopeId","data-v-edd6b962"]]),mo=h("img",{class:"h-[calc(var(--logo-height)-10px)] w-[calc(var(--logo-height)-10px)]",src:_n},null,-1),Ze=j({name:"Logo",__name:"Logo",setup(t){const{getPrefixCls:l}=X(),a=l("logo"),n=ne(),s=_(!0),r=f(()=>n.getTitle),i=f(()=>n.getLayout),c=f(()=>n.getCollapse);return we(()=>{e(c)&&(s.value=!1)}),re(()=>c.value,p=>{e(i)!=="topLeft"&&e(i)!=="cutMenu"?p?s.value=!p:setTimeout(()=>{s.value=!p},400):s.value=!0}),re(()=>i.value,p=>{p==="top"||p==="cutMenu"?s.value=!0:e(c)?s.value=!1:s.value=!0}),(p,m)=>{const d=nt("router-link");return M(),N("div",null,[o(d,{class:A([e(a),i.value!=="classic"?`${e(a)}__Top`:"","flex !h-[var(--logo-height)] items-center cursor-pointer pl-8px relative decoration-none overflow-hidden"]),to:"/"},{default:$(()=>[mo,s.value?(M(),N("div",{key:0,class:A(["ml-10px text-16px font-700",{"text-[var(--logo-title-text-color)]":i.value==="classic","text-[var(--top-header-text-color)]":i.value==="topLeft"||i.value==="top"||i.value==="cutMenu"}])},B(r.value),3)):Ie("",!0)]),_:1},8,["class"])])}}}),vo={style:{"font-size":"14px"}},ho=j({name:"Footer",__name:"Footer",setup(t){const{getPrefixCls:l}=X(),a=l("footer"),n=ne(),s=f(()=>n.getTitle);return(r,i)=>(M(),N("div",{class:A([e(a),"h-[var(--app-footer-height)] bg-[var(--app-content-bg-color)] text-center leading-[var(--app-footer-height)] text-[var(--el-text-color-placeholder)] dark:bg-[var(--el-bg-color)]"])},[h("p",vo,"Copyright \xA92022-"+B(e(s)),1)],2))}}),Ke=j({name:"AppView",__name:"AppView",setup(t){const l=ne(),a=f(()=>l.getLayout),n=f(()=>l.getFixedHeader),s=f(()=>l.getFooter),r=kt(),i=f(()=>r.getCachedViews),c=f(()=>l.getTagsView);return(p,m)=>{const d=nt("router-view");return M(),N(le,null,[h("section",{class:A(["p-[var(--app-content-padding)] w-[calc(100%-var(--app-content-padding)-var(--app-content-padding))] bg-[var(--app-content-bg-color)] dark:bg-[var(--el-bg-color)]",{"!min-h-[calc(100%-var(--app-content-padding)-var(--app-content-padding)-var(--app-footer-height))]":e(n)&&(e(a)==="classic"||e(a)==="topLeft"||e(a)==="top")&&e(s)||!e(c)&&e(a)==="top"&&e(s),"!min-h-[calc(100%-var(--app-content-padding)-var(--app-content-padding)-var(--app-footer-height)-var(--tags-view-height))]":e(c)&&e(a)==="top"&&e(s),"!min-h-[calc(100%-var(--tags-view-height)-var(--app-content-padding)-var(--app-content-padding)-var(--top-tool-height)-var(--app-footer-height))]":!e(n)&&e(a)==="classic"&&e(s),"!min-h-[calc(100%-var(--tags-view-height)-var(--app-content-padding)-var(--app-content-padding)-var(--app-footer-height))]":!e(n)&&e(a)==="topLeft"&&e(s),"!min-h-[calc(100%-var(--top-tool-height)-var(--app-content-padding)-var(--app-content-padding))]":e(n)&&e(a)==="cutMenu"&&e(s),"!min-h-[calc(100%-var(--top-tool-height)-var(--app-content-padding)-var(--app-content-padding)-var(--tags-view-height))]":!e(n)&&e(a)==="cutMenu"&&e(s)}])},[o(d,null,{default:$(({Component:v,route:b})=>[(M(),ee(en,{include:e(i)},[(M(),ee(_l(v),{key:b.fullPath}))],1032,["include"]))]),_:1})],2),e(s)?(M(),ee(e(ho),{key:0})):Ie("",!0)],64)}}}),fo={class:"message"},go={class:"message-list"},bo=(t=>(Vl("data-v-671022cd"),t=t(),Il(),t))(()=>h("img",{alt:"",class:"message-icon",src:El},null,-1)),xo={class:"message-content"},wo={class:"message-title"},yo={class:"message-date"},_o={style:{"margin-top":"10px","text-align":"right"}},Co=ge(j({name:"Message",__name:"Message",setup(t){const{push:l}=Pe(),a=_("notice"),n=_(0),s=_([]),r=async()=>{s.value=await Vn(),n.value=0},i=async()=>{In().then(p=>{n.value=p})},c=()=>{l({name:"MyNotifyMessage"})};return we(()=>{i(),setInterval(()=>{i()},12e4)}),(p,m)=>{const d=ie,v=tn,b=ln,V=an,I=Cn,u=on;return M(),N("div",fo,[o(u,{width:400,placement:"bottom",trigger:"click"},{reference:$(()=>[o(v,{"is-dot":e(n)>0,class:"item"},{default:$(()=>[o(d,{size:18,class:"cursor-pointer",icon:"ep:bell",onClick:r})]),_:1},8,["is-dot"])]),default:$(()=>[o(V,{modelValue:e(a),"onUpdate:modelValue":m[0]||(m[0]=g=>Y(a)?a.value=g:null)},{default:$(()=>[o(b,{label:"\u6211\u7684\u7AD9\u5185\u4FE1",name:"notice"},{default:$(()=>[h("div",go,[(M(!0),N(le,null,Fe(e(s),g=>(M(),N("div",{key:g.id,class:"message-item"},[bo,h("div",xo,[h("span",wo,B(g.templateNickname)+"\uFF1A"+B(g.templateContent),1),h("span",yo,B(e(Mn)(g.createTime)),1)])]))),128))])]),_:1})]),_:1},8,["modelValue"]),h("div",_o,[o(I,{preIcon:"ep:view",title:"\u67E5\u770B\u5168\u90E8",type:"primary",onClick:c})])]),_:1})])}}}),[["__scopeId","data-v-671022cd"]]),ko=j({name:"Collapse",__name:"Collapse",props:{color:st.string.def("")},setup(t){const{getPrefixCls:l}=X(),a=l("collapse"),n=ne(),s=f(()=>n.getCollapse),r=()=>{const i=e(s);n.setCollapse(!i)};return(i,c)=>{const p=ie;return M(),N("div",{class:A(e(a))},[o(p,{color:t.color,icon:e(s)?"ep:expand":"ep:fold",size:18,class:"cursor-pointer",onClick:r},null,8,["color","icon"])],2)}}}),Mo={class:"flex items-center"},To={class:"pl-[5px] text-14px text-[var(--top-header-text-color)] <lg:hidden"},Vo=j({name:"UserInfo",__name:"UserInfo",setup(t){const{t:l}=ye(),{wsCache:a}=Pl(),{push:n,replace:s}=Pe(),r=nn(),i=kt(),{getPrefixCls:c}=X(),p=c("user-info"),m=a.get(rt.USER),d=m.user.avatar?m.user.avatar:El,v=m.user.nickname?m.user.nickname:"Admin",b=()=>{sn.confirm(l("common.loginOutMessage"),l("common.reminder"),{confirmButtonText:l("common.ok"),cancelButtonText:l("common.cancel"),type:"warning"}).then(async()=>{await r.loginOut(),i.delAllViews(),s("/login?redirect=/index")}).catch(()=>{})},V=async()=>{n("/user/profile")},I=()=>{window.open("https://doc.iocoder.cn/")};return(u,g)=>{const k=Sn,C=ie,F=Mt,Z=Tt,U=Vt;return M(),ee(U,{class:A(["custom-hover",e(p)]),trigger:"click"},{dropdown:$(()=>[o(Z,null,{default:$(()=>[o(F,null,{default:$(()=>[o(C,{icon:"ep:tools"}),h("div",{onClick:V},B(e(l)("common.profile")),1)]),_:1}),o(F,null,{default:$(()=>[o(C,{icon:"ep:menu"}),h("div",{onClick:I},B(e(l)("common.document")),1)]),_:1}),o(F,{divided:"",onClick:b},{default:$(()=>[o(C,{icon:"ep:switch-button"}),h("div",null,B(e(l)("common.loginOut")),1)]),_:1})]),_:1})]),default:$(()=>[h("div",Mo,[o(k,{src:e(d),alt:"",class:"w-[calc(var(--logo-height)-25px)] rounded-[50%]"},null,8,["src"]),h("span",To,B(e(v)),1)])]),_:1},8,["class"])}}}),Io=j({name:"ScreenFull",__name:"Screenfull",props:{color:st.string.def("")},setup(t){const{getPrefixCls:l}=X(),a=l("screenfull"),{toggle:n,isFullscreen:s}=rn(),r=()=>{n()};return(i,c)=>(M(),N("div",{class:A(e(a)),onClick:r},[o(e(ie),{color:t.color,icon:e(s)?"zmdi:fullscreen-exit":"zmdi:fullscreen",size:18},null,8,["color","icon"])],2))}}),jt=(t,l="")=>{var n;const a=[];for(const s of t){const r=s==null?void 0:s.meta;if(r.hidden&&!r.canTo)continue;const i=r.alwaysShow||((n=s.children)==null?void 0:n.length)!==1?{...s}:{...s.children[0],path:ke(s.path,s.children[0].path)};i.path=ke(l,i.path),i.children&&(i.children=jt(i.children,i.path)),i&&a.push(i)}return a};let Rt,Ut,Ht,Nt,Ft,qt,Dt,Wt,Gt,_e,Yt,Zt,Kt,Xt,gt,Qt,Jt,Le,el,be,me,ze,xe,Q,Te,W,de,Ce,Ae,tl,bt,Oe,ll,al,xt,ol,nl;({getPrefixCls:Rt}=X()),Ut=Rt("breadcrumb"),Ht=ne(),Nt=f(()=>Ht.getBreadcrumbIcon),Ft=ge(j({name:"Breadcrumb",setup(){const{currentRoute:t}=Pe(),{t:l}=ye(),a=_([]),n=ut(),s=f(()=>{const r=n.getRouters;return jt(r)});return re(()=>t.value,r=>{r.path.startsWith("/redirect/")||(()=>{const i=t.value.matched.slice(-1)[0].path;a.value=bn(e(s),c=>c.path===i)})()},{immediate:!0}),()=>{let r;return o(Zl,{separator:"/",class:`${Ut} flex items-center h-full ml-[10px]`},{default:()=>{return[o(un,{appear:!0,"enter-active-class":"animate__animated animate__fadeInRight"},(i=r=xn(e(a)).map(c=>{const p=!c.redirect||c.redirect==="noredirect",m=c.meta;return o(Kl,{to:{path:p?"":c.path},key:c.name},{default:()=>{var d,v;return[m!=null&&m.icon&&Nt.value?o("div",{class:"flex items-center"},[o(ie,{icon:m.icon,class:"mr-[2px]",svgClass:"inline-block"},null),l((d=c==null?void 0:c.meta)==null?void 0:d.title)]):l((v=c==null?void 0:c.meta)==null?void 0:v.title)]}})}),typeof i=="function"||Object.prototype.toString.call(i)==="[object Object]"&&!Bl(i)?r:{default:()=>[r]}))];var i}})}}}),[["__scopeId","data-v-4b03c1c3"]]),qt=j({name:"SizeDropdown",__name:"SizeDropdown",props:{color:st.string.def("")},setup(t){const{getPrefixCls:l}=X(),a=l("size-dropdown"),{t:n}=ye(),s=ne(),r=f(()=>s.sizeMap),i=c=>{s.setCurrentSize(c)};return(c,p)=>{const m=ie,d=Mt,v=Tt,b=Vt;return M(),ee(b,{class:A(e(a)),trigger:"click",onCommand:i},{dropdown:$(()=>[o(v,null,{default:$(()=>[(M(!0),N(le,null,Fe(e(r),V=>(M(),ee(d,{key:V,command:V},{default:$(()=>[ce(B(e(n)(`size.${V}`)),1)]),_:2},1032,["command"]))),128))]),_:1})]),default:$(()=>[o(m,{color:t.color,size:18,class:"cursor-pointer",icon:"mdi:format-size"},null,8,["color"])]),_:1},8,["class"])}}}),{getPrefixCls:Dt,variables:Wt}=X(),Gt=Dt("tool-header"),_e=ne(),Yt=f(()=>_e.getBreadcrumb),Zt=f(()=>_e.getHamburger),Kt=f(()=>_e.getScreenfull),Xt=f(()=>_e.getSize),gt=f(()=>_e.getLayout),Qt=f(()=>_e.getLocale),Jt=f(()=>_e.getMessage),Le=ge(j({name:"ToolHeader",setup:()=>()=>o("div",{id:`${Wt.namespace}-tool-header`,class:[Gt,"h-[var(--top-tool-height)] relative px-[var(--top-tool-p-x)] flex items-center justify-between","dark:bg-[var(--el-bg-color)]"]},[gt.value!=="top"?o("div",{class:"h-full flex items-center"},[Zt.value&&gt.value!=="cutMenu"?o(ko,{class:"custom-hover",color:"var(--top-header-text-color)"},null):void 0,Yt.value?o(Ft,{class:"lt-md:hidden"},null):void 0]):void 0,o("div",{class:"h-full flex items-center"},[Kt.value?o(Io,{class:"custom-hover",color:"var(--top-header-text-color)"},null):void 0,Xt.value?o(qt,{class:"custom-hover",color:"var(--top-header-text-color)"},null):void 0,Qt.value?o(vn,{class:"custom-hover",color:"var(--top-header-text-color)"},null):void 0,Jt.value?o(Co,{class:"custom-hover",color:"var(--top-header-text-color)"},null):void 0,o(Vo,null,null)])])}),[["__scopeId","data-v-c972cb3f"]]),{getPrefixCls:el}=X(),be=el("layout"),me=ne(),ze=f(()=>me.getPageLoading),xe=f(()=>me.getTagsView),Q=f(()=>me.getCollapse),Te=f(()=>me.logo),W=f(()=>me.getFixedHeader),de=f(()=>me.getMobile),Ce=f(()=>me.getFixedMenu),Ae=()=>({renderClassic:()=>o(le,null,[o("div",{class:["absolute top-0 left-0 h-full layout-border__right",{"!fixed z-3000":de.value}]},[Te.value?o(Ze,{class:["bg-[var(--left-menu-bg-color)] relative",{"!pl-0":de.value&&Q.value,"w-[var(--left-menu-min-width)]":me.getCollapse,"w-[var(--left-menu-max-width)]":!me.getCollapse}],style:"transition: all var(--transition-time-02);"},null):void 0,o(We,{class:[{"!h-[calc(100%-var(--logo-height))]":Te.value}]},null)]),o("div",{class:[`${be}-content`,"absolute top-0 h-[100%]",{"w-[calc(100%-var(--left-menu-min-width))] left-[var(--left-menu-min-width)]":Q.value&&!de.value&&!de.value,"w-[calc(100%-var(--left-menu-max-width))] left-[var(--left-menu-max-width)]":!Q.value&&!de.value&&!de.value,"fixed !w-full !left-0":de.value}],style:"transition: all var(--transition-time-02);"},[Ne(o(Me,{class:[`${be}-content-scrollbar`,{"!h-[calc(100%-var(--top-tool-height)-var(--tags-view-height))] mt-[calc(var(--top-tool-height)+var(--tags-view-height))]":W.value}]},{default:()=>[o("div",{class:[{"fixed top-0 left-0 z-10":W.value,"w-[calc(100%-var(--left-menu-min-width))] !left-[var(--left-menu-min-width)]":Q.value&&W.value&&!de.value,"w-[calc(100%-var(--left-menu-max-width))] !left-[var(--left-menu-max-width)]":!Q.value&&W.value&&!de.value,"!w-full !left-0":de.value}],style:"transition: all var(--transition-time-02);"},[o(Le,{class:["bg-[var(--top-header-bg-color)]",{"layout-border__bottom":!xe.value}]},null),xe.value?o(Ye,{class:"layout-border__top layout-border__bottom"},null):void 0]),o(Ke,null,null)]}),[[ct("loading"),ze.value]])])]),renderTopLeft:()=>o(le,null,[o("div",{class:"relative flex items-center bg-[var(--top-header-bg-color)] layout-border__bottom dark:bg-[var(--el-bg-color)]"},[Te.value?o(Ze,{class:"custom-hover"},null):void 0,o(Le,{class:"flex-1"},null)]),o("div",{class:"absolute left-0 top-[var(--logo-height)+1px] h-[calc(100%-1px-var(--logo-height))] w-full flex"},[o(We,{class:"relative layout-border__right !h-full"},null),o("div",{class:[`${be}-content`,"h-[100%]",{"w-[calc(100%-var(--left-menu-min-width))] left-[var(--left-menu-min-width)]":Q.value,"w-[calc(100%-var(--left-menu-max-width))] left-[var(--left-menu-max-width)]":!Q.value}],style:"transition: all var(--transition-time-02);"},[Ne(o(Me,{class:[`${be}-content-scrollbar`,{"!h-[calc(100%-var(--tags-view-height))] mt-[calc(var(--tags-view-height))]":W.value&&xe.value}]},{default:()=>[xe.value?o(Ye,{class:["layout-border__bottom absolute",{"!fixed top-0 left-0 z-10":W.value,"w-[calc(100%-var(--left-menu-min-width))] !left-[var(--left-menu-min-width)] mt-[calc(var(--logo-height)+1px)]":Q.value&&W.value,"w-[calc(100%-var(--left-menu-max-width))] !left-[var(--left-menu-max-width)] mt-[calc(var(--logo-height)+1px)]":!Q.value&&W.value}],style:"transition: width var(--transition-time-02), left var(--transition-time-02);"},null):void 0,o(Ke,null,null)]}),[[ct("loading"),ze.value]])])])]),renderTop:()=>o(le,null,[o("div",{class:["flex items-center justify-between bg-[var(--top-header-bg-color)] relative",{"layout-border__bottom":!xe.value}]},[Te.value?o(Ze,{class:"custom-hover"},null):void 0,o(We,{class:"h-[var(--top-tool-height)] flex-1 px-10px"},null),o(Le,null,null)]),o("div",{class:[`${be}-content`,"w-full",{"h-[calc(100%-var(--app-footer-height))]":!W.value,"h-[calc(100%-var(--tags-view-height)-var(--app-footer-height))]":W.value}]},[Ne(o(Me,{class:[`${be}-content-scrollbar`,{"mt-[var(--tags-view-height)] !pb-[calc(var(--tags-view-height)+var(--app-footer-height))]":W.value,"pb-[var(--app-footer-height)]":!W.value}]},{default:()=>[xe.value?o(Ye,{class:["layout-border__bottom layout-border__top relative",{"!fixed w-full top-[calc(var(--top-tool-height)+1px)] left-0":W.value}],style:"transition: width var(--transition-time-02), left var(--transition-time-02);"},null):void 0,o(Ke,null,null)]}),[[ct("loading"),ze.value]])])]),renderCutMenu:()=>o(le,null,[o("div",{class:"relative flex items-center bg-[var(--top-header-bg-color)] layout-border__bottom"},[Te.value?o(Ze,{class:"custom-hover !pr-15px"},null):void 0,o(Le,{class:"flex-1"},null)]),o("div",{class:"absolute left-0 top-[var(--logo-height)] h-[calc(100%-var(--logo-height))] w-[calc(100%-2px)] flex"},[o(so,null,null),o("div",{class:[`${be}-content`,"h-[100%]",{"w-[calc(100%-var(--tab-menu-min-width))] left-[var(--tab-menu-min-width)]":Q.value&&!Ce.value,"w-[calc(100%-var(--tab-menu-max-width))] left-[var(--tab-menu-max-width)]":!Q.value&&!Ce.value,"w-[calc(100%-var(--tab-menu-min-width)-var(--left-menu-max-width))] ml-[var(--left-menu-max-width)]":Q.value&&Ce.value,"w-[calc(100%-var(--tab-menu-max-width)-var(--left-menu-max-width))] ml-[var(--left-menu-max-width)]":!Q.value&&Ce.value}],style:"transition: all var(--transition-time-02);"},[Ne(o(Me,{class:[`${be}-content-scrollbar`,{"!h-[calc(100%-var(--tags-view-height))] mt-[calc(var(--tags-view-height))]":W.value&&xe.value}]},{default:()=>[xe.value?o(Ye,{class:["relative layout-border__bottom layout-border__top",{"!fixed top-0 left-0 z-10":W.value,"w-[calc(100%-var(--tab-menu-min-width))] !left-[var(--tab-menu-min-width)] mt-[var(--logo-height)]":Q.value&&W.value,"w-[calc(100%-var(--tab-menu-max-width))] !left-[var(--tab-menu-max-width)] mt-[var(--logo-height)]":!Q.value&&W.value,"!fixed top-0 !left-[var(--tab-menu-min-width)+var(--left-menu-max-width)] z-10":W.value&&Ce.value,"w-[calc(100%-var(--tab-menu-min-width)-var(--left-menu-max-width))] !left-[var(--tab-menu-min-width)+var(--left-menu-max-width)] mt-[var(--logo-height)]":Q.value&&W.value&&Ce.value,"w-[calc(100%-var(--tab-menu-max-width)-var(--left-menu-max-width))] !left-[var(--tab-menu-max-width)+var(--left-menu-max-width)] mt-[var(--logo-height)]":!Q.value&&W.value&&Ce.value}],style:"transition: width var(--transition-time-02), left var(--transition-time-02);"},null):void 0,o(Ke,null,null)]}),[[ct("loading"),ze.value]])])])])}),{getPrefixCls:tl}=X(),bt=tl("layout"),Oe=ne(),ll=f(()=>Oe.getMobile),al=f(()=>Oe.getCollapse),xt=f(()=>Oe.getLayout),ol=()=>{Oe.setCollapse(!0)},nl=()=>{switch(e(xt)){case"classic":const{renderClassic:t}=Ae();return t();case"topLeft":const{renderTopLeft:l}=Ae();return l();case"top":const{renderTop:a}=Ae();return a();case"cutMenu":const{renderCutMenu:n}=Ae();return n()}},jl=ge(j({name:"Layout",setup:()=>()=>o("section",{class:[bt,`${bt}__${xt.value}`,"w-[100%] h-[100%] relative"]},[ll.value&&!al.value?o("div",{class:"absolute left-0 top-0 z-99 h-full w-full bg-[var(--el-color-black)] opacity-30",onClick:ol},null):void 0,nl(),o(ua,null,null),o(Qa,null,null)])}),[["__scopeId","data-v-3eace605"]])});export{Ln as __tla,jl as default};
