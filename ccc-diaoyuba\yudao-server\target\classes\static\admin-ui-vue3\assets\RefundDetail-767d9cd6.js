import{_ as t,__tla as r}from"./RefundDetail.vue_vue_type_script_setup_true_lang-afe7ed80.js";import{__tla as _}from"./Dialog.vue_vue_type_style_index_0_lang-5af0bc2b.js";import{__tla as a}from"./index-97fffa0c.js";import{__tla as l}from"./el-descriptions-item-5b1e935d.js";import{__tla as o}from"./DictTag.vue_vue_type_script_lang-5679ad7b.js";import"./color-a8b4eb58.js";import{__tla as m}from"./dict-6a82eb12.js";import{__tla as c}from"./formatTime-9d54d2c5.js";let e=Promise.all([(()=>{try{return r}catch{}})(),(()=>{try{return _}catch{}})(),(()=>{try{return a}catch{}})(),(()=>{try{return l}catch{}})(),(()=>{try{return o}catch{}})(),(()=>{try{return m}catch{}})(),(()=>{try{return c}catch{}})()]).then(async()=>{});export{e as __tla,t as default};
