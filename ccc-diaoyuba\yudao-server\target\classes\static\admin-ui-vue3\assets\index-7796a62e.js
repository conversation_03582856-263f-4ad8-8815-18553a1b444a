import{d as oa,N as na,ak as _a,e as ia,r as s,f as ca,A as ua,ai as ma,o as d,q as C,w as r,c as J,k as fa,a,F as pa,B as M,g as j,t as x,i as t,a3 as D,al as da,am as ya,an as ha,E as ka,L as va,j as f,D as ga,aj as ba,T as Ca,I as Ta,_ as wa,H as xa,__tla as Ua}from"./index-97fffa0c.js";import{_ as Va,__tla as Da}from"./ContentWrap.vue_vue_type_script_setup_true_lang-a574ccef.js";import{E as Ea,__tla as Fa}from"./el-card-6c7c099d.js";import{b as Sa}from"./formCreate-a3356cdc.js";import{a as ja,__tla as Aa}from"./index-a2bf77df.js";import{g as <PERSON>,__tla as qa}from"./index-db126fd7.js";import{g as Ba,a as Ia,r as Pa,__tla as za}from"./index-bcf141f5.js";import{_ as Na,__tla as $a}from"./TaskUpdateAssigneeForm.vue_vue_type_script_setup_true_lang-efc7d639.js";import{_ as Ha,__tla as Ka}from"./ProcessInstanceBpmnViewer.vue_vue_type_style_index_0_lang-bb764b8b.js";import{_ as La,__tla as Oa}from"./ProcessInstanceTaskList.vue_vue_type_script_setup_true_lang-c9cce538.js";import{_ as Qa,__tla as Ga}from"./TaskReturnDialogForm.vue_vue_type_script_name_TaskRollbackDialogForm_setup_true_lang-66a59aab.js";import{_ as Ja,__tla as Ma}from"./TaskDelegateForm.vue_vue_type_script_setup_true_lang-9f363768.js";import{_ as Wa,__tla as Xa}from"./TaskAddSignDialogForm.vue_vue_type_script_setup_true_lang-fdb8d49e.js";import{u as Ya,__tla as Za}from"./useMessage-18385d4a.js";import{__tla as ae}from"./Dialog.vue_vue_type_style_index_0_lang-5af0bc2b.js";import{__tla as ee}from"./index-e6297252.js";import{__tla as te}from"./bpmn-embedded-5f95dcf2.js";import{__tla as re}from"./XTextButton-41b6d860.js";import"./_plugin-vue_export-helper-1b428a4d.js";import{__tla as le}from"./XButton-dd4d8780.js";import{__tla as se}from"./el-link-f00f9c89.js";import{__tla as oe}from"./el-drawer-0535e62a.js";import{__tla as ne}from"./dict-6a82eb12.js";import{__tla as _e}from"./formatTime-9d54d2c5.js";import{__tla as ie}from"./el-timeline-item-25d1936c.js";import{__tla as ce}from"./DictTag.vue_vue_type_script_lang-5679ad7b.js";import"./color-a8b4eb58.js";import{__tla as ue}from"./ProcessInstanceChildrenTaskList.vue_vue_type_script_setup_true_lang-72c10418.js";import{__tla as me}from"./TaskSubSignDialogForm.vue_vue_type_script_name_TaskRollbackDialogForm_setup_true_lang-af6db73f.js";let W,fe=Promise.all([(()=>{try{return Ua}catch{}})(),(()=>{try{return Da}catch{}})(),(()=>{try{return Fa}catch{}})(),(()=>{try{return Aa}catch{}})(),(()=>{try{return qa}catch{}})(),(()=>{try{return za}catch{}})(),(()=>{try{return $a}catch{}})(),(()=>{try{return Ka}catch{}})(),(()=>{try{return Oa}catch{}})(),(()=>{try{return Ga}catch{}})(),(()=>{try{return Ma}catch{}})(),(()=>{try{return Xa}catch{}})(),(()=>{try{return Za}catch{}})(),(()=>{try{return ae}catch{}})(),(()=>{try{return ee}catch{}})(),(()=>{try{return te}catch{}})(),(()=>{try{return re}catch{}})(),(()=>{try{return le}catch{}})(),(()=>{try{return se}catch{}})(),(()=>{try{return oe}catch{}})(),(()=>{try{return ne}catch{}})(),(()=>{try{return _e}catch{}})(),(()=>{try{return ie}catch{}})(),(()=>{try{return ce}catch{}})(),(()=>{try{return ue}catch{}})(),(()=>{try{return me}catch{}})()]).then(async()=>{let A,R,q,B;A={class:"el-icon-picture-outline"},R={style:{"margin-bottom":"20px","margin-left":"10%","font-size":"14px"}},q={class:"el-icon-document"},B={key:1},W=oa({name:"BpmProcessInstanceDetail",__name:"index",setup(pe){const{query:X}=na(),E=Ya(),{proxy:Y}=_a(),Z=ia().getUser.id,F=X.id,T=s(!1),o=s({}),I=s(""),S=s(!0),k=s([]),U=s([]),v=s([]),aa=ca({reason:[{required:!0,message:"\u5BA1\u6279\u5EFA\u8BAE\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),V=s(),w=s({rule:[],option:{},value:{}}),P=async(n,e)=>{const l=U.value.indexOf(n),b=Y.$refs["form"+l][0],y=a(b);if(!y||!await y.validate())return;const h={id:n.id,reason:v.value[l].reason};e?(await Ia(h),E.success("\u5BA1\u6279\u901A\u8FC7\u6210\u529F")):(await Pa(h),E.success("\u5BA1\u6279\u4E0D\u901A\u8FC7\u6210\u529F")),g()},z=s(),N=s(),$=s(),H=s(),g=()=>{ea(),ta()},K=s(null),ea=async()=>{try{T.value=!0;const n=await Ra(F);if(!n)return void E.error("\u67E5\u8BE2\u4E0D\u5230\u6D41\u7A0B\u4FE1\u606F\uFF01");o.value=n;const e=n.processDefinition;e.formType===10?(Sa(w,e.formConf,e.formFields,n.formVariables),da().then(()=>{var l,b,y,h,u,m;(b=(l=V.value)==null?void 0:l.fapi)==null||b.btn.show(!1),(h=(y=V.value)==null?void 0:y.fapi)==null||h.resetBtn.show(!1),(m=(u=V.value)==null?void 0:u.fapi)==null||m.disabled(!0)})):K.value=ya(n.processDefinition.formCustomViewPath),I.value=await ja(e.id)}finally{T.value=!1}},ta=async()=>{try{S.value=!0;const n=await Ba(F);k.value=[],n.forEach(e=>{e.result!==4&&k.value.push(e)}),k.value.sort((e,l)=>e.endTime&&l.endTime?l.endTime-e.endTime:e.endTime?1:l.endTime?-1:l.createTime-e.createTime),U.value=[],v.value=[],L(k.value)}finally{S.value=!1}},L=n=>{n.forEach(e=>{ha(e.children)||L(e.children),e.result!==1&&e.result!==6||e.assigneeUser&&e.assigneeUser.id===Z&&(U.value.push({...e}),v.value.push({reason:""}))})};return ua(()=>{g()}),(n,e)=>{const l=ga,b=ba,y=Ca,h=Ta,u=wa,m=xa,O=ka,Q=Ea,ra=ma("form-create"),la=Va,G=va;return d(),C(la,null,{default:r(()=>[(d(!0),J(pa,null,fa(a(U),(i,p)=>M((d(),C(Q,{key:p,class:"box-card"},{header:r(()=>[j("span",A,"\u5BA1\u6279\u4EFB\u52A1\u3010"+x(i.name)+"\u3011",1)]),default:r(()=>[t(O,{offset:6,span:16},{default:r(()=>[t(h,{ref_for:!0,ref:"form"+p,model:a(v)[p],rules:a(aa),"label-width":"100px"},{default:r(()=>[a(o)&&a(o).name?(d(),C(l,{key:0,label:"\u6D41\u7A0B\u540D"},{default:r(()=>[f(x(a(o).name),1)]),_:1})):D("",!0),a(o)&&a(o).startUser?(d(),C(l,{key:1,label:"\u6D41\u7A0B\u53D1\u8D77\u4EBA"},{default:r(()=>[f(x(a(o).startUser.nickname)+" ",1),t(b,{size:"small",type:"info"},{default:r(()=>[f(x(a(o).startUser.deptName),1)]),_:1})]),_:1})):D("",!0),t(l,{label:"\u5BA1\u6279\u5EFA\u8BAE",prop:"reason"},{default:r(()=>[t(y,{modelValue:a(v)[p].reason,"onUpdate:modelValue":c=>a(v)[p].reason=c,placeholder:"\u8BF7\u8F93\u5165\u5BA1\u6279\u5EFA\u8BAE",type:"textarea"},null,8,["modelValue","onUpdate:modelValue"])]),_:2},1024)]),_:2},1032,["model","rules"]),j("div",R,[t(m,{type:"success",onClick:c=>P(i,!0)},{default:r(()=>[t(u,{icon:"ep:select"}),f(" \u901A\u8FC7 ")]),_:2},1032,["onClick"]),t(m,{type:"danger",onClick:c=>P(i,!1)},{default:r(()=>[t(u,{icon:"ep:close"}),f(" \u4E0D\u901A\u8FC7 ")]),_:2},1032,["onClick"]),t(m,{type:"primary",onClick:c=>{return _=i.id,void z.value.open(_);var _}},{default:r(()=>[t(u,{icon:"ep:edit"}),f(" \u8F6C\u529E ")]),_:2},1032,["onClick"]),t(m,{type:"primary",onClick:c=>(async _=>{N.value.open(_.id)})(i)},{default:r(()=>[t(u,{icon:"ep:position"}),f(" \u59D4\u6D3E ")]),_:2},1032,["onClick"]),t(m,{type:"primary",onClick:c=>(async _=>{H.value.open(_.id)})(i)},{default:r(()=>[t(u,{icon:"ep:plus"}),f(" \u52A0\u7B7E ")]),_:2},1032,["onClick"]),t(m,{type:"warning",onClick:c=>(async _=>{$.value.open(_.id)})(i)},{default:r(()=>[t(u,{icon:"ep:back"}),f(" \u56DE\u9000 ")]),_:2},1032,["onClick"])])]),_:2},1024)]),_:2},1024)),[[G,a(T)]])),128)),M((d(),C(Q,{class:"box-card"},{header:r(()=>[j("span",q,"\u7533\u8BF7\u4FE1\u606F\u3010"+x(a(o).name)+"\u3011",1)]),default:r(()=>{var i,p,c,_;return[((p=(i=a(o))==null?void 0:i.processDefinition)==null?void 0:p.formType)===10?(d(),C(O,{key:0,offset:6,span:16},{default:r(()=>[t(ra,{ref_key:"fApi",ref:V,modelValue:a(w).value,"onUpdate:modelValue":e[0]||(e[0]=sa=>a(w).value=sa),option:a(w).option,rule:a(w).rule},null,8,["modelValue","option","rule"])]),_:1})):D("",!0),((_=(c=a(o))==null?void 0:c.processDefinition)==null?void 0:_.formType)===20?(d(),J("div",B,[t(a(K),{id:a(o).businessKey},null,8,["id"])])):D("",!0)]}),_:1})),[[G,a(T)]]),t(La,{loading:a(S),tasks:a(k)},null,8,["loading","tasks"]),t(Ha,{id:`${a(F)}`,"bpmn-xml":a(I),loading:a(T),"process-instance":a(o),tasks:a(k)},null,8,["id","bpmn-xml","loading","process-instance","tasks"]),t(Na,{ref_key:"taskUpdateAssigneeFormRef",ref:z,onSuccess:g},null,512),t(Qa,{ref_key:"taskReturnDialogRef",ref:$,onSuccess:g},null,512),t(Ja,{ref_key:"taskDelegateForm",ref:N,onSuccess:g},null,512),t(Wa,{ref_key:"taskAddSignDialogForm",ref:H,onSuccess:g},null,512)]),_:1})}}})});export{fe as __tla,W as default};
