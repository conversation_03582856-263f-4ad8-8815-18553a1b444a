<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.yudao.module.diaoyuba.dal.mysql.teamcomment.TeamCommentMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="selectSubCommentsHotList"
            resultType="cn.iocoder.yudao.module.diaoyuba.dal.dataobject.teamcomment.TeamCommentDO">
        SELECT
            *
        FROM
            (
                SELECT
                    *,
                    ROW_NUMBER() OVER ( PARTITION BY team_id, root_comment_id ORDER BY like_count DESC ) AS rn
                FROM
                    diaoyuba_team_comment
                WHERE
                    team_id = #{teamId}
                    AND deleted = 0
                    AND root_comment_id IN
                    <foreach item="item" index="index" collection="rootCommentIds" open="(" separator="," close=")">
                        #{item}
                    </foreach>
            ) t
        WHERE
            t.rn = 1
    </select>
</mapper>
