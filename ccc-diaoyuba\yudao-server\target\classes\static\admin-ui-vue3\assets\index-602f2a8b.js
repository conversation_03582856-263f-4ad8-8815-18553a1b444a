import{d as ie,aN as Ae,cR as go,o as De,c as Le,g as j,a as l,aQ as Zt,aL as Me,bt as yo,b as E,b$ as Ue,aP as Rt,i as x,V as tt,au as xo,t as Ro,a3 as vo,aR as vt,ba as bo,bc as So,ak as Jt,r as B,aZ as ne,A as lt,bd as To,al as Ye,dO as Co,aG as el,h as ze,a$ as tl,bf as Ho,bm as Io,bn as bt,ax as ot,cb as Oe,aH as Eo,aY as Xe,bs as St,a_ as We,bB as Mo,aI as Oo,aU as ll,aS as V,c2 as Wo,cW as ko,ah as te,bl as ol,dP as Ko,dQ as _o,aO as al,ct as Ao,bp as Do,bH as Lo,w as at,j as zo,F as $o,_ as Vo,H as No,__tla as Go}from"./index-97fffa0c.js";import{j as ke,I as rl,S as nl,F as rt,u as Fo,B as sl,d as nt,A as st,R as jo,g as il,a as Bo,b as cl,k as dl,c as ul,e as Po,C as $e,E as Tt,f as Ct,h as hl,D as ml,v as qo,l as Uo,__tla as Yo}from"./el-virtual-list-404af680.js";import{_ as Xo,__tla as Qo}from"./ContentWrap.vue_vue_type_script_setup_true_lang-a574ccef.js";import{_ as Zo,__tla as Jo}from"./index-b39a19a1.js";import{_ as ea,__tla as ta}from"./AreaForm.vue_vue_type_script_setup_true_lang-dfad1e85.js";import{g as la,__tla as oa}from"./index-4037c090.js";import{__tla as aa}from"./el-card-6c7c099d.js";import"./_plugin-vue_export-helper-1b428a4d.js";import{__tla as ra}from"./Dialog.vue_vue_type_style_index_0_lang-5af0bc2b.js";import{__tla as na}from"./useMessage-18385d4a.js";let fl,sa=Promise.all([(()=>{try{return Go}catch{}})(),(()=>{try{return Yo}catch{}})(),(()=>{try{return Qo}catch{}})(),(()=>{try{return Jo}catch{}})(),(()=>{try{return ta}catch{}})(),(()=>{try{return oa}catch{}})(),(()=>{try{return aa}catch{}})(),(()=>{try{return ra}catch{}})(),(()=>{try{return na}catch{}})()]).then(async()=>{const pl={viewBox:"0 0 79 86",version:"1.1",xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink"},wl=["id"],gl=["stop-color"],yl=["stop-color"],xl=["id"],Rl=["stop-color"],vl=["stop-color"],bl=["id"],Sl={id:"Illustrations",stroke:"none","stroke-width":"1",fill:"none","fill-rule":"evenodd"},Tl={id:"B-type",transform:"translate(-1268.000000, -535.000000)"},Cl={id:"Group-2",transform:"translate(1268.000000, 535.000000)"},Hl=["fill"],Il=["fill"],El={id:"Group-Copy",transform:"translate(34.500000, 31.500000) scale(-1, 1) rotate(-25.000000) translate(-34.500000, -31.500000) translate(7.000000, 10.000000)"},Ml=["fill"],Ol=["fill"],Wl=["fill"],kl=["fill"],Kl=["fill"],_l={id:"Rectangle-Copy-17",transform:"translate(53.000000, 45.000000)"},Al=["fill","xlink:href"],Dl=["fill","mask"],Ll=["fill"],zl=ie({name:"ImgEmpty"});var $l=Zt(ie({...zl,setup(e){const t=Ae("empty"),o=go();return(a,r)=>(De(),Le("svg",pl,[j("defs",null,[j("linearGradient",{id:`linearGradient-1-${l(o)}`,x1:"38.8503086%",y1:"0%",x2:"61.1496914%",y2:"100%"},[j("stop",{"stop-color":`var(${l(t).cssVarBlockName("fill-color-1")})`,offset:"0%"},null,8,gl),j("stop",{"stop-color":`var(${l(t).cssVarBlockName("fill-color-4")})`,offset:"100%"},null,8,yl)],8,wl),j("linearGradient",{id:`linearGradient-2-${l(o)}`,x1:"0%",y1:"9.5%",x2:"100%",y2:"90.5%"},[j("stop",{"stop-color":`var(${l(t).cssVarBlockName("fill-color-1")})`,offset:"0%"},null,8,Rl),j("stop",{"stop-color":`var(${l(t).cssVarBlockName("fill-color-6")})`,offset:"100%"},null,8,vl)],8,xl),j("rect",{id:`path-3-${l(o)}`,x:"0",y:"0",width:"17",height:"36"},null,8,bl)]),j("g",Sl,[j("g",Tl,[j("g",Cl,[j("path",{id:"Oval-Copy-2",d:"M39.5,86 C61.3152476,86 79,83.9106622 79,81.3333333 C79,78.7560045 57.3152476,78 35.5,78 C13.6847524,78 0,78.7560045 0,81.3333333 C0,83.9106622 17.6847524,86 39.5,86 Z",fill:`var(${l(t).cssVarBlockName("fill-color-3")})`},null,8,Hl),j("polygon",{id:"Rectangle-Copy-14",fill:`var(${l(t).cssVarBlockName("fill-color-7")})`,transform:"translate(27.500000, 51.500000) scale(1, -1) translate(-27.500000, -51.500000) ",points:"13 58 53 58 42 45 2 45"},null,8,Il),j("g",El,[j("polygon",{id:"Rectangle-Copy-10",fill:`var(${l(t).cssVarBlockName("fill-color-7")})`,transform:"translate(11.500000, 5.000000) scale(1, -1) translate(-11.500000, -5.000000) ",points:"2.84078316e-14 3 18 3 23 7 5 7"},null,8,Ml),j("polygon",{id:"Rectangle-Copy-11",fill:`var(${l(t).cssVarBlockName("fill-color-5")})`,points:"-3.69149156e-15 7 38 7 38 43 -3.69149156e-15 43"},null,8,Ol),j("rect",{id:"Rectangle-Copy-12",fill:`url(#linearGradient-1-${l(o)})`,transform:"translate(46.500000, 25.000000) scale(-1, 1) translate(-46.500000, -25.000000) ",x:"38",y:"7",width:"17",height:"36"},null,8,Wl),j("polygon",{id:"Rectangle-Copy-13",fill:`var(${l(t).cssVarBlockName("fill-color-2")})`,transform:"translate(39.500000, 3.500000) scale(-1, 1) translate(-39.500000, -3.500000) ",points:"24 7 41 7 55 -3.63806207e-12 38 -3.63806207e-12"},null,8,kl)]),j("rect",{id:"Rectangle-Copy-15",fill:`url(#linearGradient-2-${l(o)})`,x:"13",y:"45",width:"40",height:"36"},null,8,Kl),j("g",_l,[j("use",{id:"Mask",fill:`var(${l(t).cssVarBlockName("fill-color-8")})`,transform:"translate(8.500000, 18.000000) scale(-1, 1) translate(-8.500000, -18.000000) ","xlink:href":`#path-3-${l(o)}`},null,8,Al),j("polygon",{id:"Rectangle-Copy",fill:`var(${l(t).cssVarBlockName("fill-color-9")})`,mask:`url(#mask-4-${l(o)})`,transform:"translate(12.000000, 9.000000) scale(-1, 1) translate(-12.000000, -9.000000) ",points:"7 0 24 0 20 18 7 16.5"},null,8,Dl)]),j("polygon",{id:"Rectangle-Copy-18",fill:`var(${l(t).cssVarBlockName("fill-color-2")})`,transform:"translate(66.000000, 51.500000) scale(-1, 1) translate(-66.000000, -51.500000) ",points:"62 45 79 45 70 58 53 58"},null,8,Ll)])])])]))}}),[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/empty/src/img-empty.vue"]]);const Vl=Me({image:{type:String,default:""},imageSize:Number,description:{type:String,default:""}}),Nl=["src"],Gl={key:1},Fl=ie({name:"ElEmpty"}),jl=vt(Zt(ie({...Fl,props:Vl,setup(e){const t=e,{t:o}=yo(),a=Ae("empty"),r=E(()=>t.description||o("el.table.emptyText")),n=E(()=>({width:Ue(t.imageSize)}));return(s,i)=>(De(),Le("div",{class:tt(l(a).b())},[j("div",{class:tt(l(a).e("image")),style:xo(l(n))},[s.image?(De(),Le("img",{key:0,src:s.image,ondragstart:"return false"},null,8,Nl)):Rt(s.$slots,"image",{key:1},()=>[x($l)])],6),j("div",{class:tt(l(a).e("description"))},[s.$slots.description?Rt(s.$slots,"description",{key:0}):(De(),Le("p",Gl,Ro(l(r)),1))],2),s.$slots.default?(De(),Le("div",{key:0,class:tt(l(a).e("bottom"))},[Rt(s.$slots,"default")],2)):vo("v-if",!0)],2))}}),[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/empty/src/empty.vue"]])),Ht=({name:e,clearCache:t,getColumnPosition:o,getColumnStartIndexForOffset:a,getColumnStopIndexForStartIndex:r,getEstimatedTotalHeight:n,getEstimatedTotalWidth:s,getColumnOffset:i,getRowOffset:h,getRowPosition:u,getRowStartIndexForOffset:d,getRowStopIndexForStartIndex:m,initCache:T,injectToInstance:A,validateProps:M})=>ie({name:e??"ElVirtualList",props:ke,emits:[rl,nl],setup(c,{emit:G,expose:P,slots:$}){const y=Ae("vl");M(c);const K=Jt(),W=B(T(c,K));A==null||A(K,W);const N=B(),q=B(),U=B(),Y=B(null),k=B({isScrolling:!1,scrollLeft:ne(c.initScrollLeft)?c.initScrollLeft:0,scrollTop:ne(c.initScrollTop)?c.initScrollTop:0,updateRequested:!1,xAxisScrollDir:rt,yAxisScrollDir:rt}),O=Fo(),L=E(()=>Number.parseInt(`${c.height}`,10)),F=E(()=>Number.parseInt(`${c.width}`,10)),Q=E(()=>{const{totalColumn:w,totalRow:p,columnCache:g}=c,{isScrolling:C,xAxisScrollDir:I,scrollLeft:S}=l(k);if(w===0||p===0)return[0,0,0,0];const R=a(c,S,l(W)),f=r(c,R,S,l(W)),H=C&&I!==sl?1:Math.max(1,g),_=C&&I!==rt?1:Math.max(1,g);return[Math.max(0,R-H),Math.max(0,Math.min(w-1,f+_)),R,f]}),ce=E(()=>{const{totalColumn:w,totalRow:p,rowCache:g}=c,{isScrolling:C,yAxisScrollDir:I,scrollTop:S}=l(k);if(w===0||p===0)return[0,0,0,0];const R=d(c,S,l(W)),f=m(c,R,S,l(W)),H=C&&I!==sl?1:Math.max(1,g),_=C&&I!==rt?1:Math.max(1,g);return[Math.max(0,R-H),Math.max(0,Math.min(p-1,f+_)),R,f]}),se=E(()=>n(c,l(W))),le=E(()=>s(c,l(W))),ge=E(()=>{var w;return[{position:"relative",overflow:"hidden",WebkitOverflowScrolling:"touch",willChange:"transform"},{direction:c.direction,height:ne(c.height)?`${c.height}px`:c.height,width:ne(c.width)?`${c.width}px`:c.width},(w=c.style)!=null?w:{}]}),Se=E(()=>{const w=`${l(le)}px`;return{height:`${l(se)}px`,pointerEvents:l(k).isScrolling?"none":void 0,width:w}}),de=()=>{const{totalColumn:w,totalRow:p}=c;if(w>0&&p>0){const[f,H,_,v]=l(Q),[b,D,Z,ee]=l(ce);G(rl,{columnCacheStart:f,columnCacheEnd:H,rowCacheStart:b,rowCacheEnd:D,columnVisibleStart:_,columnVisibleEnd:v,rowVisibleStart:Z,rowVisibleEnd:ee})}const{scrollLeft:g,scrollTop:C,updateRequested:I,xAxisScrollDir:S,yAxisScrollDir:R}=l(k);G(nl,{xAxisScrollDir:S,scrollLeft:g,yAxisScrollDir:R,scrollTop:C,updateRequested:I})},Te=w=>{const{clientHeight:p,clientWidth:g,scrollHeight:C,scrollLeft:I,scrollTop:S,scrollWidth:R}=w.currentTarget,f=l(k);if(f.scrollTop===S&&f.scrollLeft===I)return;let H=I;if(dl(c.direction))switch(il()){case cl:H=-I;break;case Po:H=R-g-I}k.value={...f,isScrolling:!0,scrollLeft:H,scrollTop:Math.max(0,Math.min(S,C-p)),updateRequested:!0,xAxisScrollDir:nt(f.scrollLeft,H),yAxisScrollDir:nt(f.scrollTop,S)},Ye(()=>fe()),He(),de()},Ce=(w,p)=>{const g=l(L),C=(se.value-g)/p*w;re({scrollTop:Math.min(se.value-g,C)})},ye=(w,p)=>{const g=l(F),C=(le.value-g)/p*w;re({scrollLeft:Math.min(le.value-g,C)})},{onWheel:me}=(({atXEndEdge:w,atXStartEdge:p,atYEndEdge:g,atYStartEdge:C},I)=>{let S=null,R=0,f=0;const H=(_,v)=>{const b=_<=0&&p.value||_>=0&&w.value,D=v<=0&&C.value||v>=0&&g.value;return b&&D};return{hasReachedEdge:H,onWheel:_=>{bo(S);let v=_.deltaX,b=_.deltaY;Math.abs(v)>Math.abs(b)?b=0:v=0,_.shiftKey&&b!==0&&(v=b,b=0),H(R,f)&&H(R+v,f+b)||(R+=v,f+=b,_.preventDefault(),S=So(()=>{I(R,f),R=0,f=0}))}}})({atXStartEdge:E(()=>k.value.scrollLeft<=0),atXEndEdge:E(()=>k.value.scrollLeft>=le.value-l(F)),atYStartEdge:E(()=>k.value.scrollTop<=0),atYEndEdge:E(()=>k.value.scrollTop>=se.value-l(L))},(w,p)=>{var g,C,I,S;(C=(g=q.value)==null?void 0:g.onMouseUp)==null||C.call(g),(S=(I=U.value)==null?void 0:I.onMouseUp)==null||S.call(I);const R=l(F),f=l(L);re({scrollLeft:Math.min(k.value.scrollLeft+w,le.value-R),scrollTop:Math.min(k.value.scrollTop+p,se.value-f)})}),re=({scrollLeft:w=k.value.scrollLeft,scrollTop:p=k.value.scrollTop})=>{w=Math.max(w,0),p=Math.max(p,0);const g=l(k);p===g.scrollTop&&w===g.scrollLeft||(k.value={...g,xAxisScrollDir:nt(g.scrollLeft,w),yAxisScrollDir:nt(g.scrollTop,p),scrollLeft:w,scrollTop:p,updateRequested:!0},Ye(()=>fe()),He(),de())},Be=(w,p)=>{const{columnWidth:g,direction:C,rowHeight:I}=c,S=O.value(t&&g,t&&I,t&&C),R=`${w},${p}`;if(Ho(S,R))return S[R];{const[,f]=o(c,p,l(W)),H=l(W),_=dl(C),[v,b]=u(c,w,H),[D]=o(c,p,H);return S[R]={position:"absolute",left:_?void 0:`${f}px`,right:_?`${f}px`:void 0,top:`${b}px`,height:`${v}px`,width:`${D}px`},S[R]}},fe=()=>{k.value.isScrolling=!1,Ye(()=>{O.value(-1,null,null)})};lt(()=>{if(!To)return;const{initScrollLeft:w,initScrollTop:p}=c,g=l(N);g&&(ne(w)&&(g.scrollLeft=w),ne(p)&&(g.scrollTop=p)),de()});const He=()=>{const{direction:w}=c,{scrollLeft:p,scrollTop:g,updateRequested:C}=l(k),I=l(N);if(C&&I){if(w===jo)switch(il()){case cl:I.scrollLeft=-p;break;case Bo:I.scrollLeft=p;break;default:{const{clientWidth:S,scrollWidth:R}=I;I.scrollLeft=R-S-p;break}}else I.scrollLeft=Math.max(0,p);I.scrollTop=Math.max(0,g)}},{resetAfterColumnIndex:Pe,resetAfterRowIndex:Ie,resetAfter:z}=K.proxy;P({windowRef:N,innerRef:Y,getItemStyleCache:O,scrollTo:re,scrollToItem:(w=0,p=0,g=st)=>{const C=l(k);p=Math.max(0,Math.min(p,c.totalColumn-1)),w=Math.max(0,Math.min(w,c.totalRow-1));const I=Co(y.namespace.value),S=l(W),R=n(c,S),f=s(c,S);re({scrollLeft:i(c,p,g,C.scrollLeft,S,f>c.width?I:0),scrollTop:h(c,w,g,C.scrollTop,S,R>c.height?I:0)})},states:k,resetAfterColumnIndex:Pe,resetAfterRowIndex:Ie,resetAfter:z});const X=()=>{const w=el(c.innerElement),p=(()=>{var g;const[C,I]=l(Q),[S,R]=l(ce),{data:f,totalColumn:H,totalRow:_,useIsScrolling:v,itemKey:b}=c,D=[];if(_>0&&H>0)for(let Z=S;Z<=R;Z++)for(let ee=C;ee<=I;ee++)D.push((g=$.default)==null?void 0:g.call($,{columnIndex:ee,data:f,key:b({columnIndex:ee,data:f,rowIndex:Z}),isScrolling:v?l(k).isScrolling:void 0,style:Be(Z,ee),rowIndex:Z}));return D})();return[ze(w,{style:l(Se),ref:Y},tl(w)?p:{default:()=>p})]};return()=>{const w=el(c.containerElement),{horizontalScrollbar:p,verticalScrollbar:g}=(()=>{const{scrollbarAlwaysOn:I,scrollbarStartGap:S,scrollbarEndGap:R,totalColumn:f,totalRow:H}=c,_=l(F),v=l(L),b=l(le),D=l(se),{scrollLeft:Z,scrollTop:ee}=l(k);return{horizontalScrollbar:ze(ul,{ref:q,alwaysOn:I,startGap:S,endGap:R,class:y.e("horizontal"),clientSize:_,layout:"horizontal",onScroll:ye,ratio:100*_/b,scrollFrom:Z/(b-_),total:H,visible:!0}),verticalScrollbar:ze(ul,{ref:U,alwaysOn:I,startGap:S,endGap:R,class:y.e("vertical"),clientSize:v,layout:"vertical",onScroll:Ce,ratio:100*v/D,scrollFrom:ee/(D-v),total:f,visible:!0})}})(),C=X();return ze("div",{key:0,class:y.e("wrapper"),role:c.role},[ze(w,{class:c.className,style:l(ge),onScroll:Te,onWheel:me,ref:N},tl(w)?C:{default:()=>C}),p,g])}}}),Bl=Ht({name:"ElFixedSizeGrid",getColumnPosition:({columnWidth:e},t)=>[e,t*e],getRowPosition:({rowHeight:e},t)=>[e,t*e],getEstimatedTotalHeight:({totalRow:e,rowHeight:t})=>t*e,getEstimatedTotalWidth:({totalColumn:e,columnWidth:t})=>t*e,getColumnOffset:({totalColumn:e,columnWidth:t,width:o},a,r,n,s,i)=>{o=Number(o);const h=Math.max(0,e*t-o),u=Math.min(h,a*t),d=Math.max(0,a*t-o+i+t);switch(r==="smart"&&(r=n>=d-o&&n<=u+o?st:$e),r){case Ct:return u;case Tt:return d;case $e:{const m=Math.round(d+(u-d)/2);return m<Math.ceil(o/2)?0:m>h+Math.floor(o/2)?h:m}default:return n>=d&&n<=u?n:d>u||n<d?d:u}},getRowOffset:({rowHeight:e,height:t,totalRow:o},a,r,n,s,i)=>{t=Number(t);const h=Math.max(0,o*e-t),u=Math.min(h,a*e),d=Math.max(0,a*e-t+i+e);switch(r===hl&&(r=n>=d-t&&n<=u+t?st:$e),r){case Ct:return u;case Tt:return d;case $e:{const m=Math.round(d+(u-d)/2);return m<Math.ceil(t/2)?0:m>h+Math.floor(t/2)?h:m}default:return n>=d&&n<=u?n:d>u||n<d?d:u}},getColumnStartIndexForOffset:({columnWidth:e,totalColumn:t},o)=>Math.max(0,Math.min(t-1,Math.floor(o/e))),getColumnStopIndexForStartIndex:({columnWidth:e,totalColumn:t,width:o},a,r)=>{const n=a*e,s=Math.ceil((o+r-n)/e);return Math.max(0,Math.min(t-1,a+s-1))},getRowStartIndexForOffset:({rowHeight:e,totalRow:t},o)=>Math.max(0,Math.min(t-1,Math.floor(o/e))),getRowStopIndexForStartIndex:({rowHeight:e,totalRow:t,height:o},a,r)=>{const n=a*e,s=Math.ceil((o+r-n)/e);return Math.max(0,Math.min(t-1,a+s-1))},initCache:()=>{},clearCache:!0,validateProps:({columnWidth:e,rowHeight:t})=>{}}),{max:Qe,min:It,floor:Et}=Math,Pl={column:"columnWidth",row:"rowHeight"},it={column:"lastVisitedColumnIndex",row:"lastVisitedRowIndex"},he=(e,t,o,a)=>{const[r,n,s]=[o[a],e[Pl[a]],o[it[a]]];if(t>s){let i=0;if(s>=0){const h=r[s];i=h.offset+h.size}for(let h=s+1;h<=t;h++){const u=n(h);r[h]={offset:i,size:u},i+=u}o[it[a]]=t}return r[t]},Mt=(e,t,o,a,r,n)=>{for(;o<=a;){const s=o+Et((a-o)/2),i=he(e,s,t,n).offset;if(i===r)return s;i<r?o=s+1:a=s-1}return Qe(0,o-1)},Ot=(e,t,o,a)=>{const[r,n]=[t[a],t[it[a]]];return(n>0?r[n].offset:0)>=o?Mt(e,t,0,n,o,a):((s,i,h,u,d)=>{const m=d==="column"?s.totalColumn:s.totalRow;let T=1;for(;h<m&&he(s,h,i,d).offset<u;)h+=T,T*=2;return Mt(s,i,Et(h/2),It(h,m-1),u,d)})(e,t,Qe(0,n),o,a)},Wt=({totalRow:e},{estimatedRowHeight:t,lastVisitedRowIndex:o,row:a})=>{let r=0;if(o>=e&&(o=e-1),o>=0){const n=a[o];r=n.offset+n.size}return r+(e-o-1)*t},kt=({totalColumn:e},{column:t,estimatedColumnWidth:o,lastVisitedColumnIndex:a})=>{let r=0;if(a>e&&(a=e-1),a>=0){const n=t[a];r=n.offset+n.size}return r+(e-a-1)*o},ql={column:kt,row:Wt},Kt=(e,t,o,a,r,n,s)=>{const[i,h]=[n==="row"?e.height:e.width,ql[n]],u=he(e,t,r,n),d=h(e,r),m=Qe(0,It(d-i,u.offset)),T=Qe(0,u.offset-i+s+u.size);switch(o===hl&&(o=a>=T-i&&a<=m+i?st:$e),o){case Ct:return m;case Tt:return T;case $e:return Math.round(T+(m-T)/2);default:return a>=T&&a<=m?a:T>m||a<T?T:m}},Ul=Ht({name:"ElDynamicSizeGrid",getColumnPosition:(e,t,o)=>{const a=he(e,t,o,"column");return[a.size,a.offset]},getRowPosition:(e,t,o)=>{const a=he(e,t,o,"row");return[a.size,a.offset]},getColumnOffset:(e,t,o,a,r,n)=>Kt(e,t,o,a,r,"column",n),getRowOffset:(e,t,o,a,r,n)=>Kt(e,t,o,a,r,"row",n),getColumnStartIndexForOffset:(e,t,o)=>Ot(e,o,t,"column"),getColumnStopIndexForStartIndex:(e,t,o,a)=>{const r=he(e,t,a,"column"),n=o+e.width;let s=r.offset+r.size,i=t;for(;i<e.totalColumn-1&&s<n;)i++,s+=he(e,t,a,"column").size;return i},getEstimatedTotalHeight:Wt,getEstimatedTotalWidth:kt,getRowStartIndexForOffset:(e,t,o)=>Ot(e,o,t,"row"),getRowStopIndexForStartIndex:(e,t,o,a)=>{const{totalRow:r,height:n}=e,s=he(e,t,a,"row"),i=o+n;let h=s.size+s.offset,u=t;for(;u<r-1&&h<i;)u++,h+=he(e,u,a,"row").size;return u},injectToInstance:(e,t)=>{const o=({columnIndex:a,rowIndex:r},n)=>{var s,i;n=!!Io(n)||n,ne(a)&&(t.value.lastVisitedColumnIndex=Math.min(t.value.lastVisitedColumnIndex,a-1)),ne(r)&&(t.value.lastVisitedRowIndex=Math.min(t.value.lastVisitedRowIndex,r-1)),(s=e.exposed)==null||s.getItemStyleCache.value(-1,null,null),n&&((i=e.proxy)==null||i.$forceUpdate())};Object.assign(e.proxy,{resetAfterColumnIndex:(a,r)=>{o({columnIndex:a},r)},resetAfterRowIndex:(a,r)=>{o({rowIndex:a},r)},resetAfter:o})},initCache:({estimatedColumnWidth:e=ml,estimatedRowHeight:t=ml})=>({column:{},estimatedColumnWidth:e,estimatedRowHeight:t,lastVisitedColumnIndex:-1,lastVisitedRowIndex:-1,row:{}}),clearCache:!1,validateProps:({columnWidth:e,rowHeight:t})=>{}});var Ve=(e=>(e.ASC="asc",e.DESC="desc",e))(Ve||{}),Ne=(e=>(e.CENTER="center",e.RIGHT="right",e))(Ne||{}),_t=(e=>(e.LEFT="left",e.RIGHT="right",e))(_t||{});const ct={asc:"desc",desc:"asc"},Ge=Symbol("placeholder"),Yl=(e,t,o)=>{var a;const r={flexGrow:0,flexShrink:0,...o?{}:{flexGrow:e.flexGrow||0,flexShrink:e.flexShrink||1}};o||(r.flexShrink=1);const n={...(a=e.style)!=null?a:{},...r,flexBasis:"auto",width:e.width};return t||(e.maxWidth&&(n.maxWidth=e.maxWidth),e.minWidth&&(n.minWidth=e.minWidth)),n},Xl=(e,{mainTableRef:t,leftTableRef:o,rightTableRef:a})=>{const r=Jt(),{emit:n}=r,s=Oe(!1),i=Oe(null),h=B(e.defaultExpandedRowKeys||[]),u=B(-1),d=Oe(null),m=B({}),T=B({}),A=Oe({}),M=Oe({}),c=Oe({}),G=E(()=>ne(e.estimatedRowHeight)),P=Eo(()=>{var y,K,W,N;s.value=!0,m.value={...l(m),...l(T)},$(l(d),!1),T.value={},d.value=null,(y=t.value)==null||y.forceUpdate(),(K=o.value)==null||K.forceUpdate(),(W=a.value)==null||W.forceUpdate(),(N=r.proxy)==null||N.$forceUpdate(),s.value=!1},0);function $(y,K=!1){l(G)&&[t,o,a].forEach(W=>{const N=l(W);N&&N.resetAfterRowIndex(y,K)})}return{hoveringRowKey:i,expandedRowKeys:h,lastRenderedRowIndex:u,isDynamic:G,isResetting:s,rowHeights:m,resetAfterIndex:$,onRowExpanded:function({expanded:y,rowData:K,rowIndex:W,rowKey:N}){var q,U;const Y=[...l(h)],k=Y.indexOf(N);y?k===-1&&Y.push(N):k>-1&&Y.splice(k,1),h.value=Y,n("update:expandedRowKeys",Y),(q=e.onRowExpand)==null||q.call(e,{expanded:y,rowData:K,rowIndex:W,rowKey:N}),(U=e.onExpandedRowsChange)==null||U.call(e,Y)},onRowHovered:function({hovered:y,rowKey:K}){i.value=y?K:null},onRowsRendered:function(y){var K;(K=e.onRowsRendered)==null||K.call(e,y),y.rowCacheEnd>l(u)&&(u.value=y.rowCacheEnd)},onRowHeightChange:function({rowKey:y,height:K,rowIndex:W},N){N?N===_t.RIGHT?c.value[y]=K:A.value[y]=K:M.value[y]=K;const q=Math.max(...[A,c,M].map(U=>U.value[y]||0));l(m)[y]!==q&&(function(U,Y,k){const O=l(d);(O===null||O>k)&&(d.value=k),T.value[U]=Y}(y,q,W),P())}}},Ql=(e,t)=>e+t,Ze=e=>Xe(e)?e.reduce(Ql,0):e,Re=(e,t,o={})=>St(e)?e(t):e??o,we=e=>(["width","maxWidth","minWidth","height"].forEach(t=>{e[t]=Ue(e[t])}),e),At=e=>We(e)?t=>ze(e,t):e;function Zl(e){const t=B(),o=B(),a=B(),{columns:r,columnsStyles:n,columnsTotalWidth:s,fixedColumnsOnLeft:i,fixedColumnsOnRight:h,hasFixedColumns:u,mainColumns:d,onColumnSorted:m}=function(z,X,w){const p=E(()=>l(X).filter(v=>!v.hidden)),g=E(()=>l(p).filter(v=>v.fixed==="left"||v.fixed===!0)),C=E(()=>l(p).filter(v=>v.fixed==="right")),I=E(()=>l(p).filter(v=>!v.fixed)),S=E(()=>{const v=[];return l(g).forEach(b=>{v.push({...b,placeholderSign:Ge})}),l(I).forEach(b=>{v.push(b)}),l(C).forEach(b=>{v.push({...b,placeholderSign:Ge})}),v}),R=E(()=>l(g).length||l(C).length),f=E(()=>l(X).reduce((v,b)=>(v[b.key]=Yl(b,l(w),z.fixed),v),{})),H=E(()=>l(p).reduce((v,b)=>v+b.width,0)),_=v=>l(X).find(b=>b.key===v);return{columns:X,columnsStyles:f,columnsTotalWidth:H,fixedColumnsOnLeft:g,fixedColumnsOnRight:C,hasFixedColumns:R,mainColumns:S,normalColumns:I,visibleColumns:p,getColumn:_,getColumnStyle:v=>l(f)[v],updateColumnWidth:(v,b)=>{v.width=b},onColumnSorted:function(v){var b;const{key:D}=v.currentTarget.dataset;if(!D)return;const{sortState:Z,sortBy:ee}=z;let Ke=Ve.ASC;Ke=bt(Z)?ct[Z[D]]:ct[ee.order],(b=z.onColumnSort)==null||b.call(z,{column:_(D),key:D,order:Ke})}}}(e,ll(e,"columns"),ll(e,"fixed")),{scrollTo:T,scrollToLeft:A,scrollToTop:M,scrollToRow:c,onScroll:G,onVerticalScroll:P,scrollPos:$}=((z,{mainTableRef:X,leftTableRef:w,rightTableRef:p,onMaybeEndReached:g})=>{const C=B({scrollLeft:0,scrollTop:0});function I(f){var H,_,v;const{scrollTop:b}=f;(H=X.value)==null||H.scrollTo(f),(_=w.value)==null||_.scrollToTop(b),(v=p.value)==null||v.scrollToTop(b)}function S(f){C.value=f,I(f)}function R(f){C.value.scrollTop=f,I(l(C))}return ot(()=>l(C).scrollTop,(f,H)=>{f>H&&g()}),{scrollPos:C,scrollTo:S,scrollToLeft:function(f){var H,_;C.value.scrollLeft=f,(_=(H=X.value)==null?void 0:H.scrollTo)==null||_.call(H,l(C))},scrollToTop:R,scrollToRow:function(f,H="auto"){var _;(_=X.value)==null||_.scrollToRow(f,H)},onScroll:function(f){var H;S(f),(H=z.onScroll)==null||H.call(z,f)},onVerticalScroll:function({scrollTop:f}){const{scrollTop:H}=l(C);f!==H&&R(f)}}})(e,{mainTableRef:t,leftTableRef:o,rightTableRef:a,onMaybeEndReached:function(){const{onEndReached:z}=e;if(!z)return;const{scrollTop:X}=l($),w=l(Ce),p=l(ye),g=w-(X+p)+e.hScrollbarSize;l(W)>=0&&w===X+l(ge)-l(fe)&&z(g)}}),{expandedRowKeys:y,hoveringRowKey:K,lastRenderedRowIndex:W,isDynamic:N,isResetting:q,rowHeights:U,resetAfterIndex:Y,onRowExpanded:k,onRowHeightChange:O,onRowHovered:L,onRowsRendered:F}=Xl(e,{mainTableRef:t,leftTableRef:o,rightTableRef:a}),{data:Q,depthMap:ce}=((z,{expandedRowKeys:X,lastRenderedRowIndex:w,resetAfterIndex:p})=>{const g=B({}),C=E(()=>{const S={},{data:R,rowKey:f}=z,H=l(X);if(!H||!H.length)return R;const _=[],v=new Set;H.forEach(D=>v.add(D));let b=R.slice();for(b.forEach(D=>S[D[f]]=0);b.length>0;){const D=b.shift();_.push(D),v.has(D[f])&&Array.isArray(D.children)&&D.children.length>0&&(b=[...D.children,...b],D.children.forEach(Z=>S[Z[f]]=S[D[f]]+1))}return g.value=S,_}),I=E(()=>{const{data:S,expandColumnKey:R}=z;return R?l(C):S});return ot(I,(S,R)=>{S!==R&&(w.value=-1,p(0,!0))}),{data:I,depthMap:g}})(e,{expandedRowKeys:y,lastRenderedRowIndex:W,resetAfterIndex:Y}),{bodyWidth:se,fixedTableHeight:le,mainTableHeight:ge,leftTableWidth:Se,rightTableWidth:de,headerWidth:Te,rowsHeight:Ce,windowHeight:ye,footerHeight:me,emptyStyle:re,rootStyle:Be,headerHeight:fe}=((z,{columnsTotalWidth:X,data:w,fixedColumnsOnLeft:p,fixedColumnsOnRight:g})=>{const C=E(()=>{const{fixed:J,width:ae,vScrollbarSize:oe}=z,_e=ae-oe;return J?Math.max(Math.round(l(X)),_e):_e}),I=E(()=>l(C)+(z.fixed?z.vScrollbarSize:0)),S=E(()=>{const{height:J=0,maxHeight:ae=0,footerHeight:oe,hScrollbarSize:_e}=z;if(ae>0){const xe=l(D),yt=l(R),et=l(b)+xe+yt+_e;return Math.min(et,ae-oe)}return J-oe}),R=E(()=>{const{rowHeight:J,estimatedRowHeight:ae}=z,oe=l(w);return ne(ae)?oe.length*ae:oe.length*J}),f=E(()=>{const{maxHeight:J}=z,ae=l(S);if(ne(J)&&J>0)return ae;const oe=l(R)+l(b)+l(D);return Math.min(ae,oe)}),H=J=>J.width,_=E(()=>Ze(l(p).map(H))),v=E(()=>Ze(l(g).map(H))),b=E(()=>Ze(z.headerHeight)),D=E(()=>{var J;return(((J=z.fixedData)==null?void 0:J.length)||0)*z.rowHeight}),Z=E(()=>l(S)-l(b)-l(D)),ee=E(()=>{const{style:J={},height:ae,width:oe}=z;return we({...J,height:ae,width:oe})}),Ke=E(()=>we({height:z.footerHeight})),qe=E(()=>({top:Ue(l(b)),bottom:Ue(z.footerHeight),width:Ue(z.width)}));return{bodyWidth:C,fixedTableHeight:f,mainTableHeight:S,leftTableWidth:_,rightTableWidth:v,headerWidth:I,rowsHeight:R,windowHeight:Z,footerHeight:Ke,emptyStyle:qe,rootStyle:ee,headerHeight:b}})(e,{columnsTotalWidth:s,data:Q,fixedColumnsOnLeft:i,fixedColumnsOnRight:h}),He=Oe(!1),Pe=B(),Ie=E(()=>{const z=l(Q).length===0;return Xe(e.fixedData)?e.fixedData.length===0&&z:z});return ot(()=>e.expandedRowKeys,z=>y.value=z,{deep:!0}),{columns:r,containerRef:Pe,mainTableRef:t,leftTableRef:o,rightTableRef:a,isDynamic:N,isResetting:q,isScrolling:He,hoveringRowKey:K,hasFixedColumns:u,columnsStyles:n,columnsTotalWidth:s,data:Q,expandedRowKeys:y,depthMap:ce,fixedColumnsOnLeft:i,fixedColumnsOnRight:h,mainColumns:d,bodyWidth:se,emptyStyle:re,rootStyle:Be,headerWidth:Te,footerHeight:me,mainTableHeight:ge,fixedTableHeight:le,leftTableWidth:Se,rightTableWidth:de,showEmpty:Ie,getRowHeight:function(z){const{estimatedRowHeight:X,rowHeight:w,rowKey:p}=e;return X?l(U)[l(Q)[z][p]]||X:w},onColumnSorted:m,onRowHovered:L,onRowExpanded:k,onRowsRendered:F,onRowHeightChange:O,scrollTo:T,scrollToLeft:A,scrollToTop:M,scrollToRow:c,onScroll:G,onVerticalScroll:P}}const dt=Symbol("tableV2"),Dt=String,Fe={type:V(Array),required:!0},ut={type:V(Array)},Lt={...ut,required:!0},Jl=String,zt={type:V(Array),default:()=>Wo([])},ve={type:Number,required:!0},$t={type:V([String,Number,Symbol]),default:"id"},Vt={type:V(Object)},be=Me({class:String,columns:Fe,columnsStyles:{type:V(Object),required:!0},depth:Number,expandColumnKey:Jl,estimatedRowHeight:{...ke.estimatedRowHeight,default:void 0},isScrolling:Boolean,onRowExpand:{type:V(Function)},onRowHover:{type:V(Function)},onRowHeightChange:{type:V(Function)},rowData:{type:V(Object),required:!0},rowEventHandlers:{type:V(Object)},rowIndex:{type:Number,required:!0},rowKey:$t,style:{type:V(Object)}}),ht={type:Number,required:!0},mt=Me({class:String,columns:Fe,fixedHeaderData:{type:V(Array)},headerData:{type:V(Array),required:!0},headerHeight:{type:V([Number,Array]),default:50},rowWidth:ht,rowHeight:{type:Number,default:50},height:ht,width:ht}),Je=Me({columns:Fe,data:Lt,fixedData:ut,estimatedRowHeight:be.estimatedRowHeight,width:ve,height:ve,headerWidth:ve,headerHeight:mt.headerHeight,bodyWidth:ve,rowHeight:ve,cache:qo.cache,useIsScrolling:Boolean,scrollbarAlwaysOn:ke.scrollbarAlwaysOn,scrollbarStartGap:ke.scrollbarStartGap,scrollbarEndGap:ke.scrollbarEndGap,class:Dt,style:Vt,containerStyle:Vt,getRowHeight:{type:V(Function),required:!0},rowKey:be.rowKey,onRowsRendered:{type:V(Function)},onScroll:{type:V(Function)}}),eo=Me({cache:Je.cache,estimatedRowHeight:be.estimatedRowHeight,rowKey:$t,headerClass:{type:V([String,Function])},headerProps:{type:V([Object,Function])},headerCellProps:{type:V([Object,Function])},headerHeight:mt.headerHeight,footerHeight:{type:Number,default:0},rowClass:{type:V([String,Function])},rowProps:{type:V([Object,Function])},rowHeight:{type:Number,default:50},cellProps:{type:V([Object,Function])},columns:Fe,data:Lt,dataGetter:{type:V(Function)},fixedData:ut,expandColumnKey:be.expandColumnKey,expandedRowKeys:zt,defaultExpandedRowKeys:zt,class:Dt,fixed:Boolean,style:{type:V(Object)},width:ve,height:ve,maxHeight:Number,useIsScrolling:Boolean,indentSize:{type:Number,default:12},iconSize:{type:Number,default:12},hScrollbarSize:ke.hScrollbarSize,vScrollbarSize:ke.vScrollbarSize,scrollbarAlwaysOn:Uo.alwaysOn,sortBy:{type:V(Object),default:()=>({})},sortState:{type:V(Object),default:void 0},onColumnSort:{type:V(Function)},onExpandedRowsChange:{type:V(Function)},onEndReached:{type:V(Function)},onRowExpand:be.onRowExpand,onScroll:Je.onScroll,onRowsRendered:Je.onRowsRendered,rowEventHandlers:be.rowEventHandlers}),ft=(e,{slots:t})=>{var o;const{cellData:a,style:r}=e,n=((o=a==null?void 0:a.toString)==null?void 0:o.call(a))||"";return x("div",{class:e.class,title:n,style:r},[t.default?t.default(e):n])};ft.displayName="ElTableV2Cell",ft.inheritAttrs=!1;const pt=(e,{slots:t})=>{var o,a;return t.default?t.default(e):x("div",{class:e.class,title:(o=e.column)==null?void 0:o.title},[(a=e.column)==null?void 0:a.title])};pt.displayName="ElTableV2HeaderCell",pt.inheritAttrs=!1;const to=Me({class:String,columns:Fe,columnsStyles:{type:V(Object),required:!0},headerIndex:Number,style:{type:V(Object)}}),lo=ie({name:"ElTableV2HeaderRow",props:to,setup:(e,{slots:t})=>()=>{const{columns:o,columnsStyles:a,headerIndex:r,style:n}=e;let s=o.map((i,h)=>t.cell({columns:o,column:i,columnIndex:h,headerIndex:r,style:a[i.key]}));return t.header&&(s=t.header({cells:s.map(i=>Xe(i)&&i.length===1?i[0]:i),columns:o,headerIndex:r})),x("div",{class:e.class,style:n,role:"row"},[s])}}),oo=ie({name:"ElTableV2Header",props:mt,setup(e,{slots:t,expose:o}){const a=Ae("table-v2"),r=B(),n=E(()=>we({width:e.width,height:e.height})),s=E(()=>we({width:e.rowWidth,height:e.height})),i=E(()=>ko(l(e.headerHeight))),h=()=>{const d=a.e("fixed-header-row"),{columns:m,fixedHeaderData:T,rowHeight:A}=e;return T==null?void 0:T.map((M,c)=>{var G;const P=we({height:A,width:"100%"});return(G=t.fixed)==null?void 0:G.call(t,{class:d,columns:m,rowData:M,rowIndex:-(c+1),style:P})})},u=()=>{const d=a.e("dynamic-header-row"),{columns:m}=e;return l(i).map((T,A)=>{var M;const c=we({width:"100%",height:T});return(M=t.dynamic)==null?void 0:M.call(t,{class:d,columns:m,headerIndex:A,style:c})})};return o({scrollToLeft:d=>{const m=l(r);Ye(()=>{m!=null&&m.scroll&&m.scroll({left:d})})}}),()=>{if(!(e.height<=0))return x("div",{ref:r,class:e.class,style:l(n),role:"rowgroup"},[x("div",{style:l(s),class:a.e("header")},[u(),h()])])}}}),ao=e=>{const{isScrolling:t}=ol(dt),o=B(!1),a=B(),r=E(()=>ne(e.estimatedRowHeight)&&e.rowIndex>=0),n=E(()=>{const{rowData:s,rowIndex:i,rowKey:h,onRowHover:u}=e,d=e.rowEventHandlers||{},m={};return Object.entries(d).forEach(([T,A])=>{St(A)&&(m[T]=M=>{A({event:M,rowData:s,rowIndex:i,rowKey:h})})}),u&&[{name:"onMouseleave",hovered:!1},{name:"onMouseenter",hovered:!0}].forEach(({name:T,hovered:A})=>{const M=m[T];m[T]=c=>{u({event:c,hovered:A,rowData:s,rowIndex:i,rowKey:h}),M==null||M(c)}}),m});return lt(()=>{l(r)&&((s=!1)=>{const i=l(a);if(!i)return;const{columns:h,onRowHeightChange:u,rowKey:d,rowIndex:m,style:T}=e,{height:A}=i.getBoundingClientRect();o.value=!0,Ye(()=>{if(s||A!==Number.parseInt(T.height)){const M=h[0],c=(M==null?void 0:M.placeholderSign)===Ge;u==null||u({rowKey:d,height:A,rowIndex:m},M&&!c&&M.fixed)}})})(!0)}),{isScrolling:t,measurable:r,measured:o,rowRef:a,eventHandlers:n,onExpand:s=>{const{onRowExpand:i,rowData:h,rowIndex:u,rowKey:d}=e;i==null||i({expanded:s,rowData:h,rowIndex:u,rowKey:d})}}},ro=ie({name:"ElTableV2TableRow",props:be,setup(e,{expose:t,slots:o,attrs:a}){const{eventHandlers:r,isScrolling:n,measurable:s,measured:i,rowRef:h,onExpand:u}=ao(e);return t({onExpand:u}),()=>{const{columns:d,columnsStyles:m,expandColumnKey:T,depth:A,rowData:M,rowIndex:c,style:G}=e;let P=d.map(($,y)=>{const K=Xe(M.children)&&M.children.length>0&&$.key===T;return o.cell({column:$,columns:d,columnIndex:y,depth:A,style:m[$.key],rowData:M,rowIndex:c,isScrolling:l(n),expandIconProps:K?{rowData:M,rowIndex:c,onExpand:u}:void 0})});if(o.row&&(P=o.row({cells:P.map($=>Xe($)&&$.length===1?$[0]:$),style:G,columns:d,depth:A,rowData:M,rowIndex:c,isScrolling:l(n)})),l(s)){const{height:$,...y}=G||{},K=l(i);return x("div",te({ref:h,class:e.class,style:K?G:y,role:"row"},a,l(r)),[P])}return x("div",te(a,{ref:h,class:e.class,style:G,role:"row"},l(r)),[P])}}}),no=e=>{const{sortOrder:t}=e;return x(al,{size:14,class:e.class},{default:()=>[t===Ve.ASC?x(Ko,null,null):x(_o,null,null)]})},so=e=>{const{expanded:t,expandable:o,onExpand:a,style:r,size:n}=e,s={onClick:o?()=>a(!t):void 0,class:e.class};return x(al,te(s,{size:n,style:r}),{default:()=>[x(Ao,null,null)]})},wt=ie({name:"ElTableV2Grid",props:Je,setup(e,{slots:t,expose:o}){const{ns:a}=ol(dt),{bodyRef:r,fixedRowHeight:n,gridHeight:s,hasHeader:i,headerRef:h,headerHeight:u,totalHeight:d,forceUpdate:m,itemKey:T,onItemRendered:A,resetAfterRowIndex:M,scrollTo:c,scrollToTop:G,scrollToRow:P}=(y=>{const K=B(),W=B(),N=E(()=>{const{data:O,rowHeight:L,estimatedRowHeight:F}=y;if(!F)return O.length*L}),q=E(()=>{const{fixedData:O,rowHeight:L}=y;return((O==null?void 0:O.length)||0)*L}),U=E(()=>Ze(y.headerHeight)),Y=E(()=>{const{height:O}=y;return Math.max(0,O-l(U)-l(q))}),k=E(()=>l(U)+l(q)>0);return{bodyRef:W,forceUpdate:function(){var O,L;(O=l(W))==null||O.$forceUpdate(),(L=l(K))==null||L.$forceUpdate()},fixedRowHeight:q,gridHeight:Y,hasHeader:k,headerHeight:U,headerRef:K,totalHeight:N,itemKey:({data:O,rowIndex:L})=>O[L][y.rowKey],onItemRendered:function({rowCacheStart:O,rowCacheEnd:L,rowVisibleStart:F,rowVisibleEnd:Q}){var ce;(ce=y.onRowsRendered)==null||ce.call(y,{rowCacheStart:O,rowCacheEnd:L,rowVisibleStart:F,rowVisibleEnd:Q})},resetAfterRowIndex:function(O,L){var F;(F=W.value)==null||F.resetAfterRowIndex(O,L)},scrollTo:function(O,L){const F=l(K),Q=l(W);F&&Q&&(bt(O)?(F.scrollToLeft(O.scrollLeft),Q.scrollTo(O)):(F.scrollToLeft(O),Q.scrollTo({scrollLeft:O,scrollTop:L})))},scrollToTop:function(O){var L;(L=l(W))==null||L.scrollTo({scrollTop:O})},scrollToRow:function(O,L){var F;(F=l(W))==null||F.scrollToItem(O,1,L)}}})(e);o({forceUpdate:m,totalHeight:d,scrollTo:c,scrollToTop:G,scrollToRow:P,resetAfterRowIndex:M});const $=()=>e.bodyWidth;return()=>{const{cache:y,columns:K,data:W,fixedData:N,useIsScrolling:q,scrollbarAlwaysOn:U,scrollbarEndGap:Y,scrollbarStartGap:k,style:O,rowHeight:L,bodyWidth:F,estimatedRowHeight:Q,headerWidth:ce,height:se,width:le,getRowHeight:ge,onScroll:Se}=e,de=ne(Q),Te=de?Ul:Bl,Ce=l(u);return x("div",{role:"table",class:[a.e("table"),e.class],style:O},[x(Te,{ref:r,data:W,useIsScrolling:q,itemKey:T,columnCache:0,columnWidth:de?$:F,totalColumn:1,totalRow:W.length,rowCache:y,rowHeight:de?ge:L,width:le,height:l(s),class:a.e("body"),role:"rowgroup",scrollbarStartGap:k,scrollbarEndGap:Y,scrollbarAlwaysOn:U,onScroll:Se,onItemRendered:A,perfMode:!1},{default:ye=>{var me;const re=W[ye.rowIndex];return(me=t.row)==null?void 0:me.call(t,{...ye,columns:K,rowData:re})}}),l(i)&&x(oo,{ref:h,class:a.e("header-wrapper"),columns:K,headerData:W,headerHeight:e.headerHeight,fixedHeaderData:N,rowWidth:ce,rowHeight:L,width:le,height:Math.min(Ce+l(n),se)},{dynamic:t.header,fixed:t.row})])}}}),io=(e,{slots:t})=>{const{mainTableRef:o,...a}=e;return x(wt,te({ref:o},a),typeof(r=t)=="function"||Object.prototype.toString.call(r)==="[object Object]"&&!We(r)?t:{default:()=>[t]});var r},co=(e,{slots:t})=>{if(!e.columns.length)return;const{leftTableRef:o,...a}=e;return x(wt,te({ref:o},a),typeof(r=t)=="function"||Object.prototype.toString.call(r)==="[object Object]"&&!We(r)?t:{default:()=>[t]});var r},uo=(e,{slots:t})=>{if(!e.columns.length)return;const{rightTableRef:o,...a}=e;return x(wt,te({ref:o},a),typeof(r=t)=="function"||Object.prototype.toString.call(r)==="[object Object]"&&!We(r)?t:{default:()=>[t]});var r},ho=(e,{slots:t})=>{const{columns:o,columnsStyles:a,depthMap:r,expandColumnKey:n,expandedRowKeys:s,estimatedRowHeight:i,hasFixedColumns:h,hoveringRowKey:u,rowData:d,rowIndex:m,style:T,isScrolling:A,rowProps:M,rowClass:c,rowKey:G,rowEventHandlers:P,ns:$,onRowHovered:y,onRowExpanded:K}=e,W=Re(c,{columns:o,rowData:d,rowIndex:m},""),N=Re(M,{columns:o,rowData:d,rowIndex:m}),q=d[G],U=r[q]||0,Y=!!n,k=m<0,O={...N,columns:o,columnsStyles:a,class:[$.e("row"),W,{[$.e(`row-depth-${U}`)]:Y&&m>=0,[$.is("expanded")]:Y&&s.includes(q),[$.is("hovered")]:!A&&q===u,[$.is("fixed")]:!U&&k,[$.is("customized")]:!!t.row}],depth:U,expandColumnKey:n,estimatedRowHeight:k?void 0:i,isScrolling:A,rowIndex:m,rowData:d,rowKey:q,rowEventHandlers:P,style:T};return x(ro,te(O,{onRowHover:h?y:void 0,onRowExpand:K}),typeof(L=t)=="function"||Object.prototype.toString.call(L)==="[object Object]"&&!We(L)?t:{default:()=>[t]});var L},gt=({columns:e,column:t,columnIndex:o,depth:a,expandIconProps:r,isScrolling:n,rowData:s,rowIndex:i,style:h,expandedRowKeys:u,ns:d,cellProps:m,expandColumnKey:T,indentSize:A,iconSize:M,rowKey:c},{slots:G})=>{const P=we(h);if(t.placeholderSign===Ge)return x("div",{class:d.em("row-cell","placeholder"),style:P},null);const{cellRenderer:$,dataKey:y,dataGetter:K}=t,W=At($)||G.default||(Q=>x(ft,Q,null)),N=St(K)?K({columns:e,column:t,columnIndex:o,rowData:s,rowIndex:i}):Do(s,y??""),q=Re(m,{cellData:N,columns:e,column:t,columnIndex:o,rowIndex:i,rowData:s}),U=W({class:d.e("cell-text"),columns:e,column:t,columnIndex:o,cellData:N,isScrolling:n,rowData:s,rowIndex:i}),Y=[d.e("row-cell"),t.class,t.align===Ne.CENTER&&d.is("align-center"),t.align===Ne.RIGHT&&d.is("align-right")],k=i>=0&&T&&t.key===T,O=i>=0&&u.includes(s[c]);let L;const F=`margin-inline-start: ${a*A}px;`;return k&&(L=bt(r)?x(so,te(r,{class:[d.e("expand-icon"),d.is("expanded",O)],size:M,expanded:O,style:F,expandable:!0}),null):x("div",{style:[F,`width: ${M}px; height: ${M}px;`].join(" ")},null)),x("div",te({class:Y,style:P},q,{role:"cell"}),[L,U])};gt.inheritAttrs=!1;const mo=({columns:e,columnsStyles:t,headerIndex:o,style:a,headerClass:r,headerProps:n,ns:s},{slots:i})=>{const h={columns:e,headerIndex:o},u=[s.e("header-row"),Re(r,h,""),{[s.is("customized")]:!!i.header}],d={...Re(n,h),columnsStyles:t,class:u,columns:e,headerIndex:o,style:a};return x(lo,d,typeof(m=i)=="function"||Object.prototype.toString.call(m)==="[object Object]"&&!We(m)?i:{default:()=>[i]});var m},Nt=(e,{slots:t})=>{const{column:o,ns:a,style:r,onColumnSorted:n}=e,s=we(r);if(o.placeholderSign===Ge)return x("div",{class:a.em("header-row-cell","placeholder"),style:s},null);const{headerCellRenderer:i,headerClass:h,sortable:u}=o,d={...e,class:a.e("header-cell-text")},m=(At(i)||t.default||(y=>x(pt,y,null)))(d),{sortBy:T,sortState:A,headerCellProps:M}=e;let c,G;if(A){const y=A[o.key];c=!!ct[y],G=c?y:Ve.ASC}else c=o.key===T.key,G=c?T.order:Ve.ASC;const P=[a.e("header-cell"),Re(h,e,""),o.align===Ne.CENTER&&a.is("align-center"),o.align===Ne.RIGHT&&a.is("align-right"),u&&a.is("sortable")],$={...Re(M,e),onClick:o.sortable?n:void 0,class:P,style:s,"data-key":o.key};return x("div",te($,{role:"columnheader"}),[m,u&&x(no,{class:[a.e("sort-icon"),c&&a.is("sorting")],sortOrder:G},null)])},Gt=(e,{slots:t})=>{var o;return x("div",{class:e.class,style:e.style},[(o=t.default)==null?void 0:o.call(t)])};Gt.displayName="ElTableV2Footer";const Ft=(e,{slots:t})=>x("div",{class:e.class,style:e.style},[t.default?t.default():x(jl,null,null)]);Ft.displayName="ElTableV2Empty";const jt=(e,{slots:t})=>{var o;return x("div",{class:e.class,style:e.style},[(o=t.default)==null?void 0:o.call(t)])};function je(e){return typeof e=="function"||Object.prototype.toString.call(e)==="[object Object]"&&!We(e)}jt.displayName="ElTableV2Overlay";let Bt,Pt,qt,Ut,Yt,Xt;Bt=ie({name:"ElTableV2",props:eo,setup(e,{slots:t,expose:o}){const a=Ae("table-v2"),{columnsStyles:r,fixedColumnsOnLeft:n,fixedColumnsOnRight:s,mainColumns:i,mainTableHeight:h,fixedTableHeight:u,leftTableWidth:d,rightTableWidth:m,data:T,depthMap:A,expandedRowKeys:M,hasFixedColumns:c,hoveringRowKey:G,mainTableRef:P,leftTableRef:$,rightTableRef:y,isDynamic:K,isResetting:W,isScrolling:N,bodyWidth:q,emptyStyle:U,rootStyle:Y,headerWidth:k,footerHeight:O,showEmpty:L,scrollTo:F,scrollToLeft:Q,scrollToTop:ce,scrollToRow:se,getRowHeight:le,onColumnSorted:ge,onRowHeightChange:Se,onRowHovered:de,onRowExpanded:Te,onRowsRendered:Ce,onScroll:ye,onVerticalScroll:me}=Zl(e);return o({scrollTo:F,scrollToLeft:Q,scrollToTop:ce,scrollToRow:se}),Lo(dt,{ns:a,isResetting:W,hoveringRowKey:G,isScrolling:N}),()=>{const{cache:re,cellProps:Be,estimatedRowHeight:fe,expandColumnKey:He,fixedData:Pe,headerHeight:Ie,headerClass:z,headerProps:X,headerCellProps:w,sortBy:p,sortState:g,rowHeight:C,rowClass:I,rowEventHandlers:S,rowKey:R,rowProps:f,scrollbarAlwaysOn:H,indentSize:_,iconSize:v,useIsScrolling:b,vScrollbarSize:D,width:Z}=e,ee=l(T),Ke={cache:re,class:a.e("main"),columns:l(i),data:ee,fixedData:Pe,estimatedRowHeight:fe,bodyWidth:l(q)+D,headerHeight:Ie,headerWidth:l(k),height:l(h),mainTableRef:P,rowKey:R,rowHeight:C,scrollbarAlwaysOn:H,scrollbarStartGap:2,scrollbarEndGap:D,useIsScrolling:b,width:Z,getRowHeight:le,onRowsRendered:Ce,onScroll:ye},qe=l(d),J=l(u),ae={cache:re,class:a.e("left"),columns:l(n),data:ee,estimatedRowHeight:fe,leftTableRef:$,rowHeight:C,bodyWidth:qe,headerWidth:qe,headerHeight:Ie,height:J,rowKey:R,scrollbarAlwaysOn:H,scrollbarStartGap:2,scrollbarEndGap:D,useIsScrolling:b,width:qe,getRowHeight:le,onScroll:me},oe=l(m)+D,_e={cache:re,class:a.e("right"),columns:l(s),data:ee,estimatedRowHeight:fe,rightTableRef:y,rowHeight:C,bodyWidth:oe,headerWidth:oe,headerHeight:Ie,height:J,rowKey:R,scrollbarAlwaysOn:H,scrollbarStartGap:2,scrollbarEndGap:D,width:oe,style:`--${l(a.namespace)}-table-scrollbar-size: ${D}px`,useIsScrolling:b,getRowHeight:le,onScroll:me},xe=l(r),yt={ns:a,depthMap:l(A),columnsStyles:xe,expandColumnKey:He,expandedRowKeys:l(M),estimatedRowHeight:fe,hasFixedColumns:l(c),hoveringRowKey:l(G),rowProps:f,rowClass:I,rowKey:R,rowEventHandlers:S,onRowHovered:de,onRowExpanded:Te,onRowHeightChange:Se},et={cellProps:Be,expandColumnKey:He,indentSize:_,iconSize:v,rowKey:R,expandedRowKeys:l(M),ns:a},fo={ns:a,headerClass:z,headerProps:X,columnsStyles:xe},Qt={ns:a,sortBy:p,sortState:g,headerCellProps:w,onColumnSorted:ge},pe={row:xt=>x(ho,te(xt,yt),{row:t.row,cell:ue=>{let Ee;return t.cell?x(gt,te(ue,et,{style:xe[ue.column.key]}),je(Ee=t.cell(ue))?Ee:{default:()=>[Ee]}):x(gt,te(ue,et,{style:xe[ue.column.key]}),null)}}),header:xt=>x(mo,te(xt,fo),{header:t.header,cell:ue=>{let Ee;return t["header-cell"]?x(Nt,te(ue,Qt,{style:xe[ue.column.key]}),je(Ee=t["header-cell"](ue))?Ee:{default:()=>[Ee]}):x(Nt,te(ue,Qt,{style:xe[ue.column.key]}),null)}})},po=[e.class,a.b(),a.e("root"),{[a.is("dynamic")]:l(K)}],wo={class:a.e("footer"),style:l(O)};return x("div",{class:po,style:l(Y)},[x(io,Ke,je(pe)?pe:{default:()=>[pe]}),x(co,ae,je(pe)?pe:{default:()=>[pe]}),x(uo,_e,je(pe)?pe:{default:()=>[pe]}),t.footer&&x(Gt,wo,{default:t.footer}),l(L)&&x(Ft,{class:a.e("empty"),style:l(U)},{default:t.empty}),t.overlay&&x(jt,{class:a.e("overlay")},{default:t.overlay})])}}}),Pt=Me({disableWidth:Boolean,disableHeight:Boolean,onResize:{type:V(Function)}}),qt=ie({name:"ElAutoResizer",props:Pt,setup(e,{slots:t}){const o=Ae("auto-resizer"),{height:a,width:r,sizer:n}=(i=>{const h=B(),u=B(0),d=B(0);let m;return lt(()=>{m=Mo(h,([T])=>{const{width:A,height:M}=T.contentRect,{paddingLeft:c,paddingRight:G,paddingTop:P,paddingBottom:$}=getComputedStyle(T.target),y=Number.parseInt(c)||0,K=Number.parseInt(G)||0,W=Number.parseInt(P)||0,N=Number.parseInt($)||0;u.value=A-y-K,d.value=M-W-N}).stop}),Oo(()=>{m==null||m()}),ot([u,d],([T,A])=>{var M;(M=i.onResize)==null||M.call(i,{width:T,height:A})}),{sizer:h,width:u,height:d}})(e),s={width:"100%",height:"100%"};return()=>{var i;return x("div",{ref:n,class:o.b(),style:s},[(i=t.default)==null?void 0:i.call(t,{height:a.value,width:r.value})])}}}),Ut=vt(Bt),Yt=vt(qt),Xt={style:{width:"100%",height:"700px"}},fl=ie({name:"SystemArea",__name:"index",setup(e){const t=[{dataKey:"id",title:"\u7F16\u53F7",width:400,fixed:!0,key:"id"},{dataKey:"name",title:"\u5730\u540D",width:200}],o=B([]),a=B();return lt(()=>{(async()=>o.value=await la())()}),(r,n)=>{const s=Zo,i=Vo,h=No,u=Xo,d=Ut,m=Yt;return De(),Le($o,null,[x(s,{title:"\u5730\u533A & IP",url:"https://doc.iocoder.cn/area-and-ip/"}),x(u,null,{default:at(()=>[x(h,{type:"primary",plain:"",onClick:n[0]||(n[0]=T=>{a.value.open()})},{default:at(()=>[x(i,{icon:"ep:plus",class:"mr-5px"}),zo(" IP \u67E5\u8BE2 ")]),_:1})]),_:1}),x(u,null,{default:at(()=>[j("div",Xt,[x(m,null,{default:at(({height:T,width:A})=>[x(d,{columns:t,data:l(o),width:A,height:T,"expand-column-key":"id"},null,8,["data","width","height"])]),_:1})])]),_:1}),x(ea,{ref_key:"formRef",ref:a},null,512)],64)}}})});export{sa as __tla,fl as default};
