{"groups": [{"name": "yudao.tenant", "type": "cn.iocoder.yudao.framework.tenant.config.TenantProperties", "sourceType": "cn.iocoder.yudao.framework.tenant.config.TenantProperties"}], "properties": [{"name": "yudao.tenant.enable", "type": "java.lang.Bo<PERSON>an", "description": "是否开启", "sourceType": "cn.iocoder.yudao.framework.tenant.config.TenantProperties"}, {"name": "yudao.tenant.ignore-tables", "type": "java.util.Set<java.lang.String>", "description": "需要忽略多租户的表 即默认所有表都开启多租户的功能，所以记得添加对应的 tenant_id 字段哟", "sourceType": "cn.iocoder.yudao.framework.tenant.config.TenantProperties"}, {"name": "yudao.tenant.ignore-urls", "type": "java.util.Set<java.lang.String>", "description": "需要忽略多租户的请求 默认情况下，每个请求需要带上 tenant-id 的请求头。但是，部分请求是无需带上的，例如说短信回调、支付回调等 Open API！", "sourceType": "cn.iocoder.yudao.framework.tenant.config.TenantProperties"}], "hints": []}