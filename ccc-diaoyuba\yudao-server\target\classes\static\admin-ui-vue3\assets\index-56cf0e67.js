import{d as ee,r as _,f as ae,A as re,O as te,o as p,c as le,i as e,w as r,a,P as oe,j as u,B as F,q as m,a3 as y,F as ne,T as de,D as ie,M as se,C as _e,G as pe,_ as ue,H as ce,I as me,J as be,bN as fe,K as ge,L as he,__tla as ke}from"./index-97fffa0c.js";import{_ as ye,__tla as we}from"./index.vue_vue_type_script_setup_true_lang-d31568cf.js";import{E as xe,a as Ue,b as ve,__tla as Ee}from"./el-dropdown-item-1342d280.js";import{E as Ve,__tla as Ce}from"./el-avatar-c773bffa.js";import{_ as Te,__tla as Be}from"./ContentWrap.vue_vue_type_script_setup_true_lang-a574ccef.js";import{d as O,__tla as qe}from"./formatTime-9d54d2c5.js";import{g as De,c as Ie,b as Pe,__tla as Fe}from"./index-f2de8828.js";import{c as b,__tla as Oe}from"./permission-e32f164f.js";import{f as w,__tla as Ne}from"./formatter-e323aac6.js";import{_ as Ye,__tla as ze}from"./UpdateBindUserForm.vue_vue_type_script_setup_true_lang-a994ea51.js";import{_ as Le,__tla as Me}from"./BrokerageUserListDialog.vue_vue_type_script_setup_true_lang-1f35fcac.js";import{_ as Re,__tla as Se}from"./BrokerageOrderListDialog.vue_vue_type_script_setup_true_lang-1a096888.js";import{u as He,__tla as Ke}from"./useMessage-18385d4a.js";import{__tla as $e}from"./index-8d6db4ce.js";import{__tla as je}from"./el-card-6c7c099d.js";import{__tla as Ae}from"./Dialog.vue_vue_type_style_index_0_lang-5af0bc2b.js";import{__tla as Ge}from"./el-descriptions-item-5b1e935d.js";import{__tla as Je}from"./DictTag.vue_vue_type_script_lang-5679ad7b.js";import"./color-a8b4eb58.js";import{__tla as Qe}from"./dict-6a82eb12.js";import{__tla as We}from"./index-9edd9996.js";import"./constants-3933cd3a.js";let N,Xe=Promise.all([(()=>{try{return ke}catch{}})(),(()=>{try{return we}catch{}})(),(()=>{try{return Ee}catch{}})(),(()=>{try{return Ce}catch{}})(),(()=>{try{return Be}catch{}})(),(()=>{try{return qe}catch{}})(),(()=>{try{return Fe}catch{}})(),(()=>{try{return Oe}catch{}})(),(()=>{try{return Ne}catch{}})(),(()=>{try{return ze}catch{}})(),(()=>{try{return Me}catch{}})(),(()=>{try{return Se}catch{}})(),(()=>{try{return Ke}catch{}})(),(()=>{try{return $e}catch{}})(),(()=>{try{return je}catch{}})(),(()=>{try{return Ae}catch{}})(),(()=>{try{return Ge}catch{}})(),(()=>{try{return Je}catch{}})(),(()=>{try{return Qe}catch{}})(),(()=>{try{return We}catch{}})()]).then(async()=>{N=ee({name:"TradeBrokerageUser",__name:"index",setup(Ze){const f=He(),x=_(!0),V=_(0),C=_([]),o=ae({pageNo:1,pageSize:10,bindUserId:null,brokerageEnabled:!0,createTime:[]}),T=_(),c=async()=>{x.value=!0;try{const n=await De(o);C.value=n.list,V.value=n.total}finally{x.value=!1}},U=()=>{o.pageNo=1,c()},Y=()=>{T.value.resetFields(),U()},B=_(),z=n=>{B.value.open(n)},q=_(),L=n=>{q.value.open(n)},D=_(),M=n=>{D.value.open(n)},R=async n=>{try{await f.confirm(`\u786E\u8BA4\u8981\u6E05\u9664"${n.nickname}"\u7684\u4E0A\u7EA7\u63A8\u5E7F\u4EBA\u5417\uFF1F`),await Ie({id:n.id}),f.success("\u6E05\u9664\u6210\u529F"),await c()}catch{}};return re(()=>{c()}),(n,d)=>{const S=de,g=ie,I=se,H=_e,K=pe,v=ue,E=ce,$=me,P=Te,l=be,j=Ve,A=fe,h=xe,G=Ue,J=ve,Q=ge,W=ye,X=te("hasPermi"),Z=he;return p(),le(ne,null,[e(P,null,{default:r(()=>[e($,{class:"-mb-15px",model:a(o),ref_key:"queryFormRef",ref:T,inline:!0,"label-width":"85px"},{default:r(()=>[e(g,{label:"\u63A8\u5E7F\u5458\u7F16\u53F7",prop:"bindUserId"},{default:r(()=>[e(S,{modelValue:a(o).bindUserId,"onUpdate:modelValue":d[0]||(d[0]=t=>a(o).bindUserId=t),placeholder:"\u8BF7\u8F93\u5165\u63A8\u5E7F\u5458\u7F16\u53F7",clearable:"",onKeyup:oe(U,["enter"]),class:"!w-240px"},null,8,["modelValue","onKeyup"])]),_:1}),e(g,{label:"\u63A8\u5E7F\u8D44\u683C",prop:"brokerageEnabled"},{default:r(()=>[e(H,{modelValue:a(o).brokerageEnabled,"onUpdate:modelValue":d[1]||(d[1]=t=>a(o).brokerageEnabled=t),class:"!w-240px",clearable:"",placeholder:"\u8BF7\u9009\u62E9\u63A8\u5E7F\u8D44\u683C"},{default:r(()=>[e(I,{label:"\u6709",value:!0}),e(I,{label:"\u65E0",value:!1})]),_:1},8,["modelValue"])]),_:1}),e(g,{label:"\u521B\u5EFA\u65F6\u95F4",prop:"createTime"},{default:r(()=>[e(K,{modelValue:a(o).createTime,"onUpdate:modelValue":d[2]||(d[2]=t=>a(o).createTime=t),"value-format":"YYYY-MM-DD HH:mm:ss",type:"daterange","start-placeholder":"\u5F00\u59CB\u65E5\u671F","end-placeholder":"\u7ED3\u675F\u65E5\u671F","default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],class:"!w-240px"},null,8,["modelValue","default-time"])]),_:1}),e(g,null,{default:r(()=>[e(E,{onClick:U},{default:r(()=>[e(v,{icon:"ep:search",class:"mr-5px"}),u(" \u641C\u7D22")]),_:1}),e(E,{onClick:Y},{default:r(()=>[e(v,{icon:"ep:refresh",class:"mr-5px"}),u(" \u91CD\u7F6E")]),_:1})]),_:1})]),_:1},8,["model"])]),_:1}),e(P,null,{default:r(()=>[F((p(),m(Q,{data:a(C),stripe:!0,"show-overflow-tooltip":!0},{default:r(()=>[e(l,{label:"\u7528\u6237\u7F16\u53F7",align:"center",prop:"id","min-width":"80px"}),e(l,{label:"\u5934\u50CF",align:"center",prop:"avatar",width:"70px"},{default:r(t=>[e(j,{src:t.row.avatar},null,8,["src"])]),_:1}),e(l,{label:"\u6635\u79F0",align:"center",prop:"nickname","min-width":"80px"}),e(l,{label:"\u63A8\u5E7F\u4EBA\u6570",align:"center",prop:"brokerageUserCount",width:"80px"}),e(l,{label:"\u63A8\u5E7F\u8BA2\u5355\u6570\u91CF",align:"center",prop:"brokerageOrderCount","min-width":"110px"}),e(l,{label:"\u63A8\u5E7F\u8BA2\u5355\u91D1\u989D",align:"center",prop:"brokerageOrderPrice","min-width":"110px",formatter:a(w)},null,8,["formatter"]),e(l,{label:"\u5DF2\u63D0\u73B0\u91D1\u989D",align:"center",prop:"withdrawPrice","min-width":"100px",formatter:a(w)},null,8,["formatter"]),e(l,{label:"\u5DF2\u63D0\u73B0\u6B21\u6570",align:"center",prop:"withdrawCount","min-width":"100px"}),e(l,{label:"\u672A\u63D0\u73B0\u91D1\u989D",align:"center",prop:"price","min-width":"100px",formatter:a(w)},null,8,["formatter"]),e(l,{label:"\u51BB\u7ED3\u4E2D\u4F63\u91D1",align:"center",prop:"frozenPrice","min-width":"100px",formatter:a(w)},null,8,["formatter"]),e(l,{label:"\u63A8\u5E7F\u8D44\u683C",align:"center",prop:"brokerageEnabled","min-width":"80px"},{default:r(t=>[e(A,{modelValue:t.row.brokerageEnabled,"onUpdate:modelValue":k=>t.row.brokerageEnabled=k,"active-text":"\u6709","inactive-text":"\u65E0","inline-prompt":"",disabled:!a(b)(["trade:brokerage-user:update-bind-user"]),onChange:k=>(async i=>{try{const s=i.brokerageEnabled?"\u5F00\u901A":"\u5173\u95ED";await f.confirm(`\u786E\u8BA4\u8981${s}"${i.nickname}"\u7684\u63A8\u5E7F\u8D44\u683C\u5417\uFF1F`),await Pe({id:i.id,enabled:i.brokerageEnabled}),f.success(s+"\u6210\u529F"),await c()}catch{i.brokerageEnabled=!i.brokerageEnabled}})(t.row)},null,8,["modelValue","onUpdate:modelValue","disabled","onChange"])]),_:1}),e(l,{label:"\u6210\u4E3A\u63A8\u5E7F\u5458\u65F6\u95F4",align:"center",prop:"brokerageTime",formatter:a(O),width:"180px"},null,8,["formatter"]),e(l,{label:"\u4E0A\u7EA7\u63A8\u5E7F\u5458\u7F16\u53F7",align:"center",prop:"bindUserId",width:"150px"}),e(l,{label:"\u63A8\u5E7F\u5458\u7ED1\u5B9A\u65F6\u95F4",align:"center",prop:"bindUserTime",formatter:a(O),width:"180px"},null,8,["formatter"]),e(l,{label:"\u64CD\u4F5C",align:"center",width:"150px",fixed:"right"},{default:r(t=>[F((p(),m(J,{onCommand:k=>((i,s)=>{switch(i){case"openBrokerageUserTable":z(s.id);break;case"openBrokerageOrderTable":L(s.id);break;case"openUpdateBindUserForm":M(s);break;case"handleClearBindUser":R(s)}})(k,t.row)},{dropdown:r(()=>[e(G,null,{default:r(()=>[a(b)(["trade:brokerage-user:user-query"])?(p(),m(h,{key:0,command:"openBrokerageUserTable"},{default:r(()=>[u(" \u63A8\u5E7F\u4EBA ")]),_:1})):y("",!0),a(b)(["trade:brokerage-user:order-query"])?(p(),m(h,{key:1,command:"openBrokerageOrderTable"},{default:r(()=>[u(" \u63A8\u5E7F\u8BA2\u5355 ")]),_:1})):y("",!0),a(b)(["trade:brokerage-user:update-bind-user"])?(p(),m(h,{key:2,command:"openUpdateBindUserForm"},{default:r(()=>[u(" \u4FEE\u6539\u4E0A\u7EA7\u63A8\u5E7F\u4EBA ")]),_:1})):y("",!0),t.row.bindUserId&&a(b)(["trade:brokerage-user:clear-bind-user"])?(p(),m(h,{key:3,command:"handleClearBindUser"},{default:r(()=>[u(" \u6E05\u9664\u4E0A\u7EA7\u63A8\u5E7F\u4EBA ")]),_:1})):y("",!0)]),_:2},1024)]),default:r(()=>[e(E,{link:"",type:"primary"},{default:r(()=>[e(v,{icon:"ep:d-arrow-right"}),u(" \u66F4\u591A ")]),_:1})]),_:2},1032,["onCommand"])),[[X,["trade:brokerage-user:user-query","trade:brokerage-user:order-query","trade:brokerage-user:update-bind-user","trade:brokerage-user:clear-bind-user"]]])]),_:1})]),_:1},8,["data"])),[[Z,a(x)]]),e(W,{total:a(V),page:a(o).pageNo,"onUpdate:page":d[3]||(d[3]=t=>a(o).pageNo=t),limit:a(o).pageSize,"onUpdate:limit":d[4]||(d[4]=t=>a(o).pageSize=t),onPagination:c},null,8,["total","page","limit"])]),_:1}),e(Ye,{ref_key:"updateBindUserFormRef",ref:D,onSuccess:c},null,512),e(Le,{ref_key:"brokerageUserListDialogRef",ref:B},null,512),e(Re,{ref_key:"brokerageOrderListDialogRef",ref:q},null,512)],64)}}})});export{Xe as __tla,N as default};
