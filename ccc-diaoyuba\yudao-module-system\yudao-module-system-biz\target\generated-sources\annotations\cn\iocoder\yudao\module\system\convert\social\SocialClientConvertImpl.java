package cn.iocoder.yudao.module.system.convert.social;

import cn.binarywang.wx.miniapp.bean.WxMaPhoneNumberInfo;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.system.api.social.dto.SocialWxJsapiSignatureRespDTO;
import cn.iocoder.yudao.module.system.api.social.dto.SocialWxPhoneNumberInfoRespDTO;
import cn.iocoder.yudao.module.system.controller.admin.socail.vo.client.SocialClientCreateReqVO;
import cn.iocoder.yudao.module.system.controller.admin.socail.vo.client.SocialClientRespVO;
import cn.iocoder.yudao.module.system.controller.admin.socail.vo.client.SocialClientUpdateReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.social.SocialClientDO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import me.chanjar.weixin.common.bean.WxJsapiSignature;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-21T09:32:49+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_181 (Oracle Corporation)"
)
public class SocialClientConvertImpl implements SocialClientConvert {

    @Override
    public SocialWxJsapiSignatureRespDTO convert(WxJsapiSignature bean) {
        if ( bean == null ) {
            return null;
        }

        SocialWxJsapiSignatureRespDTO socialWxJsapiSignatureRespDTO = new SocialWxJsapiSignatureRespDTO();

        socialWxJsapiSignatureRespDTO.setAppId( bean.getAppId() );
        socialWxJsapiSignatureRespDTO.setNonceStr( bean.getNonceStr() );
        socialWxJsapiSignatureRespDTO.setTimestamp( bean.getTimestamp() );
        socialWxJsapiSignatureRespDTO.setUrl( bean.getUrl() );
        socialWxJsapiSignatureRespDTO.setSignature( bean.getSignature() );

        return socialWxJsapiSignatureRespDTO;
    }

    @Override
    public SocialWxPhoneNumberInfoRespDTO convert(WxMaPhoneNumberInfo bean) {
        if ( bean == null ) {
            return null;
        }

        SocialWxPhoneNumberInfoRespDTO socialWxPhoneNumberInfoRespDTO = new SocialWxPhoneNumberInfoRespDTO();

        socialWxPhoneNumberInfoRespDTO.setPhoneNumber( bean.getPhoneNumber() );
        socialWxPhoneNumberInfoRespDTO.setPurePhoneNumber( bean.getPurePhoneNumber() );
        socialWxPhoneNumberInfoRespDTO.setCountryCode( bean.getCountryCode() );

        return socialWxPhoneNumberInfoRespDTO;
    }

    @Override
    public SocialClientDO convert(SocialClientCreateReqVO bean) {
        if ( bean == null ) {
            return null;
        }

        SocialClientDO.SocialClientDOBuilder socialClientDO = SocialClientDO.builder();

        socialClientDO.name( bean.getName() );
        socialClientDO.socialType( bean.getSocialType() );
        socialClientDO.userType( bean.getUserType() );
        socialClientDO.status( bean.getStatus() );
        socialClientDO.clientId( bean.getClientId() );
        socialClientDO.clientSecret( bean.getClientSecret() );
        socialClientDO.agentId( bean.getAgentId() );

        return socialClientDO.build();
    }

    @Override
    public SocialClientDO convert(SocialClientUpdateReqVO bean) {
        if ( bean == null ) {
            return null;
        }

        SocialClientDO.SocialClientDOBuilder socialClientDO = SocialClientDO.builder();

        socialClientDO.id( bean.getId() );
        socialClientDO.name( bean.getName() );
        socialClientDO.socialType( bean.getSocialType() );
        socialClientDO.userType( bean.getUserType() );
        socialClientDO.status( bean.getStatus() );
        socialClientDO.clientId( bean.getClientId() );
        socialClientDO.clientSecret( bean.getClientSecret() );
        socialClientDO.agentId( bean.getAgentId() );

        return socialClientDO.build();
    }

    @Override
    public SocialClientRespVO convert(SocialClientDO bean) {
        if ( bean == null ) {
            return null;
        }

        SocialClientRespVO socialClientRespVO = new SocialClientRespVO();

        socialClientRespVO.setName( bean.getName() );
        socialClientRespVO.setSocialType( bean.getSocialType() );
        socialClientRespVO.setUserType( bean.getUserType() );
        socialClientRespVO.setClientId( bean.getClientId() );
        socialClientRespVO.setClientSecret( bean.getClientSecret() );
        socialClientRespVO.setAgentId( bean.getAgentId() );
        socialClientRespVO.setStatus( bean.getStatus() );
        socialClientRespVO.setId( bean.getId() );
        socialClientRespVO.setCreateTime( bean.getCreateTime() );

        return socialClientRespVO;
    }

    @Override
    public List<SocialClientRespVO> convertList(List<SocialClientDO> list) {
        if ( list == null ) {
            return null;
        }

        List<SocialClientRespVO> list1 = new ArrayList<SocialClientRespVO>( list.size() );
        for ( SocialClientDO socialClientDO : list ) {
            list1.add( convert( socialClientDO ) );
        }

        return list1;
    }

    @Override
    public PageResult<SocialClientRespVO> convertPage(PageResult<SocialClientDO> page) {
        if ( page == null ) {
            return null;
        }

        PageResult<SocialClientRespVO> pageResult = new PageResult<SocialClientRespVO>();

        pageResult.setList( convertList( page.getList() ) );
        pageResult.setTotal( page.getTotal() );

        return pageResult;
    }
}
