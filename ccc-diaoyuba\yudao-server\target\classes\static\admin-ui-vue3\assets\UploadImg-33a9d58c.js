import{d as q,p as s,cf as M,r as f,cg as P,cd as W,ce as A,o as r,c as m,i as o,w as B,a as d,V as F,g as t,aP as V,q as D,a3 as G,b9 as H,F as J,t as _,bk as K,l as L,_ as N,__tla as O}from"./index-97fffa0c.js";import{E as Q,__tla as R}from"./el-image-viewer-fddfe81d.js";import{u as X,__tla as Y}from"./useMessage-18385d4a.js";import{_ as Z}from"./_plugin-vue_export-helper-1b428a4d.js";let z,aa=Promise.all([(()=>{try{return O}catch{}})(),(()=>{try{return R}catch{}})(),(()=>{try{return Y}catch{}})()]).then(async()=>{let g,h,y,v;g={class:"upload-box"},h=["src"],y={key:1,class:"upload-empty"},v={class:"el-upload__tip"},z=Z(q({name:"UploadImg",__name:"UploadImg",props:{modelValue:s.string.def(""),updateUrl:s.string.def("http://localhost:48080/admin-api/infra/file/upload"),drag:s.bool.def(!0),disabled:s.bool.def(!1),fileSize:s.number.def(5),fileType:s.array.def(["image/jpeg","image/png","image/gif"]),height:s.string.def("150px"),width:s.string.def("150px"),borderradius:s.string.def("8px")},emits:["update:modelValue"],setup(e,{emit:w}){M(a=>({d8b082f2:e.width,"70fdcef4":e.height,"4bfd53dd":e.borderradius}));const c=e,{t:p}=L(),n=X(),b=f("id-"+P()),u=f(!1),k=w,x=()=>{k("update:modelValue","")},C=f({Authorization:"Bearer "+W(),"tenant-id":A()}),U=()=>{const a=document.querySelector(`#${b.value} .el-upload__input`);a&&a.dispatchEvent(new MouseEvent("click"))},E=a=>{const l=a.size/1024/1024<c.fileSize,i=c.fileType;return i.includes(a.type)||n.notifyWarning("\u4E0A\u4F20\u56FE\u7247\u4E0D\u7B26\u5408\u6240\u9700\u7684\u683C\u5F0F\uFF01"),l||n.notifyWarning(`\u4E0A\u4F20\u56FE\u7247\u5927\u5C0F\u4E0D\u80FD\u8D85\u8FC7 ${c.fileSize}M\uFF01`),i.includes(a.type)&&l},S=a=>{n.success("\u4E0A\u4F20\u6210\u529F"),k("update:modelValue",a.data)},$=()=>{n.notifyError("\u56FE\u7247\u4E0A\u4F20\u5931\u8D25\uFF0C\u8BF7\u60A8\u91CD\u65B0\u4E0A\u4F20\uFF01")};return(a,l)=>{const i=N,j=H,I=Q;return r(),m("div",g,[o(j,{action:e.updateUrl,id:d(b),class:F(["upload",e.drag?"no-border":""]),multiple:!1,"show-file-list":!1,headers:d(C),"before-upload":E,"on-success":S,"on-error":$,drag:e.drag,accept:e.fileType.join(",")},{default:B(()=>[e.modelValue?(r(),m(J,{key:0},[t("img",{src:e.modelValue,class:"upload-image"},null,8,h),t("div",{class:"upload-handle",onClick:l[1]||(l[1]=K(()=>{},["stop"]))},[t("div",{class:"handle-icon",onClick:U},[o(i,{icon:"ep:edit"}),t("span",null,_(d(p)("action.edit")),1)]),t("div",{class:"handle-icon",onClick:l[0]||(l[0]=T=>u.value=!0)},[o(i,{icon:"ep:zoom-in"}),t("span",null,_(d(p)("action.detail")),1)]),t("div",{class:"handle-icon",onClick:x},[o(i,{icon:"ep:delete"}),t("span",null,_(d(p)("action.del")),1)])])],64)):(r(),m("div",y,[V(a.$slots,"empty",{},()=>[o(i,{icon:"ep:plus"})],!0)]))]),_:3},8,["action","id","class","headers","drag","accept"]),t("div",v,[V(a.$slots,"tip",{},void 0,!0)]),d(u)?(r(),D(I,{key:0,onClose:l[2]||(l[2]=T=>u.value=!1),"url-list":[e.modelValue]},null,8,["url-list"])):G("",!0)])}}}),[["__scopeId","data-v-f28ddfa0"]])});export{z as _,aa as __tla};
