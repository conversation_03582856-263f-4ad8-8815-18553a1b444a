#!/bin/bash

# 生产环境启动脚本 - 钓鱼吧应用
# 适用于您的Linux服务器环境

# 配置参数
APP_NAME="diaoyuba"
LOG_DIR="/log"
JAR_FILE="/www/wwwroot/yudao-server/yudao-server.jar"
JAVA_BIN="/usr/local/btjdk/jdk8/bin/java"
PROFILE="pro"
SERVER_PORT="48080"
PID_FILE="/var/run/diaoyuba.pid"

echo "=========================================="
echo "启动 $APP_NAME 生产环境"
echo "=========================================="

# 检查Java环境
if [ ! -f "$JAVA_BIN" ]; then
    echo "❌ Java不存在: $JAVA_BIN"
    exit 1
fi

echo "✅ Java版本: $($JAVA_BIN -version 2>&1 | head -n 1)"

# 检查JAR文件
if [ ! -f "$JAR_FILE" ]; then
    echo "❌ JAR文件不存在: $JAR_FILE"
    exit 1
fi

echo "✅ JAR文件存在: $JAR_FILE"

# 创建日志目录
echo "检查日志目录: $LOG_DIR"
if [ ! -d "$LOG_DIR" ]; then
    echo "创建日志目录: $LOG_DIR"
    mkdir -p "$LOG_DIR"
    chmod 755 "$LOG_DIR"
    echo "✅ 日志目录创建成功"
else
    echo "✅ 日志目录已存在"
fi

# 检查是否已有进程在运行
if [ -f "$PID_FILE" ]; then
    OLD_PID=$(cat "$PID_FILE")
    if ps -p $OLD_PID > /dev/null 2>&1; then
        echo "⚠️ 应用已在运行 (PID: $OLD_PID)"
        echo "停止现有进程..."
        kill -TERM $OLD_PID
        sleep 5
        if ps -p $OLD_PID > /dev/null 2>&1; then
            echo "强制停止进程..."
            kill -9 $OLD_PID
        fi
        rm -f "$PID_FILE"
    fi
fi

# 启动应用
echo "启动应用..."
echo "配置: $PROFILE"
echo "端口: $SERVER_PORT"
echo "日志: $LOG_DIR/diaoyuba.log"
echo "错误日志: $LOG_DIR/diaoyuba-error.log"
echo ""

# 启动命令
nohup $JAVA_BIN -jar \
    -Xmx1024M \
    -Xms256M \
    -Dlogging.file.name=$LOG_DIR/diaoyuba.log \
    -Dlogging.file.path=$LOG_DIR \
    -Dfile.encoding=UTF-8 \
    -Duser.timezone=Asia/Shanghai \
    -XX:+UseG1GC \
    -XX:+PrintGCDetails \
    -XX:+PrintGCTimeStamps \
    -Xloggc:$LOG_DIR/gc.log \
    "$JAR_FILE" \
    --server.port=$SERVER_PORT \
    --spring.profiles.active=$PROFILE \
    > $LOG_DIR/startup.log 2>&1 &

# 保存PID
NEW_PID=$!
echo $NEW_PID > "$PID_FILE"
echo "🚀 应用已启动，PID: $NEW_PID"

# 等待启动
echo "等待应用启动..."
sleep 10

# 检查启动状态
if ps -p $NEW_PID > /dev/null 2>&1; then
    echo "✅ 应用启动成功！"
    echo ""
    echo "📋 应用信息:"
    echo "  PID: $NEW_PID"
    echo "  端口: $SERVER_PORT"
    echo "  配置: $PROFILE"
    echo "  PID文件: $PID_FILE"
    echo ""
    echo "📁 日志文件:"
    echo "  应用日志: $LOG_DIR/diaoyuba.log"
    echo "  错误日志: $LOG_DIR/diaoyuba-error.log"
    echo "  启动日志: $LOG_DIR/startup.log"
    echo "  GC日志: $LOG_DIR/gc.log"
    echo ""
    echo "📖 常用命令:"
    echo "  查看日志: tail -f $LOG_DIR/diaoyuba.log"
    echo "  查看错误: tail -f $LOG_DIR/diaoyuba-error.log"
    echo "  停止应用: kill -TERM $NEW_PID"
    echo "  检查状态: ps -p $NEW_PID"
    echo ""
    echo "🌐 访问地址: http://localhost:$SERVER_PORT"
else
    echo "❌ 应用启动失败！"
    echo "请检查启动日志: $LOG_DIR/startup.log"
    rm -f "$PID_FILE"
    exit 1
fi
