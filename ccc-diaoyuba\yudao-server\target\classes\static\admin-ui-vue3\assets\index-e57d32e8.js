import{d as $,u as Q,l as W,r as g,f as X,A as Z,O as ee,o as s,c as _,i as a,w as t,a as l,P as A,F as m,k as h,q as u,j as d,B as w,g as ae,t as le,a3 as te,bY as re,T as oe,D as se,M as ne,C as ue,G as pe,_ as ce,H as ie,I as _e,J as me,K as de,L as fe,__tla as ye}from"./index-97fffa0c.js";import{_ as be,__tla as ve}from"./index.vue_vue_type_script_setup_true_lang-d31568cf.js";import{_ as ge,__tla as he}from"./DictTag.vue_vue_type_script_lang-5679ad7b.js";import{_ as we,__tla as Se}from"./ContentWrap.vue_vue_type_script_setup_true_lang-a574ccef.js";import{a as E,D as i,__tla as ke}from"./dict-6a82eb12.js";import{d as R,__tla as Te}from"./formatTime-9d54d2c5.js";import{b as Ce,a as Pe,__tla as Ve}from"./index-db126fd7.js";import{u as xe,__tla as Ee}from"./useMessage-18385d4a.js";import{__tla as De}from"./index-8d6db4ce.js";import"./color-a8b4eb58.js";import{__tla as Be}from"./el-card-6c7c099d.js";let Y,Ie=Promise.all([(()=>{try{return ye}catch{}})(),(()=>{try{return ve}catch{}})(),(()=>{try{return he}catch{}})(),(()=>{try{return Se}catch{}})(),(()=>{try{return ke}catch{}})(),(()=>{try{return Te}catch{}})(),(()=>{try{return Ve}catch{}})(),(()=>{try{return Ee}catch{}})(),(()=>{try{return De}catch{}})(),(()=>{try{return Be}catch{}})()]).then(async()=>{Y=$({name:"BpmProcessInstance",__name:"index",setup(Me){const D=Q(),K=xe(),{t:B}=W(),S=g(!0),I=g(0),M=g([]),r=X({pageNo:1,pageSize:10,name:"",processDefinitionId:void 0,category:void 0,status:void 0,result:void 0,createTime:[]}),N=g(),f=async()=>{S.value=!0;try{const k=await Ce(r);M.value=k.list,I.value=k.total}finally{S.value=!1}},y=()=>{r.pageNo=1,f()},L=()=>{N.value.resetFields(),y()},q=()=>{D.push({name:"BpmProcessInstanceCreate"})};return Z(()=>{f()}),(k,o)=>{const U=oe,p=se,T=ne,C=ue,z=pe,P=ce,c=ie,F=_e,O=we,n=me,V=ge,G=de,H=be,x=ee("hasPermi"),j=fe;return s(),_(m,null,[a(O,null,{default:t(()=>[a(F,{class:"-mb-15px",model:l(r),ref_key:"queryFormRef",ref:N,inline:!0,"label-width":"68px"},{default:t(()=>[a(p,{label:"\u6D41\u7A0B\u540D\u79F0",prop:"name"},{default:t(()=>[a(U,{modelValue:l(r).name,"onUpdate:modelValue":o[0]||(o[0]=e=>l(r).name=e),placeholder:"\u8BF7\u8F93\u5165\u6D41\u7A0B\u540D\u79F0",clearable:"",onKeyup:A(y,["enter"]),class:"!w-240px"},null,8,["modelValue","onKeyup"])]),_:1}),a(p,{label:"\u6240\u5C5E\u6D41\u7A0B",prop:"processDefinitionId"},{default:t(()=>[a(U,{modelValue:l(r).processDefinitionId,"onUpdate:modelValue":o[1]||(o[1]=e=>l(r).processDefinitionId=e),placeholder:"\u8BF7\u8F93\u5165\u6D41\u7A0B\u5B9A\u4E49\u7684\u7F16\u53F7",clearable:"",onKeyup:A(y,["enter"]),class:"!w-240px"},null,8,["modelValue","onKeyup"])]),_:1}),a(p,{label:"\u6D41\u7A0B\u5206\u7C7B",prop:"category"},{default:t(()=>[a(C,{modelValue:l(r).category,"onUpdate:modelValue":o[2]||(o[2]=e=>l(r).category=e),placeholder:"\u8BF7\u9009\u62E9\u6D41\u7A0B\u5206\u7C7B",clearable:"",class:"!w-240px"},{default:t(()=>[(s(!0),_(m,null,h(l(E)(l(i).BPM_MODEL_CATEGORY),e=>(s(),u(T,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(p,{label:"\u72B6\u6001",prop:"status"},{default:t(()=>[a(C,{modelValue:l(r).status,"onUpdate:modelValue":o[3]||(o[3]=e=>l(r).status=e),placeholder:"\u8BF7\u9009\u62E9\u72B6\u6001",clearable:"",class:"!w-240px"},{default:t(()=>[(s(!0),_(m,null,h(l(E)(l(i).BPM_PROCESS_INSTANCE_STATUS),e=>(s(),u(T,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(p,{label:"\u7ED3\u679C",prop:"result"},{default:t(()=>[a(C,{modelValue:l(r).result,"onUpdate:modelValue":o[4]||(o[4]=e=>l(r).result=e),placeholder:"\u8BF7\u9009\u62E9\u7ED3\u679C",clearable:"",class:"!w-240px"},{default:t(()=>[(s(!0),_(m,null,h(l(E)(l(i).BPM_PROCESS_INSTANCE_RESULT),e=>(s(),u(T,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(p,{label:"\u63D0\u4EA4\u65F6\u95F4",prop:"createTime"},{default:t(()=>[a(z,{modelValue:l(r).createTime,"onUpdate:modelValue":o[5]||(o[5]=e=>l(r).createTime=e),"value-format":"YYYY-MM-DD HH:mm:ss",type:"daterange","start-placeholder":"\u5F00\u59CB\u65E5\u671F","end-placeholder":"\u7ED3\u675F\u65E5\u671F","default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],class:"!w-240px"},null,8,["modelValue","default-time"])]),_:1}),a(p,null,{default:t(()=>[a(c,{onClick:y},{default:t(()=>[a(P,{icon:"ep:search",class:"mr-5px"}),d(" \u641C\u7D22")]),_:1}),a(c,{onClick:L},{default:t(()=>[a(P,{icon:"ep:refresh",class:"mr-5px"}),d(" \u91CD\u7F6E")]),_:1}),w((s(),u(c,{type:"primary",plain:"",onClick:q},{default:t(()=>[a(P,{icon:"ep:plus",class:"mr-5px"}),d(" \u53D1\u8D77\u6D41\u7A0B ")]),_:1})),[[x,["bpm:process-instance:query"]]])]),_:1})]),_:1},8,["model"])]),_:1}),a(O,null,{default:t(()=>[w((s(),u(G,{data:l(M)},{default:t(()=>[a(n,{label:"\u6D41\u7A0B\u7F16\u53F7",align:"center",prop:"id",width:"300px"}),a(n,{label:"\u6D41\u7A0B\u540D\u79F0",align:"center",prop:"name"}),a(n,{label:"\u6D41\u7A0B\u5206\u7C7B",align:"center",prop:"category"},{default:t(e=>[a(V,{type:l(i).BPM_MODEL_CATEGORY,value:e.row.category},null,8,["type","value"])]),_:1}),a(n,{label:"\u5F53\u524D\u5BA1\u6279\u4EFB\u52A1",align:"center",prop:"tasks"},{default:t(e=>[(s(!0),_(m,null,h(e.row.tasks,b=>(s(),u(c,{type:"primary",key:b.id,link:""},{default:t(()=>[ae("span",null,le(b.name),1)]),_:2},1024))),128))]),_:1}),a(n,{label:"\u72B6\u6001",prop:"status"},{default:t(e=>[a(V,{type:l(i).BPM_PROCESS_INSTANCE_STATUS,value:e.row.status},null,8,["type","value"])]),_:1}),a(n,{label:"\u7ED3\u679C",prop:"result"},{default:t(e=>[a(V,{type:l(i).BPM_PROCESS_INSTANCE_RESULT,value:e.row.result},null,8,["type","value"])]),_:1}),a(n,{label:"\u63D0\u4EA4\u65F6\u95F4",align:"center",prop:"createTime",width:"180",formatter:l(R)},null,8,["formatter"]),a(n,{label:"\u7ED3\u675F\u65F6\u95F4",align:"center",prop:"endTime",width:"180",formatter:l(R)},null,8,["formatter"]),a(n,{label:"\u64CD\u4F5C",align:"center"},{default:t(e=>[w((s(),u(c,{link:"",type:"primary",onClick:b=>{return v=e.row,void D.push({name:"BpmProcessInstanceDetail",query:{id:v.id}});var v}},{default:t(()=>[d(" \u8BE6\u60C5 ")]),_:2},1032,["onClick"])),[[x,["bpm:process-instance:cancel"]]]),e.row.result===1?w((s(),u(c,{key:0,link:"",type:"primary",onClick:b=>(async v=>{const{value:J}=await re.prompt("\u8BF7\u8F93\u5165\u53D6\u6D88\u539F\u56E0","\u53D6\u6D88\u6D41\u7A0B",{confirmButtonText:B("common.ok"),cancelButtonText:B("common.cancel"),inputPattern:/^[\s\S]*.*\S[\s\S]*$/,inputErrorMessage:"\u53D6\u6D88\u539F\u56E0\u4E0D\u80FD\u4E3A\u7A7A"});await Pe(v.id,J),K.success("\u53D6\u6D88\u6210\u529F"),await f()})(e.row)},{default:t(()=>[d(" \u53D6\u6D88 ")]),_:2},1032,["onClick"])),[[x,["bpm:process-instance:query"]]]):te("",!0)]),_:1})]),_:1},8,["data"])),[[j,l(S)]]),a(H,{total:l(I),page:l(r).pageNo,"onUpdate:page":o[6]||(o[6]=e=>l(r).pageNo=e),limit:l(r).pageSize,"onUpdate:limit":o[7]||(o[7]=e=>l(r).pageSize=e),onPagination:f},null,8,["total","page","limit"])]),_:1})],64)}}})});export{Ie as __tla,Y as default};
