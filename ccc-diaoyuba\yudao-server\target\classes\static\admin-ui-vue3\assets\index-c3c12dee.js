import{_ as ua,__tla as _a}from"./ContentWrap.vue_vue_type_script_setup_true_lang-a574ccef.js";import{d as ca,N as oa,u as na,r as O,A as pa,o as c,c as k,i as a,w as e,j as s,t as u,a as t,at as D,F as C,k as S,q as y,a3 as v,l as ia,H as da,J as ya,K as ma,E as fa,n as ba,g as b,au as ha,$ as Ea,a0 as va,av as wa,aj as Ta,__tla as Aa}from"./index-97fffa0c.js";import{E as Ra,a as ka,__tla as Ca}from"./el-timeline-item-25d1936c.js";import{E as Na,__tla as Pa}from"./el-image-1637bc2a.js";import{__tla as Da}from"./el-image-viewer-fddfe81d.js";import{E as Sa,a as ga,__tla as Ya}from"./el-descriptions-item-5b1e935d.js";import{_ as Fa,__tla as Ia}from"./DictTag.vue_vue_type_script_lang-5679ad7b.js";import{g as La,a as Ua,r as xa,b as Oa,c as Ha,__tla as Ma}from"./index-e32b664d.js";import{D as p,b as ja,c as Va,__tla as $a}from"./dict-6a82eb12.js";import{f as H,__tla as qa}from"./formatTime-9d54d2c5.js";import{_ as Ba,__tla as Ga}from"./AfterSaleDisagreeForm.vue_vue_type_script_setup_true_lang-471ae678.js";import{c as Ja,__tla as Ka}from"./index-75488397.js";import{u as Wa,__tla as za}from"./tagsView-c5b6677c.js";import{u as Qa,__tla as Xa}from"./useMessage-18385d4a.js";import{_ as Za}from"./_plugin-vue_export-helper-1b428a4d.js";import{__tla as ae}from"./el-card-6c7c099d.js";import"./color-a8b4eb58.js";import{__tla as ee}from"./Dialog.vue_vue_type_style_index_0_lang-5af0bc2b.js";import{__tla as te}from"./ImageViewer.vue_vue_type_script_setup_true_lang-cdbc76a9.js";let M,le=Promise.all([(()=>{try{return _a}catch{}})(),(()=>{try{return Aa}catch{}})(),(()=>{try{return Ca}catch{}})(),(()=>{try{return Pa}catch{}})(),(()=>{try{return Da}catch{}})(),(()=>{try{return Ya}catch{}})(),(()=>{try{return Ia}catch{}})(),(()=>{try{return Ma}catch{}})(),(()=>{try{return $a}catch{}})(),(()=>{try{return qa}catch{}})(),(()=>{try{return Ga}catch{}})(),(()=>{try{return Ka}catch{}})(),(()=>{try{return za}catch{}})(),(()=>{try{return Xa}catch{}})(),(()=>{try{return ae}catch{}})(),(()=>{try{return ee}catch{}})(),(()=>{try{return te}catch{}})()]).then(async()=>{let w,g,Y,F,I,L;w=T=>(Ea("data-v-d290ceed"),T=T(),va(),T),g=w(()=>b("span",{style:{color:"red"}},"\u63D0\u9192: ",-1)),Y=w(()=>b("br",null,null,-1)),F=w(()=>b("br",null,null,-1)),I={class:"el-timeline-right-content"},L=ca({name:"TradeAfterSaleDetail",__name:"index",setup(T){const{t:A}=ia(),o=Qa(),{params:j}=oa(),{push:V,currentRoute:$}=na(),l=O({order:{},logs:[]}),U=O(),q=i=>{const n=Va(p.USER_TYPE,i);switch(n==null?void 0:n.colorType){case"success":return"#67C23A";case"info":return"#909399";case"warning":return"#E6A23C";case"danger":return"#F56C6C"}return"#409EFF"},f=async()=>{const i=j.id;if(i){const n=await La(i);n==null&&(o.notifyError("\u552E\u540E\u8BA2\u5355\u4E0D\u5B58\u5728"),Q()),l.value=n}},B=async()=>{try{await o.confirm("\u662F\u5426\u540C\u610F\u552E\u540E\uFF1F"),await Ua(l.value.id),o.success(A("common.success")),await f()}catch{}},G=async()=>{var i;(i=U.value)==null||i.open(l.value)},J=async()=>{try{await o.confirm("\u662F\u5426\u786E\u8BA4\u6536\u8D27\uFF1F"),await xa(l.value.id),o.success(A("common.success")),await f()}catch{}},K=async()=>{try{await o.confirm("\u662F\u5426\u62D2\u7EDD\u6536\u8D27\uFF1F"),await Oa(l.value.id),o.success(A("common.success")),await f()}catch{}},W=async()=>{try{await o.confirm("\u662F\u5426\u786E\u8BA4\u9000\u6B3E\uFF1F"),await Ha(l.value.id),o.success(A("common.success")),await f()}catch{}},{delView:z}=Wa(),Q=()=>{z(t($)),V({name:"TradeAfterSale"})};return pa(async()=>{await f()}),(i,n)=>{const r=Sa,m=Fa,h=ga,X=Na,E=da,Z=Ta,R=ya,aa=ma,x=fa,ea=ba,ta=ka,la=Ra,ra=ua;return c(),k(C,null,[a(ra,null,{default:e(()=>[a(h,{title:"\u8BA2\u5355\u4FE1\u606F"},{default:e(()=>[a(r,{label:"\u8BA2\u5355\u53F7: "},{default:e(()=>[s(u(t(l).orderNo),1)]),_:1}),a(r,{label:"\u914D\u9001\u65B9\u5F0F: "},{default:e(()=>[a(m,{type:t(p).TRADE_DELIVERY_TYPE,value:t(l).order.deliveryType},null,8,["type","value"])]),_:1}),a(r,{label:"\u8BA2\u5355\u7C7B\u578B: "},{default:e(()=>[a(m,{type:t(p).TRADE_ORDER_TYPE,value:t(l).order.type},null,8,["type","value"])]),_:1}),a(r,{label:"\u6536\u8D27\u4EBA: "},{default:e(()=>[s(u(t(l).order.receiverName),1)]),_:1}),a(r,{label:"\u4E70\u5BB6\u7559\u8A00: "},{default:e(()=>[s(u(t(l).order.userRemark),1)]),_:1}),a(r,{label:"\u8BA2\u5355\u6765\u6E90: "},{default:e(()=>[a(m,{type:t(p).TERMINAL,value:t(l).order.terminal},null,8,["type","value"])]),_:1}),a(r,{label:"\u8054\u7CFB\u7535\u8BDD: "},{default:e(()=>[s(u(t(l).order.receiverMobile),1)]),_:1}),a(r,{label:"\u5546\u5BB6\u5907\u6CE8: "},{default:e(()=>[s(u(t(l).order.remark),1)]),_:1}),a(r,{label:"\u652F\u4ED8\u5355\u53F7: "},{default:e(()=>[s(u(t(l).order.payOrderId),1)]),_:1}),a(r,{label:"\u4ED8\u6B3E\u65B9\u5F0F: "},{default:e(()=>[a(m,{type:t(p).PAY_CHANNEL_CODE,value:t(l).order.payChannelCode},null,8,["type","value"])]),_:1}),a(r,{label:"\u4E70\u5BB6: "},{default:e(()=>{var _,d;return[s(u((d=(_=t(l))==null?void 0:_.user)==null?void 0:d.nickname),1)]}),_:1})]),_:1}),a(h,{title:"\u552E\u540E\u4FE1\u606F"},{default:e(()=>[a(r,{label:"\u9000\u6B3E\u7F16\u53F7: "},{default:e(()=>[s(u(t(l).no),1)]),_:1}),a(r,{label:"\u7533\u8BF7\u65F6\u95F4: "},{default:e(()=>[s(u(t(H)(t(l).auditTime)),1)]),_:1}),a(r,{label:"\u552E\u540E\u7C7B\u578B: "},{default:e(()=>[a(m,{type:t(p).TRADE_AFTER_SALE_TYPE,value:t(l).type},null,8,["type","value"])]),_:1}),a(r,{label:"\u552E\u540E\u65B9\u5F0F: "},{default:e(()=>[a(m,{type:t(p).TRADE_AFTER_SALE_WAY,value:t(l).way},null,8,["type","value"])]),_:1}),a(r,{label:"\u9000\u6B3E\u91D1\u989D: "},{default:e(()=>[s(u(t(D)(t(l).refundPrice)),1)]),_:1}),a(r,{label:"\u9000\u6B3E\u539F\u56E0: "},{default:e(()=>[s(u(t(l).applyReason),1)]),_:1}),a(r,{label:"\u8865\u5145\u63CF\u8FF0: "},{default:e(()=>[s(u(t(l).applyDescription),1)]),_:1}),a(r,{label:"\u51ED\u8BC1\u56FE\u7247: "},{default:e(()=>[(c(!0),k(C,null,S(t(l).applyPicUrls,(_,d)=>(c(),y(X,{key:d,src:_.url,class:"mr-10px h-60px w-60px",onClick:n[0]||(n[0]=re=>(N=>{const P=[];wa(N)?N.forEach(sa=>{P.push(sa.url)}):P.push(N),Ja({urlList:P})})(t(l).applyPicUrls))},null,8,["src"]))),128))]),_:1})]),_:1}),a(h,{column:1,title:"\u9000\u6B3E\u72B6\u6001"},{default:e(()=>[a(r,{label:"\u9000\u6B3E\u72B6\u6001: "},{default:e(()=>[a(m,{type:t(p).TRADE_AFTER_SALE_STATUS,value:t(l).status},null,8,["type","value"])]),_:1}),a(r,{"label-class-name":"no-colon"},{default:e(()=>[t(l).status===10?(c(),y(E,{key:0,type:"primary",onClick:B},{default:e(()=>[s("\u540C\u610F\u552E\u540E")]),_:1})):v("",!0),t(l).status===10?(c(),y(E,{key:1,type:"primary",onClick:G},{default:e(()=>[s(" \u62D2\u7EDD\u552E\u540E ")]),_:1})):v("",!0),t(l).status===30?(c(),y(E,{key:2,type:"primary",onClick:J},{default:e(()=>[s(" \u786E\u8BA4\u6536\u8D27 ")]),_:1})):v("",!0),t(l).status===30?(c(),y(E,{key:3,type:"primary",onClick:K},{default:e(()=>[s("\u62D2\u7EDD\u6536\u8D27")]),_:1})):v("",!0),t(l).status===40?(c(),y(E,{key:4,type:"primary",onClick:W},{default:e(()=>[s("\u786E\u8BA4\u9000\u6B3E")]),_:1})):v("",!0)]),_:1}),a(r,null,{label:e(()=>[g]),default:e(()=>[s(" \u5982\u679C\u672A\u53D1\u8D27\uFF0C\u8BF7\u70B9\u51FB\u540C\u610F\u9000\u6B3E\u7ED9\u4E70\u5BB6\u3002"),Y,s(" \u5982\u679C\u5B9E\u9645\u5DF2\u53D1\u8D27\uFF0C\u8BF7\u4E3B\u52A8\u4E0E\u4E70\u5BB6\u8054\u7CFB\u3002"),F,s(" \u5982\u679C\u8BA2\u5355\u6574\u4F53\u9000\u6B3E\u540E\uFF0C\u4F18\u60E0\u5238\u548C\u4F59\u989D\u4F1A\u9000\u8FD8\u7ED9\u4E70\u5BB6. ")]),_:1})]),_:1}),a(h,{title:"\u5546\u54C1\u4FE1\u606F"},{default:e(()=>[a(r,{labelClassName:"no-colon"},{default:e(()=>[a(ea,{gutter:20},{default:e(()=>[a(x,{span:15},{default:e(()=>[a(aa,{data:[t(l).orderItem],border:""},{default:e(()=>[a(R,{label:"\u5546\u54C1",prop:"spuName",width:"auto"},{default:e(({row:_})=>[s(u(_.spuName)+" ",1),(c(!0),k(C,null,S(_.properties,d=>(c(),y(Z,{key:d.propertyId},{default:e(()=>[s(u(d.propertyName)+": "+u(d.valueName),1)]),_:2},1024))),128))]),_:1}),a(R,{label:"\u5546\u54C1\u539F\u4EF7",prop:"price",width:"150"},{default:e(({row:_})=>[s(u(t(D)(_.price))+" \u5143",1)]),_:1}),a(R,{label:"\u6570\u91CF",prop:"count",width:"100"}),a(R,{label:"\u5408\u8BA1",prop:"payPrice",width:"150"},{default:e(({row:_})=>[s(u(t(D)(_.payPrice))+" \u5143",1)]),_:1})]),_:1},8,["data"])]),_:1}),a(x,{span:10})]),_:1})]),_:1})]),_:1}),a(h,{title:"\u552E\u540E\u65E5\u5FD7"},{default:e(()=>[a(r,{labelClassName:"no-colon"},{default:e(()=>[a(la,null,{default:e(()=>[(c(!0),k(C,null,S(t(l).logs,_=>(c(),y(ta,{key:_.id,timestamp:t(H)(_.createTime),placement:"top"},{dot:e(()=>[b("span",{style:ha({backgroundColor:q(_.userType)}),class:"dot-node-style"},u(t(ja)(t(p).USER_TYPE,_.userType)[0]||"\u7CFB"),5)]),default:e(()=>[b("div",I,[b("span",null,u(_.content),1)])]),_:2},1032,["timestamp"]))),128))]),_:1})]),_:1})]),_:1})]),_:1}),a(Ba,{ref_key:"updateAuditReasonFormRef",ref:U,onSuccess:f},null,512)],64)}}}),M=Za(L,[["__scopeId","data-v-d290ceed"]])});export{le as __tla,M as default};
