import{d as g,r as d,A as x,o as P,c as C,i as t,w as s,a,at as n,E as w,n as b,__tla as U}from"./index-97fffa0c.js";import{b as j,__tla as A}from"./trade-a4dc55c5.js";import{a as E,__tla as H}from"./member-6cff0f2a.js";import{_ as i,__tla as I}from"./ComparisonCard.vue_vue_type_script_setup_true_lang-b8bec1dc.js";import{_ as M,__tla as T}from"./MemberStatisticsCard.vue_vue_type_script_setup_true_lang-29ebbd69.js";import{_ as k,__tla as q}from"./OperationDataCard.vue_vue_type_script_setup_true_lang-3d267ff9.js";import{_ as z,__tla as B}from"./ShortcutCard.vue_vue_type_script_setup_true_lang-044e9973.js";import{_ as D,__tla as F}from"./TradeTrendCard.vue_vue_type_script_setup_true_lang-68ceebe8.js";import{_ as G,__tla as J}from"./MemberTerminalCard.vue_vue_type_script_setup_true_lang-8318f6f2.js";import K,{__tla as L}from"./MemberFunnelCard-ffaa6573.js";import{_ as N}from"./_plugin-vue_export-helper-1b428a4d.js";import{__tla as O}from"./formatTime-9d54d2c5.js";import{__tla as Q}from"./CountTo.vue_vue_type_script_setup_true_lang-9925bfe0.js";import{__tla as R}from"./el-card-6c7c099d.js";import{__tla as S}from"./Echart.vue_vue_type_script_setup_true_lang-470d3b7c.js";import{__tla as V}from"./CardTitle-c3925800.js";import{__tla as W}from"./spu-02377d16.js";import{__tla as X}from"./dict-6a82eb12.js";import{__tla as Y}from"./index.vue_vue_type_script_setup_true_lang-f5357967.js";let p,Z=Promise.all([(()=>{try{return U}catch{}})(),(()=>{try{return A}catch{}})(),(()=>{try{return H}catch{}})(),(()=>{try{return I}catch{}})(),(()=>{try{return T}catch{}})(),(()=>{try{return q}catch{}})(),(()=>{try{return B}catch{}})(),(()=>{try{return F}catch{}})(),(()=>{try{return J}catch{}})(),(()=>{try{return L}catch{}})(),(()=>{try{return O}catch{}})(),(()=>{try{return Q}catch{}})(),(()=>{try{return R}catch{}})(),(()=>{try{return S}catch{}})(),(()=>{try{return V}catch{}})(),(()=>{try{return W}catch{}})(),(()=>{try{return X}catch{}})(),(()=>{try{return Y}catch{}})()]).then(async()=>{let y;y={class:"flex flex-col"},p=N(g({name:"MallHome",__name:"index",setup($){const c=d(!0),u=d(),m=d(),v=async()=>{u.value=await j()},h=async()=>{m.value=await E()};return x(async()=>{c.value=!0,await Promise.all([v(),h()]),c.value=!1}),(tt,at)=>{const o=w,f=b;return P(),C("div",y,[t(f,{gutter:16,class:"row"},{default:s(()=>[t(o,{md:6,sm:12,xs:24,loading:a(c)},{default:s(()=>{var r,e,_,l;return[t(i,{tag:"\u4ECA\u65E5",title:"\u9500\u552E\u989D",prefix:"\uFFE5",":decimals":2,value:a(n)(((e=(r=a(u))==null?void 0:r.value)==null?void 0:e.orderPayPrice)||0),reference:a(n)(((l=(_=a(u))==null?void 0:_.reference)==null?void 0:l.orderPayPrice)||0)},null,8,["value","reference"])]}),_:1},8,["loading"]),t(o,{md:6,sm:12,xs:24,loading:a(c)},{default:s(()=>{var r,e,_,l;return[t(i,{tag:"\u4ECA\u65E5",title:"\u7528\u6237\u8BBF\u95EE\u91CF",value:((e=(r=a(m))==null?void 0:r.value)==null?void 0:e.visitUserCount)||0,reference:((l=(_=a(m))==null?void 0:_.reference)==null?void 0:l.visitUserCount)||0},null,8,["value","reference"])]}),_:1},8,["loading"]),t(o,{md:6,sm:12,xs:24,loading:a(c)},{default:s(()=>{var r,e,_,l;return[t(i,{tag:"\u4ECA\u65E5",title:"\u8BA2\u5355\u91CF",value:a(n)(((e=(r=a(u))==null?void 0:r.value)==null?void 0:e.orderPayCount)||0),reference:a(n)(((l=(_=a(u))==null?void 0:_.reference)==null?void 0:l.orderPayCount)||0)},null,8,["value","reference"])]}),_:1},8,["loading"]),t(o,{md:6,sm:12,xs:24,loading:a(c)},{default:s(()=>{var r,e,_,l;return[t(i,{tag:"\u4ECA\u65E5",title:"\u65B0\u589E\u7528\u6237",value:((e=(r=a(m))==null?void 0:r.value)==null?void 0:e.registerUserCount)||0,reference:((l=(_=a(m))==null?void 0:_.reference)==null?void 0:l.registerUserCount)||0},null,8,["value","reference"])]}),_:1},8,["loading"])]),_:1}),t(f,{gutter:16,class:"row"},{default:s(()=>[t(o,{md:12},{default:s(()=>[t(z)]),_:1}),t(o,{md:12},{default:s(()=>[t(k)]),_:1})]),_:1}),t(f,{gutter:16,class:"mb-4"},{default:s(()=>[t(o,{md:18,sm:24},{default:s(()=>[t(K)]),_:1}),t(o,{md:6,sm:24},{default:s(()=>[t(G)]),_:1})]),_:1}),t(D,{class:"mb-4"}),t(M)])}}}),[["__scopeId","data-v-7b34cf75"]])});export{Z as __tla,p as default};
