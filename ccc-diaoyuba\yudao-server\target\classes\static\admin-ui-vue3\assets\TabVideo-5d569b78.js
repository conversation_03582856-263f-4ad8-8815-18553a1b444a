import{d as z,cd as A,b as B,r as y,f as C,o as h,c as E,i as a,w as r,a as e,q as M,a3 as S,j as V,x as H,T as P,n as Q,_ as W,H as D,cj as F,E as G,b9 as J,__tla as K}from"./index-97fffa0c.js";import{_ as L,__tla as N}from"./main.vue_vue_type_script_setup_true_lang-4cb32a33.js";import{W as O,__tla as R}from"./main-4dd868b0.js";import{u as X,U as Y,__tla as Z}from"./useUpload-36312237.js";import{u as $,__tla as tt}from"./useMessage-18385d4a.js";import{_ as at}from"./_plugin-vue_export-helper-1b428a4d.js";import{__tla as lt}from"./index.vue_vue_type_script_setup_true_lang-d31568cf.js";import{__tla as et}from"./index-8d6db4ce.js";import{__tla as rt}from"./main-17919147.js";import{__tla as ot}from"./el-image-1637bc2a.js";import{__tla as ut}from"./el-image-viewer-fddfe81d.js";import{__tla as _t}from"./main-7292042e.js";import{__tla as it}from"./index-aa57e946.js";import{__tla as st}from"./index-3b46e2ef.js";import{__tla as ct}from"./formatTime-9d54d2c5.js";let v,nt=Promise.all([(()=>{try{return K}catch{}})(),(()=>{try{return N}catch{}})(),(()=>{try{return R}catch{}})(),(()=>{try{return Z}catch{}})(),(()=>{try{return tt}catch{}})(),(()=>{try{return lt}catch{}})(),(()=>{try{return et}catch{}})(),(()=>{try{return rt}catch{}})(),(()=>{try{return ot}catch{}})(),(()=>{try{return ut}catch{}})(),(()=>{try{return _t}catch{}})(),(()=>{try{return it}catch{}})(),(()=>{try{return st}catch{}})(),(()=>{try{return ct}catch{}})()]).then(async()=>{v=at(z({__name:"TabVideo",props:{modelValue:{}},emits:["update:modelValue"],setup(b,{emit:I}){const g=$(),U={Authorization:"Bearer "+A()},k=b,w=I,l=B({get:()=>k.modelValue,set:t=>w("update:modelValue",t)}),_=y(!1),n=y([]),i=C({accountId:l.value.accountId,type:"video",title:"",introduction:""}),j=t=>X(Y.Video,10)(t),x=t=>{if(t.code!==0)return g.error("\u4E0A\u4F20\u51FA\u9519\uFF1A"+t.msg),!1;n.value=[],i.title="",i.introduction="",d(t.data)},d=t=>{_.value=!1,l.value.mediaId=t.mediaId,l.value.url=t.url,l.value.name=t.name,t.title&&(l.value.title=t.title||""),t.introduction&&(l.value.description=t.introduction||"")};return(t,o)=>{const m=P,s=Q,p=W,f=D,T=F,c=G,q=J;return h(),E("div",null,[a(s,null,{default:r(()=>[a(m,{modelValue:e(l).title,"onUpdate:modelValue":o[0]||(o[0]=u=>e(l).title=u),class:"input-margin-bottom",placeholder:"\u8BF7\u8F93\u5165\u6807\u9898"},null,8,["modelValue"]),a(m,{class:"input-margin-bottom",modelValue:e(l).description,"onUpdate:modelValue":o[1]||(o[1]=u=>e(l).description=u),placeholder:"\u8BF7\u8F93\u5165\u63CF\u8FF0"},null,8,["modelValue"]),a(s,{class:"ope-row",justify:"center"},{default:r(()=>[e(l).url?(h(),M(e(L),{key:0,url:e(l).url},null,8,["url"])):S("",!0)]),_:1}),a(c,null,{default:r(()=>[a(s,{style:{"text-align":"center"},align:"middle"},{default:r(()=>[a(c,{span:12},{default:r(()=>[a(f,{type:"success",onClick:o[2]||(o[2]=u=>_.value=!0)},{default:r(()=>[V(" \u7D20\u6750\u5E93\u9009\u62E9 "),a(p,{icon:"ep:circle-check"})]),_:1}),a(T,{title:"\u9009\u62E9\u89C6\u9891",modelValue:e(_),"onUpdate:modelValue":o[3]||(o[3]=u=>H(_)?_.value=u:null),width:"90%","append-to-body":"","destroy-on-close":""},{default:r(()=>[a(e(O),{type:"video","account-id":e(l).accountId,onSelectMaterial:d},null,8,["account-id"])]),_:1},8,["modelValue"])]),_:1}),a(c,{span:12},{default:r(()=>[a(q,{action:"/admin-api/mp/material/upload-temporary",headers:U,multiple:"",limit:1,"file-list":e(n),data:e(i),"before-upload":j,"on-success":x},{default:r(()=>[a(f,{type:"primary"},{default:r(()=>[V("\u65B0\u5EFA\u89C6\u9891 "),a(p,{icon:"ep:upload"})]),_:1})]),_:1},8,["file-list","data"])]),_:1})]),_:1})]),_:1})]),_:1})])}}}),[["__scopeId","data-v-10385e99"]])});export{nt as __tla,v as default};
