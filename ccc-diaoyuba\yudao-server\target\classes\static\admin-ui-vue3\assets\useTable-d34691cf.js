import{d as ae,p as c,r as _,A as pe,a,b as $,ax as R,B as ue,L as ge,i as g,K as me,ah as K,v as fe,J as W,l as ne,ar as he,bX as ye,o as P,c as Q,ci as we,k as be,w as k,aP as V,q as B,j as M,t as q,a3 as L,au as xe,F as ve,_ as _e,H as Pe,f as Se,aw as oe,bY as le,al as Ae,__tla as ke}from"./index-97fffa0c.js";import{g as I,_ as Le,__tla as ze}from"./Form-abbdb81e.js";import{E as Oe,__tla as Te}from"./index-8d6db4ce.js";import{_ as Ce}from"./_plugin-vue_export-helper-1b428a4d.js";import{u as je,__tla as Fe}from"./useForm-66271e88.js";import{d as $e}from"./download-20922b56.js";let re,ie,se,Re=Promise.all([(()=>{try{return ke}catch{}})(),(()=>{try{return ze}catch{}})(),(()=>{try{return Te}catch{}})(),(()=>{try{return Fe}catch{}})()]).then(async()=>{let Z,y;ie=Ce(ae({name:"Table",props:{pageSize:c.number.def(10),currentPage:c.number.def(1),selection:c.bool.def(!1),showOverflowTooltip:c.bool.def(!0),columns:{type:Array,default:()=>[]},expand:c.bool.def(!1),pagination:{type:Object,default:()=>{}},reserveSelection:c.bool.def(!1),loading:c.bool.def(!1),reserveIndex:c.bool.def(!1),align:c.string.validate(e=>["left","center","right"].includes(e)).def("center"),headerAlign:c.string.validate(e=>["left","center","right"].includes(e)).def("center"),data:{type:Array,default:()=>[]}},emits:["update:pageSize","update:currentPage","register"],setup(e,{attrs:o,slots:p,emit:m,expose:z}){const u=_();pe(()=>{const t=a(u);m("register",t==null?void 0:t.$parent,u)});const b=_(e.pageSize),f=_(e.currentPage),n=_({}),l=_({}),r=$(()=>{const t={...e};return Object.assign(t,a(l)),t}),x=(t,O)=>{var S;const{columns:T}=a(r);for(const C of O||T)for(const j of t)C.field===j.field?fe(C,j.path,j.value):(S=C.children)!=null&&S.length&&x(t,C.children)},D=_([]),U=t=>{D.value=t};z({setProps:(t={})=>{l.value=Object.assign(a(l),t),n.value=t},setColumn:x,selections:D});const i=$(()=>Object.assign({small:!1,background:!0,pagerCount:document.body.clientWidth<992?5:7,layout:"total, sizes, prev, pager, next, jumper",pageSizes:[10,20,30,50,100],disabled:!1,hideOnSinglePage:!1,total:10},a(r).pagination));R(()=>a(r).pageSize,t=>{b.value=t}),R(()=>a(r).currentPage,t=>{f.value=t}),R(()=>b.value,t=>{m("update:pageSize",t)}),R(()=>f.value,t=>{m("update:currentPage",t)});const v=$(()=>{const t={...o,...e};return delete t.columns,delete t.data,t}),d=()=>{const{selection:t,reserveSelection:O,align:T,headerAlign:S}=a(r);return t?g(W,{type:"selection",reserveSelection:O,align:T,headerAlign:S,width:"50"},null):void 0},w=()=>{const{align:t,headerAlign:O,expand:T}=a(r);return T?g(W,{type:"expand",align:t,headerAlign:O},{default:S=>I(p,"expand",S)}):void 0},E=t=>{const{columns:O,reserveIndex:T,pageSize:S,currentPage:C,align:j,headerAlign:ee,showOverflowTooltip:ce}=a(r);return[w(),d()].concat((t||O).map(s=>{if(s.type==="index")return g(W,{type:"index",index:s.index?s.index:N=>((A,H,X,Y)=>{const J=H+1;return A?X*(Y-1)+J:J})(T,N,S,C),align:s.align||j,headerAlign:s.headerAlign||ee,label:s.label,width:"65px"},null);{const N={...s};return N.children&&delete N.children,g(W,K({showOverflowTooltip:ce,align:j,headerAlign:ee},N,{prop:s.field}),{default:A=>{var H;return s.children&&s.children.length?(X=>{const{align:Y,headerAlign:J,showOverflowTooltip:de}=a(r);return X.map(h=>{const G={...h};return G.children&&delete G.children,g(W,K({showOverflowTooltip:de,align:Y,headerAlign:J},G,{prop:h.field}),{default:F=>{var te;return h.children&&h.children.length?E(h.children):I(p,h.field,F)||((te=h==null?void 0:h.formatter)==null?void 0:te.call(h,F.row,F.column,F.row[h.field],F.$index))||F.row[h.field]},header:I(p,`${h.field}-header`)})})})(s.children):I(p,s.field,A)||((H=s==null?void 0:s.formatter)==null?void 0:H.call(s,A.row,A.column,A.row[s.field],A.$index))||A.row[s.field]},header:()=>I(p,`${s.field}-header`)||s.label})}}))};return()=>ue(g("div",null,[g(me,K({ref:u,data:a(r).data,"onSelection-change":U},a(v)),{default:()=>E(),append:()=>I(p,"append")}),a(r).pagination?g(Oe,K({pageSize:b.value,"onUpdate:pageSize":t=>b.value=t,currentPage:f.value,"onUpdate:currentPage":t=>f.value=t,class:"float-right mb-15px mt-15px"},a(i)),null):void 0]),[[ge,a(r).loading]])}}),[["__scopeId","data-v-1ee0f9ba"]]),Z={key:0},re=ae({name:"Search",__name:"Search",props:{schema:{type:Array,default:()=>[]},isCol:c.bool.def(!1),labelWidth:c.oneOfType([String,Number]).def("auto"),layout:c.string.validate(e=>["inline","bottom"].includes(e)).def("inline"),buttomPosition:c.string.validate(e=>["left","center","right"].includes(e)).def("center"),showSearch:c.bool.def(!0),showReset:c.bool.def(!0),expand:c.bool.def(!1),expandField:c.string.def(""),inline:c.bool.def(!0),model:{type:Object,default:()=>({})}},emits:["search","reset"],setup(e,{emit:o}){const{t:p}=ne(),m=e,z=o,u=_(!0),b=$(()=>{let i=he(m.schema);if(m.expand&&m.expandField&&!a(u)){const v=ye(i,d=>d.field===m.expandField);if(v>-1){const d=i.length;i.splice(v+1,d)}}return m.layout==="inline"&&(i=i.concat([{field:"action",formItemProps:{labelWidth:"0px"}}])),i}),{register:f,elFormRef:n,methods:l}=je({model:m.model||{}}),r=async()=>{var i;await((i=a(n))==null?void 0:i.validate(async v=>{if(v){const{getFormData:d}=l,w=await d();z("search",w)}}))},x=async()=>{var d;(d=a(n))==null||d.resetFields();const{getFormData:i}=l,v=await i();z("reset",v)},D=$(()=>({textAlign:m.buttomPosition})),U=()=>{var i;(i=a(n))==null||i.resetFields(),u.value=!a(u)};return(i,v)=>{const d=_e,w=Pe,E=Le;return P(),Q(ve,null,[g(E,{inline:e.inline,"is-col":e.isCol,"is-custom":!1,"label-width":e.labelWidth,schema:a(b),class:"-mb-15px","hide-required-asterisk":"",onRegister:a(f)},we({action:k(()=>[e.layout==="inline"?(P(),Q("div",Z,[e.showSearch?(P(),B(w,{key:0,onClick:r},{default:k(()=>[g(d,{class:"mr-5px",icon:"ep:search"}),M(" "+q(a(p)("common.query")),1)]),_:1})):L("",!0),e.showReset?(P(),B(w,{key:1,onClick:x},{default:k(()=>[g(d,{class:"mr-5px",icon:"ep:refresh"}),M(" "+q(a(p)("common.reset")),1)]),_:1})):L("",!0),e.expand?(P(),B(w,{key:2,text:"",onClick:U},{default:k(()=>[M(q(a(p)(a(u)?"common.shrink":"common.expand"))+" ",1),g(d,{icon:a(u)?"ep:arrow-up":"ep:arrow-down"},null,8,["icon"])]),_:1})):L("",!0),V(i.$slots,"actionMore")])):L("",!0)]),_:2},[be(Object.keys(i.$slots),t=>({name:t,fn:k(()=>[V(i.$slots,t)])}))]),1032,["inline","is-col","label-width","schema","onRegister"]),e.layout==="bottom"?(P(),Q("div",{key:0,style:xe(a(D))},[e.showSearch?(P(),B(w,{key:0,type:"primary",onClick:r},{default:k(()=>[g(d,{class:"mr-5px",icon:"ep:search"}),M(" "+q(a(p)("common.query")),1)]),_:1})):L("",!0),e.showReset?(P(),B(w,{key:1,onClick:x},{default:k(()=>[g(d,{class:"mr-5px",icon:"ep:refresh-right"}),M(" "+q(a(p)("common.reset")),1)]),_:1})):L("",!0),e.expand?(P(),B(w,{key:2,text:"",onClick:U},{default:k(()=>[M(q(a(p)(a(u)?"common.shrink":"common.expand"))+" ",1),g(d,{icon:a(u)?"ep:arrow-up":"ep:arrow-down"},null,8,["icon"])]),_:1})):L("",!0),V(i.$slots,"actionMore")],4)):L("",!0)],64)}}}),{t:y}=ne(),se=e=>{const o=Se({pageSize:10,currentPage:1,total:10,tableList:[],params:{...(e==null?void 0:e.defaultParams)||{}},loading:!0,exportLoading:!1,currentRow:null}),p=$(()=>({...o.params,pageSize:o.pageSize,pageNo:o.currentPage}));R(()=>o.currentPage,()=>{f.getList()}),R(()=>o.pageSize,()=>{o.currentPage===1||(o.currentPage=1),f.getList()});const m=_(),z=_(),u=async()=>(await Ae(),a(m)),b=async n=>{let l=1;n instanceof Array?(l=n.length,await Promise.all(n.map(async r=>{await((e==null?void 0:e.delListApi)&&(e==null?void 0:e.delListApi(r)))}))):await((e==null?void 0:e.delListApi)&&(e==null?void 0:e.delListApi(n))),oe.success(y("common.delSuccess")),o.currentPage=(o.total%o.pageSize===l||o.pageSize===1)&&o.currentPage>1?o.currentPage-1:o.currentPage,await f.getList()},f={getList:async()=>{o.loading=!0;const n=await(e==null?void 0:e.getListApi(a(p)).finally(()=>{o.loading=!1}));n&&(o.tableList=n.list,o.total=n.total??0)},setProps:async(n={})=>{const l=await u();l==null||l.setProps(n)},setColumn:async n=>{const l=await u();l==null||l.setColumn(n)},getSelections:async()=>{const n=await u();return(n==null?void 0:n.selections)||[]},setSearchParams:n=>{o.params=Object.assign(o.params,{pageSize:o.pageSize,pageNo:1,...n}),o.currentPage!==1?o.currentPage=1:f.getList()},delList:async(n,l,r=!0)=>{const x=await u();!l||x!=null&&x.selections.length?r?le.confirm(y("common.delMessage"),y("common.confirmTitle"),{confirmButtonText:y("common.ok"),cancelButtonText:y("common.cancel"),type:"warning"}).then(async()=>{await b(n)}):await b(n):oe.warning(y("common.delNoData"))},exportList:async n=>{o.exportLoading=!0,le.confirm(y("common.exportMessage"),y("common.confirmTitle"),{confirmButtonText:y("common.ok"),cancelButtonText:y("common.cancel"),type:"warning"}).then(async()=>{var r;const l=await((r=e==null?void 0:e.exportListApi)==null?void 0:r.call(e,a(p)));l&&$e.excel(l,n)}).finally(()=>{o.exportLoading=!1})}};return e!=null&&e.props&&f.setProps(e.props),{register:(n,l)=>{m.value=n,z.value=l},elTableRef:z,tableObject:o,methods:f,tableMethods:f}}});export{re as _,Re as __tla,ie as a,se as u};
