package cn.iocoder.yudao.module.system.convert.social;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.system.api.social.dto.SocialUserBindReqDTO;
import cn.iocoder.yudao.module.system.api.social.dto.SocialUserUnbindReqDTO;
import cn.iocoder.yudao.module.system.controller.admin.socail.vo.SocialUserBindReqVO;
import cn.iocoder.yudao.module.system.controller.admin.socail.vo.SocialUserUnbindReqVO;
import cn.iocoder.yudao.module.system.controller.admin.socail.vo.user.SocialUserRespVO;
import cn.iocoder.yudao.module.system.dal.dataobject.social.SocialUserDO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-21T09:32:49+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_181 (Oracle Corporation)"
)
public class SocialUserConvertImpl implements SocialUserConvert {

    @Override
    public SocialUserBindReqDTO convert(Long userId, Integer userType, SocialUserBindReqVO reqVO) {
        if ( userId == null && userType == null && reqVO == null ) {
            return null;
        }

        SocialUserBindReqDTO socialUserBindReqDTO = new SocialUserBindReqDTO();

        if ( reqVO != null ) {
            socialUserBindReqDTO.setSocialType( reqVO.getType() );
            socialUserBindReqDTO.setCode( reqVO.getCode() );
            socialUserBindReqDTO.setState( reqVO.getState() );
        }
        socialUserBindReqDTO.setUserId( userId );
        socialUserBindReqDTO.setUserType( userType );

        return socialUserBindReqDTO;
    }

    @Override
    public SocialUserUnbindReqDTO convert(Long userId, Integer userType, SocialUserUnbindReqVO reqVO) {
        if ( userId == null && userType == null && reqVO == null ) {
            return null;
        }

        SocialUserUnbindReqDTO socialUserUnbindReqDTO = new SocialUserUnbindReqDTO();

        if ( reqVO != null ) {
            socialUserUnbindReqDTO.setOpenid( reqVO.getOpenid() );
        }
        socialUserUnbindReqDTO.setUserId( userId );
        socialUserUnbindReqDTO.setUserType( userType );

        return socialUserUnbindReqDTO;
    }

    @Override
    public SocialUserRespVO convert(SocialUserDO bean) {
        if ( bean == null ) {
            return null;
        }

        SocialUserRespVO socialUserRespVO = new SocialUserRespVO();

        socialUserRespVO.setType( bean.getType() );
        socialUserRespVO.setOpenid( bean.getOpenid() );
        socialUserRespVO.setToken( bean.getToken() );
        socialUserRespVO.setRawTokenInfo( bean.getRawTokenInfo() );
        socialUserRespVO.setNickname( bean.getNickname() );
        socialUserRespVO.setAvatar( bean.getAvatar() );
        socialUserRespVO.setRawUserInfo( bean.getRawUserInfo() );
        socialUserRespVO.setCode( bean.getCode() );
        socialUserRespVO.setState( bean.getState() );
        socialUserRespVO.setId( bean.getId() );
        socialUserRespVO.setCreateTime( bean.getCreateTime() );
        socialUserRespVO.setUpdateTime( bean.getUpdateTime() );

        return socialUserRespVO;
    }

    @Override
    public List<SocialUserRespVO> convertList(List<SocialUserDO> list) {
        if ( list == null ) {
            return null;
        }

        List<SocialUserRespVO> list1 = new ArrayList<SocialUserRespVO>( list.size() );
        for ( SocialUserDO socialUserDO : list ) {
            list1.add( convert( socialUserDO ) );
        }

        return list1;
    }

    @Override
    public PageResult<SocialUserRespVO> convertPage(PageResult<SocialUserDO> page) {
        if ( page == null ) {
            return null;
        }

        PageResult<SocialUserRespVO> pageResult = new PageResult<SocialUserRespVO>();

        pageResult.setList( convertList( page.getList() ) );
        pageResult.setTotal( page.getTotal() );

        return pageResult;
    }
}
