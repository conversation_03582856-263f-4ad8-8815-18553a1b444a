package cn.iocoder.yudao.module.diaoyuba.convert.checkin;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.diaoyuba.controller.app.checkin.vo.AppCheckInCreateReqVO;
import cn.iocoder.yudao.module.diaoyuba.controller.app.checkin.vo.AppCheckInExcelVO;
import cn.iocoder.yudao.module.diaoyuba.controller.app.checkin.vo.AppCheckInRespVO;
import cn.iocoder.yudao.module.diaoyuba.controller.app.checkin.vo.AppCheckInUpdateReqVO;
import cn.iocoder.yudao.module.diaoyuba.dal.dataobject.checkin.CheckInDO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-21T09:34:05+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_181 (Oracle Corporation)"
)
public class CheckInConvertImpl implements CheckInConvert {

    @Override
    public CheckInDO convert(AppCheckInCreateReqVO bean) {
        if ( bean == null ) {
            return null;
        }

        CheckInDO.CheckInDOBuilder checkInDO = CheckInDO.builder();

        checkInDO.positionId( bean.getPositionId() );

        return checkInDO.build();
    }

    @Override
    public CheckInDO convert(AppCheckInUpdateReqVO bean) {
        if ( bean == null ) {
            return null;
        }

        CheckInDO.CheckInDOBuilder checkInDO = CheckInDO.builder();

        checkInDO.id( bean.getId() );
        checkInDO.positionId( bean.getPositionId() );

        return checkInDO.build();
    }

    @Override
    public AppCheckInRespVO convert(CheckInDO bean) {
        if ( bean == null ) {
            return null;
        }

        AppCheckInRespVO appCheckInRespVO = new AppCheckInRespVO();

        appCheckInRespVO.setPositionId( bean.getPositionId() );
        appCheckInRespVO.setId( bean.getId() );
        appCheckInRespVO.setCreateTime( bean.getCreateTime() );

        return appCheckInRespVO;
    }

    @Override
    public List<AppCheckInRespVO> convertList(List<CheckInDO> list) {
        if ( list == null ) {
            return null;
        }

        List<AppCheckInRespVO> list1 = new ArrayList<AppCheckInRespVO>( list.size() );
        for ( CheckInDO checkInDO : list ) {
            list1.add( convert( checkInDO ) );
        }

        return list1;
    }

    @Override
    public PageResult<AppCheckInRespVO> convertPage(PageResult<CheckInDO> page) {
        if ( page == null ) {
            return null;
        }

        PageResult<AppCheckInRespVO> pageResult = new PageResult<AppCheckInRespVO>();

        pageResult.setList( convertList( page.getList() ) );
        pageResult.setTotal( page.getTotal() );

        return pageResult;
    }

    @Override
    public List<AppCheckInExcelVO> convertList02(List<CheckInDO> list) {
        if ( list == null ) {
            return null;
        }

        List<AppCheckInExcelVO> list1 = new ArrayList<AppCheckInExcelVO>( list.size() );
        for ( CheckInDO checkInDO : list ) {
            list1.add( checkInDOToAppCheckInExcelVO( checkInDO ) );
        }

        return list1;
    }

    protected AppCheckInExcelVO checkInDOToAppCheckInExcelVO(CheckInDO checkInDO) {
        if ( checkInDO == null ) {
            return null;
        }

        AppCheckInExcelVO appCheckInExcelVO = new AppCheckInExcelVO();

        appCheckInExcelVO.setId( checkInDO.getId() );
        appCheckInExcelVO.setUserId( checkInDO.getUserId() );
        appCheckInExcelVO.setCheckIn( checkInDO.getCheckIn() );
        appCheckInExcelVO.setPositionId( checkInDO.getPositionId() );
        appCheckInExcelVO.setCreateTime( checkInDO.getCreateTime() );

        return appCheckInExcelVO;
    }
}
