package cn.iocoder.yudao.module.diaoyuba.convert.userlike;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.diaoyuba.controller.app.userlike.vo.AppUserLikeCreateReqVO;
import cn.iocoder.yudao.module.diaoyuba.controller.app.userlike.vo.AppUserLikeExcelVO;
import cn.iocoder.yudao.module.diaoyuba.controller.app.userlike.vo.AppUserLikeRespVO;
import cn.iocoder.yudao.module.diaoyuba.controller.app.userlike.vo.AppUserLikeUpdateReqVO;
import cn.iocoder.yudao.module.diaoyuba.dal.dataobject.userlike.UserLikeDO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-21T09:34:05+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_181 (Oracle Corporation)"
)
public class UserLikeConvertImpl implements UserLikeConvert {

    @Override
    public UserLikeDO convert(AppUserLikeCreateReqVO bean) {
        if ( bean == null ) {
            return null;
        }

        UserLikeDO.UserLikeDOBuilder userLikeDO = UserLikeDO.builder();

        userLikeDO.userId( bean.getUserId() );
        userLikeDO.positionId( bean.getPositionId() );

        return userLikeDO.build();
    }

    @Override
    public UserLikeDO convert(AppUserLikeUpdateReqVO bean) {
        if ( bean == null ) {
            return null;
        }

        UserLikeDO.UserLikeDOBuilder userLikeDO = UserLikeDO.builder();

        userLikeDO.id( bean.getId() );
        userLikeDO.userId( bean.getUserId() );
        userLikeDO.positionId( bean.getPositionId() );

        return userLikeDO.build();
    }

    @Override
    public AppUserLikeRespVO convert(UserLikeDO bean) {
        if ( bean == null ) {
            return null;
        }

        AppUserLikeRespVO appUserLikeRespVO = new AppUserLikeRespVO();

        appUserLikeRespVO.setUserId( bean.getUserId() );
        appUserLikeRespVO.setPositionId( bean.getPositionId() );
        appUserLikeRespVO.setId( bean.getId() );
        appUserLikeRespVO.setCreateTime( bean.getCreateTime() );

        return appUserLikeRespVO;
    }

    @Override
    public List<AppUserLikeRespVO> convertList(List<UserLikeDO> list) {
        if ( list == null ) {
            return null;
        }

        List<AppUserLikeRespVO> list1 = new ArrayList<AppUserLikeRespVO>( list.size() );
        for ( UserLikeDO userLikeDO : list ) {
            list1.add( convert( userLikeDO ) );
        }

        return list1;
    }

    @Override
    public PageResult<AppUserLikeRespVO> convertPage(PageResult<UserLikeDO> page) {
        if ( page == null ) {
            return null;
        }

        PageResult<AppUserLikeRespVO> pageResult = new PageResult<AppUserLikeRespVO>();

        pageResult.setList( convertList( page.getList() ) );
        pageResult.setTotal( page.getTotal() );

        return pageResult;
    }

    @Override
    public List<AppUserLikeExcelVO> convertList02(List<UserLikeDO> list) {
        if ( list == null ) {
            return null;
        }

        List<AppUserLikeExcelVO> list1 = new ArrayList<AppUserLikeExcelVO>( list.size() );
        for ( UserLikeDO userLikeDO : list ) {
            list1.add( userLikeDOToAppUserLikeExcelVO( userLikeDO ) );
        }

        return list1;
    }

    protected AppUserLikeExcelVO userLikeDOToAppUserLikeExcelVO(UserLikeDO userLikeDO) {
        if ( userLikeDO == null ) {
            return null;
        }

        AppUserLikeExcelVO appUserLikeExcelVO = new AppUserLikeExcelVO();

        appUserLikeExcelVO.setId( userLikeDO.getId() );
        appUserLikeExcelVO.setUserId( userLikeDO.getUserId() );
        appUserLikeExcelVO.setPositionId( userLikeDO.getPositionId() );
        appUserLikeExcelVO.setCreateTime( userLikeDO.getCreateTime() );

        return appUserLikeExcelVO;
    }
}
