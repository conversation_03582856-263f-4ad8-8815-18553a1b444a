import{d as H,cd as Q,b as S,r as w,f as T,o as n,c as I,a as r,g as b,t as D,i as a,w as l,q as J,j as g,x as O,n as P,_ as W,H as $,cj as F,E as G,b9 as K,$ as L,a0 as N,__tla as R}from"./index-97fffa0c.js";import{W as X,__tla as Y}from"./main-4dd868b0.js";import Z,{__tla as aa}from"./main-7292042e.js";import{u as ta,U as ea,__tla as la}from"./useUpload-36312237.js";import{u as ra,__tla as _a}from"./useMessage-18385d4a.js";import{_ as sa}from"./_plugin-vue_export-helper-1b428a4d.js";import{__tla as ca}from"./index.vue_vue_type_script_setup_true_lang-d31568cf.js";import{__tla as oa}from"./index-8d6db4ce.js";import{__tla as ua}from"./main-17919147.js";import{__tla as ia}from"./el-image-1637bc2a.js";import{__tla as na}from"./el-image-viewer-fddfe81d.js";import{__tla as ma}from"./main.vue_vue_type_script_setup_true_lang-4cb32a33.js";import{__tla as da}from"./index-aa57e946.js";import{__tla as pa}from"./index-3b46e2ef.js";import{__tla as fa}from"./formatTime-9d54d2c5.js";let k,ya=Promise.all([(()=>{try{return R}catch{}})(),(()=>{try{return Y}catch{}})(),(()=>{try{return aa}catch{}})(),(()=>{try{return la}catch{}})(),(()=>{try{return _a}catch{}})(),(()=>{try{return ca}catch{}})(),(()=>{try{return oa}catch{}})(),(()=>{try{return ua}catch{}})(),(()=>{try{return ia}catch{}})(),(()=>{try{return na}catch{}})(),(()=>{try{return ma}catch{}})(),(()=>{try{return da}catch{}})(),(()=>{try{return pa}catch{}})(),(()=>{try{return fa}catch{}})()]).then(async()=>{let m,d,p;m={key:0,class:"select-item2"},d={class:"item-name"},p=(_=>(L("data-v-782ce0e7"),_=_(),N(),_))(()=>b("div",{class:"el-upload__tip"}," \u683C\u5F0F\u652F\u6301 mp3/wma/wav/amr\uFF0C\u6587\u4EF6\u5927\u5C0F\u4E0D\u8D85\u8FC7 2M\uFF0C\u64AD\u653E\u957F\u5EA6\u4E0D\u8D85\u8FC7 60s ",-1)),k=sa(H({__name:"TabVoice",props:{modelValue:{}},emits:["update:modelValue"],setup(_,{emit:j}){const x=ra(),U={Authorization:"Bearer "+Q()},C=_,M=j,e=S({get:()=>C.modelValue,set:t=>M("update:modelValue",t)}),s=w(!1),f=w([]),o=T({accountId:e.value.accountId,type:"voice",title:"",introduction:""}),q=t=>ta(ea.Voice,10)(t),z=t=>{if(t.code!==0)return x.error("\u4E0A\u4F20\u51FA\u9519\uFF1A"+t.msg),!1;f.value=[],o.title="",o.introduction="",y(t.data)},A=()=>{e.value.mediaId=null,e.value.url=null,e.value.name=null},y=t=>{s.value=!1,e.value.mediaId=t.mediaId,e.value.url=t.url,e.value.name=t.name};return(t,c)=>{const u=P,h=W,i=$,B=F,v=G,E=K;return n(),I("div",null,[r(e).url?(n(),I("div",m,[b("p",d,D(r(e).name),1),a(u,{class:"ope-row",justify:"center"},{default:l(()=>[a(r(Z),{url:r(e).url},null,8,["url"])]),_:1}),a(u,{class:"ope-row",justify:"center"},{default:l(()=>[a(i,{type:"danger",circle:"",onClick:A},{default:l(()=>[a(h,{icon:"ep:delete"})]),_:1})]),_:1})])):(n(),J(u,{key:1,style:{"text-align":"center"}},{default:l(()=>[a(v,{span:12,class:"col-select"},{default:l(()=>[a(i,{type:"success",onClick:c[0]||(c[0]=V=>s.value=!0)},{default:l(()=>[g(" \u7D20\u6750\u5E93\u9009\u62E9"),a(h,{icon:"ep:circle-check"})]),_:1}),a(B,{title:"\u9009\u62E9\u8BED\u97F3",modelValue:r(s),"onUpdate:modelValue":c[1]||(c[1]=V=>O(s)?s.value=V:null),width:"90%","append-to-body":"","destroy-on-close":""},{default:l(()=>[a(r(X),{type:"voice","account-id":r(e).accountId,onSelectMaterial:y},null,8,["account-id"])]),_:1},8,["modelValue"])]),_:1}),a(v,{span:12,class:"col-add"},{default:l(()=>[a(E,{action:"/admin-api/mp/material/upload-temporary",headers:U,multiple:"",limit:1,"file-list":r(f),data:r(o),"before-upload":q,"on-success":z},{tip:l(()=>[p]),default:l(()=>[a(i,{type:"primary"},{default:l(()=>[g("\u70B9\u51FB\u4E0A\u4F20")]),_:1})]),_:1},8,["file-list","data"])]),_:1})]),_:1}))])}}}),[["__scopeId","data-v-782ce0e7"]])});export{ya as __tla,k as default};
