import{d as m,r as _,A as y,o as l,c as p,i as e,w as f,a as r,B as h,q as d,a3 as k,F as g,L as v,__tla as w}from"./index-97fffa0c.js";import{_ as x,__tla as b}from"./ContentWrap.vue_vue_type_script_setup_true_lang-a574ccef.js";import{_ as j,__tla as q}from"./IFrame.vue_vue_type_script_setup_true_lang-f3d5b5e9.js";import{_ as A,__tla as B}from"./index-b39a19a1.js";import{b as F,__tla as I}from"./index-74e8f36e.js";import{__tla as L}from"./el-card-6c7c099d.js";import"./_plugin-vue_export-helper-1b428a4d.js";let n,P=Promise.all([(()=>{try{return w}catch{}})(),(()=>{try{return b}catch{}})(),(()=>{try{return q}catch{}})(),(()=>{try{return B}catch{}})(),(()=>{try{return I}catch{}})(),(()=>{try{return L}catch{}})()]).then(async()=>{n=m({name:"InfraSkyWalking",__name:"index",setup(S){const t=_(!0),s=_("http://skywalking.shop.iocoder.cn");return y(async()=>{try{const a=await F("url.skywalking");a&&a.length>0&&(s.value=a)}finally{t.value=!1}}),(a,W)=>{const o=A,c=j,i=x,u=v;return l(),p(g,null,[e(o,{title:"\u670D\u52A1\u76D1\u63A7",url:"https://doc.iocoder.cn/server-monitor/"}),e(i,null,{default:f(()=>[r(t)?k("",!0):h((l(),d(c,{key:0,src:r(s)},null,8,["src"])),[[u,r(t)]])]),_:1})],64)}}})});export{P as __tla,n as default};
