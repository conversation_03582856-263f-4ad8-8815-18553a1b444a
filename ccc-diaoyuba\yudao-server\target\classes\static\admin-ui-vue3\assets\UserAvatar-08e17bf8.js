import{aL as si,d as mt,aN as hi,b as rt,h as ia,aP as li,r as X,aF as ci,aY as kt,aZ as zt,aS as St,a_ as di,a$ as aa,b0 as na,i as B,b1 as J,j as Jt,b2 as pi,b3 as ra,aR as oa,p as I,b4 as sa,b5 as ha,A as la,b6 as ca,a as v,o as q,c as gt,B as da,a2 as pa,g as ot,au as ui,V as F,Z as te,b7 as mi,w as H,x as ua,t as gi,q as Wt,a3 as ft,b8 as ma,b9 as ga,H as fi,ax as fa,__tla as va}from"./index-97fffa0c.js";import{b as ba,__tla as ya}from"./profile-9d2d9ae0.js";import{E as vi,__tla as wa}from"./el-avatar-c773bffa.js";import{_ as xa,__tla as Ca}from"./Dialog.vue_vue_type_style_index_0_lang-5af0bc2b.js";import{_ as Ma,__tla as Da}from"./XButton-dd4d8780.js";import{u as ka,__tla as Ba}from"./useMessage-18385d4a.js";import{_ as bi}from"./_plugin-vue_export-helper-1b428a4d.js";let yi,Oa=Promise.all([(()=>{try{return va}catch{}})(),(()=>{try{return ya}catch{}})(),(()=>{try{return wa}catch{}})(),(()=>{try{return Ca}catch{}})(),(()=>{try{return Da}catch{}})(),(()=>{try{return Ba}catch{}})()]).then(async()=>{const ee=mt({name:"ElSpaceItem",props:si({prefixCls:{type:String}}),setup(t,{slots:i}){const e=hi("space"),n=rt(()=>`${t.prefixCls||e.b()}__item`);return()=>ia("div",{class:n.value},li(i,"default"))}}),ie={small:8,default:12,large:16},wi=mt({name:"ElSpace",props:si({direction:{type:String,values:["horizontal","vertical"],default:"horizontal"},class:{type:St([String,Object,Array]),default:""},style:{type:St([String,Array,Object]),default:""},alignment:{type:St(String),default:"center"},prefixCls:{type:String},spacer:{type:St([Object,String,Number,Array]),default:null,validator:t=>di(t)||zt(t)||aa(t)},wrap:Boolean,fill:Boolean,fillRatio:{type:Number,default:100},size:{type:[String,Array,Number],values:na,validator:t=>zt(t)||kt(t)&&t.length===2&&t.every(zt)}}),setup(t,{slots:i}){const{classes:e,containerStyle:n,itemStyle:a}=function(h){const s=hi("space"),l=rt(()=>[s.b(),s.m(h.direction),h.class]),o=X(0),d=X(0),p=rt(()=>[h.wrap||h.fill?{flexWrap:"wrap",marginBottom:`-${d.value}px`}:{},{alignItems:h.alignment},h.style]),c=rt(()=>[{paddingBottom:`${d.value}px`,marginRight:`${o.value}px`},h.fill?{flexGrow:1,minWidth:`${h.fillRatio}%`}:{}]);return ci(()=>{const{size:m="small",wrap:f,direction:g,fill:b}=h;if(kt(m)){const[y=0,w=0]=m;o.value=y,d.value=w}else{let y;y=zt(m)?m:ie[m||"small"]||ie.small,(f||b)&&g==="horizontal"?o.value=d.value=y:g==="horizontal"?(o.value=y,d.value=0):(d.value=y,o.value=0)}}),{classes:l,containerStyle:p,itemStyle:c}}(t);function r(h,s="",l=[]){const{prefixCls:o}=t;return h.forEach((d,p)=>{pi(d)?kt(d.children)&&d.children.forEach((c,m)=>{pi(c)&&kt(c.children)?r(c.children,`${s+m}-`,l):l.push(B(ee,{style:a.value,prefixCls:o,key:`nested-${s+m}`},{default:()=>[c]},J.PROPS|J.STYLE,["style","prefixCls"]))}):ra(d)&&l.push(B(ee,{style:a.value,prefixCls:o,key:`LoopKey${s+p}`},{default:()=>[d]},J.PROPS|J.STYLE,["style","prefixCls"]))}),l}return()=>{var h;const{spacer:s,direction:l}=t,o=li(i,"default",{key:0},()=>[]);if(((h=o.children)!=null?h:[]).length===0)return null;if(kt(o.children)){let d=r(o.children);if(s){const p=d.length-1;d=d.reduce((c,m,f)=>{const g=[...c,m];return f!==p&&g.push(B("span",{style:[a.value,l==="vertical"?"width: 100%":null],key:f},[di(s)?s:Jt(s,J.TEXT)],J.STYLE)),g},[])}return B("div",{class:e.value,style:n.value},d,J.STYLE|J.CLASS)}return o.children}}}),xi=oa(wi);function ae(t,i){var e=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);i&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(t,a).enumerable})),e.push.apply(e,n)}return e}function ne(t){for(var i=1;i<arguments.length;i++){var e=arguments[i]!=null?arguments[i]:{};i%2?ae(Object(e),!0).forEach(function(n){Ci(t,n,e[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(e)):ae(Object(e)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(e,n))})}return t}function Lt(t){return Lt=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(i){return typeof i}:function(i){return i&&typeof Symbol=="function"&&i.constructor===Symbol&&i!==Symbol.prototype?"symbol":typeof i},Lt(t)}function re(t,i){for(var e=0;e<i.length;e++){var n=i[e];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,se(n.key),n)}}function Ci(t,i,e){return(i=se(i))in t?Object.defineProperty(t,i,{value:e,enumerable:!0,configurable:!0,writable:!0}):t[i]=e,t}function oe(t){return function(i){if(Array.isArray(i))return Rt(i)}(t)||function(i){if(typeof Symbol<"u"&&i[Symbol.iterator]!=null||i["@@iterator"]!=null)return Array.from(i)}(t)||function(i,e){if(i){if(typeof i=="string")return Rt(i,e);var n=Object.prototype.toString.call(i).slice(8,-1);if(n==="Object"&&i.constructor&&(n=i.constructor.name),n==="Map"||n==="Set")return Array.from(i);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Rt(i,e)}}(t)||function(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}()}function Rt(t,i){(i==null||i>t.length)&&(i=t.length);for(var e=0,n=new Array(i);e<i;e++)n[e]=t[e];return n}function se(t){var i=function(e,n){if(typeof e!="object"||e===null)return e;var a=e[Symbol.toPrimitive];if(a!==void 0){var r=a.call(e,n||"default");if(typeof r!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return(n==="string"?String:Number)(e)}(t,"string");return typeof i=="symbol"?i:String(i)}var Bt=typeof window<"u"&&window.document!==void 0,U=Bt?window:{},Nt=!(!Bt||!U.document.documentElement)&&"ontouchstart"in U.document.documentElement,Ht=!!Bt&&"PointerEvent"in U,O="cropper",Yt="all",he="crop",le="move",ce="zoom",tt="e",et="w",st="s",Z="n",vt="ne",bt="nw",yt="se",wt="sw",At="".concat(O,"-crop"),de="".concat(O,"-disabled"),R="".concat(O,"-hidden"),pe="".concat(O,"-hide"),Mi="".concat(O,"-invisible"),Ot="".concat(O,"-modal"),Xt="".concat(O,"-move"),xt="".concat(O,"Action"),_t="".concat(O,"Preview"),It="crop",ue="move",me="none",jt="crop",Pt="cropend",Ut="cropmove",$t="cropstart",ge="dblclick",fe=Ht?"pointerdown":Nt?"touchstart":"mousedown",ve=Ht?"pointermove":Nt?"touchmove":"mousemove",be=Ht?"pointerup pointercancel":Nt?"touchend touchcancel":"mouseup",ye="ready",we="resize",xe="wheel",qt="zoom",Ce="image/jpeg",Di=/^e|w|s|n|se|sw|ne|nw|all|crop|move|zoom$/,ki=/^data:/,Bi=/^data:image\/jpeg;base64,/,Oi=/^img|canvas$/i,Me={viewMode:0,dragMode:It,initialAspectRatio:NaN,aspectRatio:NaN,data:null,preview:"",responsive:!0,restore:!0,checkCrossOrigin:!0,checkOrientation:!0,modal:!0,guides:!0,center:!0,highlight:!0,background:!0,autoCrop:!0,autoCropArea:.8,movable:!0,rotatable:!0,scalable:!0,zoomable:!0,zoomOnTouch:!0,zoomOnWheel:!0,wheelZoomRatio:.1,cropBoxMovable:!0,cropBoxResizable:!0,toggleDragModeOnDblclick:!0,minCanvasWidth:0,minCanvasHeight:0,minCropBoxWidth:0,minCropBoxHeight:0,minContainerWidth:200,minContainerHeight:100,ready:null,cropstart:null,cropmove:null,cropend:null,crop:null,zoom:null},_i=Number.isNaN||U.isNaN;function C(t){return typeof t=="number"&&!_i(t)}var De=function(t){return t>0&&t<1/0};function Vt(t){return t===void 0}function it(t){return Lt(t)==="object"&&t!==null}var Ti=Object.prototype.hasOwnProperty;function ht(t){if(!it(t))return!1;try{var i=t.constructor,e=i.prototype;return i&&e&&Ti.call(e,"isPrototypeOf")}catch{return!1}}function N(t){return typeof t=="function"}var Ei=Array.prototype.slice;function ke(t){return Array.from?Array.from(t):Ei.call(t)}function E(t,i){return t&&N(i)&&(Array.isArray(t)||C(t.length)?ke(t).forEach(function(e,n){i.call(t,e,n,t)}):it(t)&&Object.keys(t).forEach(function(e){i.call(t,t[e],e,t)})),t}var _=Object.assign||function(t){for(var i=arguments.length,e=new Array(i>1?i-1:0),n=1;n<i;n++)e[n-1]=arguments[n];return it(t)&&e.length>0&&e.forEach(function(a){it(a)&&Object.keys(a).forEach(function(r){t[r]=a[r]})}),t},zi=/\.\d*(?:0|9){12}\d*$/;function lt(t){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:1e11;return zi.test(t)?Math.round(t*i)/i:t}var Si=/^width|height|left|top|marginLeft|marginTop$/;function K(t,i){var e=t.style;E(i,function(n,a){Si.test(a)&&C(n)&&(n="".concat(n,"px")),e[a]=n})}function z(t,i){if(i)if(C(t.length))E(t,function(n){z(n,i)});else if(t.classList)t.classList.add(i);else{var e=t.className.trim();e?e.indexOf(i)<0&&(t.className="".concat(e," ").concat(i)):t.className=i}}function $(t,i){i&&(C(t.length)?E(t,function(e){$(e,i)}):t.classList?t.classList.remove(i):t.className.indexOf(i)>=0&&(t.className=t.className.replace(i,"")))}function ct(t,i,e){i&&(C(t.length)?E(t,function(n){ct(n,i,e)}):e?z(t,i):$(t,i))}var Wi=/([a-z\d])([A-Z])/g;function Ft(t){return t.replace(Wi,"$1-$2").toLowerCase()}function Zt(t,i){return it(t[i])?t[i]:t.dataset?t.dataset[i]:t.getAttribute("data-".concat(Ft(i)))}function Ct(t,i,e){it(e)?t[i]=e:t.dataset?t.dataset[i]=e:t.setAttribute("data-".concat(Ft(i)),e)}var Be=/\s\s*/,Oe=function(){var t=!1;if(Bt){var i=!1,e=function(){},n=Object.defineProperty({},"once",{get:function(){return t=!0,i},set:function(a){i=a}});U.addEventListener("test",e,n),U.removeEventListener("test",e,n)}return t}();function j(t,i,e){var n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{},a=e;i.trim().split(Be).forEach(function(r){if(!Oe){var h=t.listeners;h&&h[r]&&h[r][e]&&(a=h[r][e],delete h[r][e],Object.keys(h[r]).length===0&&delete h[r],Object.keys(h).length===0&&delete t.listeners)}t.removeEventListener(r,a,n)})}function A(t,i,e){var n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{},a=e;i.trim().split(Be).forEach(function(r){if(n.once&&!Oe){var h=t.listeners,s=h===void 0?{}:h;a=function(){delete s[r][e],t.removeEventListener(r,a,n);for(var l=arguments.length,o=new Array(l),d=0;d<l;d++)o[d]=arguments[d];e.apply(t,o)},s[r]||(s[r]={}),s[r][e]&&t.removeEventListener(r,s[r][e],n),s[r][e]=a,t.listeners=s}t.addEventListener(r,a,n)})}function dt(t,i,e){var n;return N(Event)&&N(CustomEvent)?n=new CustomEvent(i,{detail:e,bubbles:!0,cancelable:!0}):(n=document.createEvent("CustomEvent")).initCustomEvent(i,!0,!0,e),t.dispatchEvent(n)}function _e(t){var i=t.getBoundingClientRect();return{left:i.left+(window.pageXOffset-document.documentElement.clientLeft),top:i.top+(window.pageYOffset-document.documentElement.clientTop)}}var Kt=U.location,Li=/^(\w+:)\/\/([^:/?#]*):?(\d*)/i;function Te(t){var i=t.match(Li);return i!==null&&(i[1]!==Kt.protocol||i[2]!==Kt.hostname||i[3]!==Kt.port)}function Ee(t){var i="timestamp=".concat(new Date().getTime());return t+(t.indexOf("?")===-1?"?":"&")+i}function Mt(t){var i=t.rotate,e=t.scaleX,n=t.scaleY,a=t.translateX,r=t.translateY,h=[];C(a)&&a!==0&&h.push("translateX(".concat(a,"px)")),C(r)&&r!==0&&h.push("translateY(".concat(r,"px)")),C(i)&&i!==0&&h.push("rotate(".concat(i,"deg)")),C(e)&&e!==1&&h.push("scaleX(".concat(e,")")),C(n)&&n!==1&&h.push("scaleY(".concat(n,")"));var s=h.length?h.join(" "):"none";return{WebkitTransform:s,msTransform:s,transform:s}}function Tt(t,i){var e=t.pageX,n=t.pageY,a={endX:e,endY:n};return i?a:ne({startX:e,startY:n},a)}function Q(t){var i=t.aspectRatio,e=t.height,n=t.width,a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"contain",r=De(n),h=De(e);if(r&&h){var s=e*i;a==="contain"&&s>n||a==="cover"&&s<n?e=n/i:n=e*i}else r?e=n/i:h&&(n=e*i);return{width:n,height:e}}var ze=String.fromCharCode,Ri=/^data:.*,/;function Ni(t){var i,e=new DataView(t);try{var n,a,r;if(e.getUint8(0)===255&&e.getUint8(1)===216)for(var h=e.byteLength,s=2;s+1<h;){if(e.getUint8(s)===255&&e.getUint8(s+1)===225){a=s;break}s+=1}if(a){var l=a+10;if(function(f,g,b){var y="";b+=g;for(var w=g;w<b;w+=1)y+=ze(f.getUint8(w));return y}(e,a+4,4)==="Exif"){var o=e.getUint16(l);if(((n=o===18761)||o===19789)&&e.getUint16(l+2,n)===42){var d=e.getUint32(l+4,n);d>=8&&(r=l+d)}}}if(r){var p,c,m=e.getUint16(r,n);for(c=0;c<m;c+=1)if(p=r+12*c+2,e.getUint16(p,n)===274){p+=8,i=e.getUint16(p,n),e.setUint16(p,1,n);break}}}catch{i=1}return i}var Hi={render:function(){this.initContainer(),this.initCanvas(),this.initCropBox(),this.renderCanvas(),this.cropped&&this.renderCropBox()},initContainer:function(){var t=this.element,i=this.options,e=this.container,n=this.cropper,a=Number(i.minContainerWidth),r=Number(i.minContainerHeight);z(n,R),$(t,R);var h={width:Math.max(e.offsetWidth,a>=0?a:200),height:Math.max(e.offsetHeight,r>=0?r:100)};this.containerData=h,K(n,{width:h.width,height:h.height}),z(t,R),$(n,R)},initCanvas:function(){var t=this.containerData,i=this.imageData,e=this.options.viewMode,n=Math.abs(i.rotate)%180==90,a=n?i.naturalHeight:i.naturalWidth,r=n?i.naturalWidth:i.naturalHeight,h=a/r,s=t.width,l=t.height;t.height*h>t.width?e===3?s=t.height*h:l=t.width/h:e===3?l=t.width/h:s=t.height*h;var o={aspectRatio:h,naturalWidth:a,naturalHeight:r,width:s,height:l};this.canvasData=o,this.limited=e===1||e===2,this.limitCanvas(!0,!0),o.width=Math.min(Math.max(o.width,o.minWidth),o.maxWidth),o.height=Math.min(Math.max(o.height,o.minHeight),o.maxHeight),o.left=(t.width-o.width)/2,o.top=(t.height-o.height)/2,o.oldLeft=o.left,o.oldTop=o.top,this.initialCanvasData=_({},o)},limitCanvas:function(t,i){var e=this.options,n=this.containerData,a=this.canvasData,r=this.cropBoxData,h=e.viewMode,s=a.aspectRatio,l=this.cropped&&r;if(t){var o=Number(e.minCanvasWidth)||0,d=Number(e.minCanvasHeight)||0;h>1?(o=Math.max(o,n.width),d=Math.max(d,n.height),h===3&&(d*s>o?o=d*s:d=o/s)):h>0&&(o?o=Math.max(o,l?r.width:0):d?d=Math.max(d,l?r.height:0):l&&(o=r.width,(d=r.height)*s>o?o=d*s:d=o/s));var p=Q({aspectRatio:s,width:o,height:d});o=p.width,d=p.height,a.minWidth=o,a.minHeight=d,a.maxWidth=1/0,a.maxHeight=1/0}if(i)if(h>(l?0:1)){var c=n.width-a.width,m=n.height-a.height;a.minLeft=Math.min(0,c),a.minTop=Math.min(0,m),a.maxLeft=Math.max(0,c),a.maxTop=Math.max(0,m),l&&this.limited&&(a.minLeft=Math.min(r.left,r.left+(r.width-a.width)),a.minTop=Math.min(r.top,r.top+(r.height-a.height)),a.maxLeft=r.left,a.maxTop=r.top,h===2&&(a.width>=n.width&&(a.minLeft=Math.min(0,c),a.maxLeft=Math.max(0,c)),a.height>=n.height&&(a.minTop=Math.min(0,m),a.maxTop=Math.max(0,m))))}else a.minLeft=-a.width,a.minTop=-a.height,a.maxLeft=n.width,a.maxTop=n.height},renderCanvas:function(t,i){var e=this.canvasData,n=this.imageData;if(i){var a=function(o){var d=o.width,p=o.height,c=o.degree;if((c=Math.abs(c)%180)==90)return{width:p,height:d};var m=c%90*Math.PI/180,f=Math.sin(m),g=Math.cos(m),b=d*g+p*f,y=d*f+p*g;return c>90?{width:y,height:b}:{width:b,height:y}}({width:n.naturalWidth*Math.abs(n.scaleX||1),height:n.naturalHeight*Math.abs(n.scaleY||1),degree:n.rotate||0}),r=a.width,h=a.height,s=e.width*(r/e.naturalWidth),l=e.height*(h/e.naturalHeight);e.left-=(s-e.width)/2,e.top-=(l-e.height)/2,e.width=s,e.height=l,e.aspectRatio=r/h,e.naturalWidth=r,e.naturalHeight=h,this.limitCanvas(!0,!1)}(e.width>e.maxWidth||e.width<e.minWidth)&&(e.left=e.oldLeft),(e.height>e.maxHeight||e.height<e.minHeight)&&(e.top=e.oldTop),e.width=Math.min(Math.max(e.width,e.minWidth),e.maxWidth),e.height=Math.min(Math.max(e.height,e.minHeight),e.maxHeight),this.limitCanvas(!1,!0),e.left=Math.min(Math.max(e.left,e.minLeft),e.maxLeft),e.top=Math.min(Math.max(e.top,e.minTop),e.maxTop),e.oldLeft=e.left,e.oldTop=e.top,K(this.canvas,_({width:e.width,height:e.height},Mt({translateX:e.left,translateY:e.top}))),this.renderImage(t),this.cropped&&this.limited&&this.limitCropBox(!0,!0)},renderImage:function(t){var i=this.canvasData,e=this.imageData,n=e.naturalWidth*(i.width/i.naturalWidth),a=e.naturalHeight*(i.height/i.naturalHeight);_(e,{width:n,height:a,left:(i.width-n)/2,top:(i.height-a)/2}),K(this.image,_({width:e.width,height:e.height},Mt(_({translateX:e.left,translateY:e.top},e)))),t&&this.output()},initCropBox:function(){var t=this.options,i=this.canvasData,e=t.aspectRatio||t.initialAspectRatio,n=Number(t.autoCropArea)||.8,a={width:i.width,height:i.height};e&&(i.height*e>i.width?a.height=a.width/e:a.width=a.height*e),this.cropBoxData=a,this.limitCropBox(!0,!0),a.width=Math.min(Math.max(a.width,a.minWidth),a.maxWidth),a.height=Math.min(Math.max(a.height,a.minHeight),a.maxHeight),a.width=Math.max(a.minWidth,a.width*n),a.height=Math.max(a.minHeight,a.height*n),a.left=i.left+(i.width-a.width)/2,a.top=i.top+(i.height-a.height)/2,a.oldLeft=a.left,a.oldTop=a.top,this.initialCropBoxData=_({},a)},limitCropBox:function(t,i){var e=this.options,n=this.containerData,a=this.canvasData,r=this.cropBoxData,h=this.limited,s=e.aspectRatio;if(t){var l=Number(e.minCropBoxWidth)||0,o=Number(e.minCropBoxHeight)||0,d=h?Math.min(n.width,a.width,a.width+a.left,n.width-a.left):n.width,p=h?Math.min(n.height,a.height,a.height+a.top,n.height-a.top):n.height;l=Math.min(l,n.width),o=Math.min(o,n.height),s&&(l&&o?o*s>l?o=l/s:l=o*s:l?o=l/s:o&&(l=o*s),p*s>d?p=d/s:d=p*s),r.minWidth=Math.min(l,d),r.minHeight=Math.min(o,p),r.maxWidth=d,r.maxHeight=p}i&&(h?(r.minLeft=Math.max(0,a.left),r.minTop=Math.max(0,a.top),r.maxLeft=Math.min(n.width,a.left+a.width)-r.width,r.maxTop=Math.min(n.height,a.top+a.height)-r.height):(r.minLeft=0,r.minTop=0,r.maxLeft=n.width-r.width,r.maxTop=n.height-r.height))},renderCropBox:function(){var t=this.options,i=this.containerData,e=this.cropBoxData;(e.width>e.maxWidth||e.width<e.minWidth)&&(e.left=e.oldLeft),(e.height>e.maxHeight||e.height<e.minHeight)&&(e.top=e.oldTop),e.width=Math.min(Math.max(e.width,e.minWidth),e.maxWidth),e.height=Math.min(Math.max(e.height,e.minHeight),e.maxHeight),this.limitCropBox(!1,!0),e.left=Math.min(Math.max(e.left,e.minLeft),e.maxLeft),e.top=Math.min(Math.max(e.top,e.minTop),e.maxTop),e.oldLeft=e.left,e.oldTop=e.top,t.movable&&t.cropBoxMovable&&Ct(this.face,xt,e.width>=i.width&&e.height>=i.height?le:Yt),K(this.cropBox,_({width:e.width,height:e.height},Mt({translateX:e.left,translateY:e.top}))),this.cropped&&this.limited&&this.limitCanvas(!0,!0),this.disabled||this.output()},output:function(){this.preview(),dt(this.element,jt,this.getData())}},Yi={initPreview:function(){var t=this.element,i=this.crossOrigin,e=this.options.preview,n=i?this.crossOriginUrl:this.url,a=t.alt||"The image to preview",r=document.createElement("img");if(i&&(r.crossOrigin=i),r.src=n,r.alt=a,this.viewBox.appendChild(r),this.viewBoxImage=r,e){var h=e;typeof e=="string"?h=t.ownerDocument.querySelectorAll(e):e.querySelector&&(h=[e]),this.previews=h,E(h,function(s){var l=document.createElement("img");Ct(s,_t,{width:s.offsetWidth,height:s.offsetHeight,html:s.innerHTML}),i&&(l.crossOrigin=i),l.src=n,l.alt=a,l.style.cssText='display:block;width:100%;height:auto;min-width:0!important;min-height:0!important;max-width:none!important;max-height:none!important;image-orientation:0deg!important;"',s.innerHTML="",s.appendChild(l)})}},resetPreview:function(){E(this.previews,function(t){var i=Zt(t,_t);K(t,{width:i.width,height:i.height}),t.innerHTML=i.html,function(e,n){if(it(e[n]))try{delete e[n]}catch{e[n]=void 0}else if(e.dataset)try{delete e.dataset[n]}catch{e.dataset[n]=void 0}else e.removeAttribute("data-".concat(Ft(n)))}(t,_t)})},preview:function(){var t=this.imageData,i=this.canvasData,e=this.cropBoxData,n=e.width,a=e.height,r=t.width,h=t.height,s=e.left-i.left-t.left,l=e.top-i.top-t.top;this.cropped&&!this.disabled&&(K(this.viewBoxImage,_({width:r,height:h},Mt(_({translateX:-s,translateY:-l},t)))),E(this.previews,function(o){var d=Zt(o,_t),p=d.width,c=d.height,m=p,f=c,g=1;n&&(f=a*(g=p/n)),a&&f>c&&(m=n*(g=c/a),f=c),K(o,{width:m,height:f}),K(o.getElementsByTagName("img")[0],_({width:r*g,height:h*g},Mt(_({translateX:-s*g,translateY:-l*g},t))))}))}},Ai={bind:function(){var t=this.element,i=this.options,e=this.cropper;N(i.cropstart)&&A(t,$t,i.cropstart),N(i.cropmove)&&A(t,Ut,i.cropmove),N(i.cropend)&&A(t,Pt,i.cropend),N(i.crop)&&A(t,jt,i.crop),N(i.zoom)&&A(t,qt,i.zoom),A(e,fe,this.onCropStart=this.cropStart.bind(this)),i.zoomable&&i.zoomOnWheel&&A(e,xe,this.onWheel=this.wheel.bind(this),{passive:!1,capture:!0}),i.toggleDragModeOnDblclick&&A(e,ge,this.onDblclick=this.dblclick.bind(this)),A(t.ownerDocument,ve,this.onCropMove=this.cropMove.bind(this)),A(t.ownerDocument,be,this.onCropEnd=this.cropEnd.bind(this)),i.responsive&&A(window,we,this.onResize=this.resize.bind(this))},unbind:function(){var t=this.element,i=this.options,e=this.cropper;N(i.cropstart)&&j(t,$t,i.cropstart),N(i.cropmove)&&j(t,Ut,i.cropmove),N(i.cropend)&&j(t,Pt,i.cropend),N(i.crop)&&j(t,jt,i.crop),N(i.zoom)&&j(t,qt,i.zoom),j(e,fe,this.onCropStart),i.zoomable&&i.zoomOnWheel&&j(e,xe,this.onWheel,{passive:!1,capture:!0}),i.toggleDragModeOnDblclick&&j(e,ge,this.onDblclick),j(t.ownerDocument,ve,this.onCropMove),j(t.ownerDocument,be,this.onCropEnd),i.responsive&&j(window,we,this.onResize)}},Xi={resize:function(){if(!this.disabled){var t,i,e=this.options,n=this.container,a=this.containerData,r=n.offsetWidth/a.width,h=n.offsetHeight/a.height,s=Math.abs(r-1)>Math.abs(h-1)?r:h;s!==1&&(e.restore&&(t=this.getCanvasData(),i=this.getCropBoxData()),this.render(),e.restore&&(this.setCanvasData(E(t,function(l,o){t[o]=l*s})),this.setCropBoxData(E(i,function(l,o){i[o]=l*s}))))}},dblclick:function(){var t,i;this.disabled||this.options.dragMode===me||this.setDragMode((t=this.dragBox,i=At,(t.classList?t.classList.contains(i):t.className.indexOf(i)>-1)?ue:It))},wheel:function(t){var i=this,e=Number(this.options.wheelZoomRatio)||.1,n=1;this.disabled||(t.preventDefault(),this.wheeling||(this.wheeling=!0,setTimeout(function(){i.wheeling=!1},50),t.deltaY?n=t.deltaY>0?1:-1:t.wheelDelta?n=-t.wheelDelta/120:t.detail&&(n=t.detail>0?1:-1),this.zoom(-n*e,t)))},cropStart:function(t){var i=t.buttons,e=t.button;if(!(this.disabled||(t.type==="mousedown"||t.type==="pointerdown"&&t.pointerType==="mouse")&&(C(i)&&i!==1||C(e)&&e!==0||t.ctrlKey))){var n,a=this.options,r=this.pointers;t.changedTouches?E(t.changedTouches,function(h){r[h.identifier]=Tt(h)}):r[t.pointerId||0]=Tt(t),n=Object.keys(r).length>1&&a.zoomable&&a.zoomOnTouch?ce:Zt(t.target,xt),Di.test(n)&&dt(this.element,$t,{originalEvent:t,action:n})!==!1&&(t.preventDefault(),this.action=n,this.cropping=!1,n===he&&(this.cropping=!0,z(this.dragBox,Ot)))}},cropMove:function(t){var i=this.action;if(!this.disabled&&i){var e=this.pointers;t.preventDefault(),dt(this.element,Ut,{originalEvent:t,action:i})!==!1&&(t.changedTouches?E(t.changedTouches,function(n){_(e[n.identifier]||{},Tt(n,!0))}):_(e[t.pointerId||0]||{},Tt(t,!0)),this.change(t))}},cropEnd:function(t){if(!this.disabled){var i=this.action,e=this.pointers;t.changedTouches?E(t.changedTouches,function(n){delete e[n.identifier]}):delete e[t.pointerId||0],i&&(t.preventDefault(),Object.keys(e).length||(this.action=""),this.cropping&&(this.cropping=!1,ct(this.dragBox,Ot,this.cropped&&this.options.modal)),dt(this.element,Pt,{originalEvent:t,action:i}))}}},Ii={change:function(t){var i,e=this.options,n=this.canvasData,a=this.containerData,r=this.cropBoxData,h=this.pointers,s=this.action,l=e.aspectRatio,o=r.left,d=r.top,p=r.width,c=r.height,m=o+p,f=d+c,g=0,b=0,y=a.width,w=a.height,D=!0;!l&&t.shiftKey&&(l=p&&c?p/c:1),this.limited&&(g=r.minLeft,b=r.minTop,y=g+Math.min(a.width,n.width,n.left+n.width),w=b+Math.min(a.height,n.height,n.top+n.height));var M=h[Object.keys(h)[0]],u={x:M.endX-M.startX,y:M.endY-M.startY},x=function(k){switch(k){case tt:m+u.x>y&&(u.x=y-m);break;case et:o+u.x<g&&(u.x=g-o);break;case Z:d+u.y<b&&(u.y=b-d);break;case st:f+u.y>w&&(u.y=w-f)}};switch(s){case Yt:o+=u.x,d+=u.y;break;case tt:if(u.x>=0&&(m>=y||l&&(d<=b||f>=w))){D=!1;break}x(tt),(p+=u.x)<0&&(s=et,o-=p=-p),l&&(c=p/l,d+=(r.height-c)/2);break;case Z:if(u.y<=0&&(d<=b||l&&(o<=g||m>=y))){D=!1;break}x(Z),c-=u.y,d+=u.y,c<0&&(s=st,d-=c=-c),l&&(p=c*l,o+=(r.width-p)/2);break;case et:if(u.x<=0&&(o<=g||l&&(d<=b||f>=w))){D=!1;break}x(et),p-=u.x,o+=u.x,p<0&&(s=tt,o-=p=-p),l&&(c=p/l,d+=(r.height-c)/2);break;case st:if(u.y>=0&&(f>=w||l&&(o<=g||m>=y))){D=!1;break}x(st),(c+=u.y)<0&&(s=Z,d-=c=-c),l&&(p=c*l,o+=(r.width-p)/2);break;case vt:if(l){if(u.y<=0&&(d<=b||m>=y)){D=!1;break}x(Z),c-=u.y,d+=u.y,p=c*l}else x(Z),x(tt),u.x>=0?m<y?p+=u.x:u.y<=0&&d<=b&&(D=!1):p+=u.x,u.y<=0?d>b&&(c-=u.y,d+=u.y):(c-=u.y,d+=u.y);p<0&&c<0?(s=wt,d-=c=-c,o-=p=-p):p<0?(s=bt,o-=p=-p):c<0&&(s=yt,d-=c=-c);break;case bt:if(l){if(u.y<=0&&(d<=b||o<=g)){D=!1;break}x(Z),c-=u.y,d+=u.y,p=c*l,o+=r.width-p}else x(Z),x(et),u.x<=0?o>g?(p-=u.x,o+=u.x):u.y<=0&&d<=b&&(D=!1):(p-=u.x,o+=u.x),u.y<=0?d>b&&(c-=u.y,d+=u.y):(c-=u.y,d+=u.y);p<0&&c<0?(s=yt,d-=c=-c,o-=p=-p):p<0?(s=vt,o-=p=-p):c<0&&(s=wt,d-=c=-c);break;case wt:if(l){if(u.x<=0&&(o<=g||f>=w)){D=!1;break}x(et),p-=u.x,o+=u.x,c=p/l}else x(st),x(et),u.x<=0?o>g?(p-=u.x,o+=u.x):u.y>=0&&f>=w&&(D=!1):(p-=u.x,o+=u.x),u.y>=0?f<w&&(c+=u.y):c+=u.y;p<0&&c<0?(s=vt,d-=c=-c,o-=p=-p):p<0?(s=yt,o-=p=-p):c<0&&(s=bt,d-=c=-c);break;case yt:if(l){if(u.x>=0&&(m>=y||f>=w)){D=!1;break}x(tt),c=(p+=u.x)/l}else x(st),x(tt),u.x>=0?m<y?p+=u.x:u.y>=0&&f>=w&&(D=!1):p+=u.x,u.y>=0?f<w&&(c+=u.y):c+=u.y;p<0&&c<0?(s=bt,d-=c=-c,o-=p=-p):p<0?(s=wt,o-=p=-p):c<0&&(s=vt,d-=c=-c);break;case le:this.move(u.x,u.y),D=!1;break;case ce:this.zoom(function(k){var Y=ne({},k),L=0;return E(k,function(S,G){delete Y[G],E(Y,function(W){var T=Math.abs(S.startX-W.startX),Dt=Math.abs(S.startY-W.startY),at=Math.abs(S.endX-W.endX),pt=Math.abs(S.endY-W.endY),V=Math.sqrt(T*T+Dt*Dt),ut=(Math.sqrt(at*at+pt*pt)-V)/V;Math.abs(ut)>Math.abs(L)&&(L=ut)})}),L}(h),t),D=!1;break;case he:if(!u.x||!u.y){D=!1;break}i=_e(this.cropper),o=M.startX-i.left,d=M.startY-i.top,p=r.minWidth,c=r.minHeight,u.x>0?s=u.y>0?yt:vt:u.x<0&&(o-=p,s=u.y>0?wt:bt),u.y<0&&(d-=c),this.cropped||($(this.cropBox,R),this.cropped=!0,this.limited&&this.limitCropBox(!0,!0))}D&&(r.width=p,r.height=c,r.left=o,r.top=d,this.action=s,this.renderCropBox()),E(h,function(k){k.startX=k.endX,k.startY=k.endY})}},ji={crop:function(){return!this.ready||this.cropped||this.disabled||(this.cropped=!0,this.limitCropBox(!0,!0),this.options.modal&&z(this.dragBox,Ot),$(this.cropBox,R),this.setCropBoxData(this.initialCropBoxData)),this},reset:function(){return this.ready&&!this.disabled&&(this.imageData=_({},this.initialImageData),this.canvasData=_({},this.initialCanvasData),this.cropBoxData=_({},this.initialCropBoxData),this.renderCanvas(),this.cropped&&this.renderCropBox()),this},clear:function(){return this.cropped&&!this.disabled&&(_(this.cropBoxData,{left:0,top:0,width:0,height:0}),this.cropped=!1,this.renderCropBox(),this.limitCanvas(!0,!0),this.renderCanvas(),$(this.dragBox,Ot),z(this.cropBox,R)),this},replace:function(t){var i=arguments.length>1&&arguments[1]!==void 0&&arguments[1];return!this.disabled&&t&&(this.isImg&&(this.element.src=t),i?(this.url=t,this.image.src=t,this.ready&&(this.viewBoxImage.src=t,E(this.previews,function(e){e.getElementsByTagName("img")[0].src=t}))):(this.isImg&&(this.replaced=!0),this.options.data=null,this.uncreate(),this.load(t))),this},enable:function(){return this.ready&&this.disabled&&(this.disabled=!1,$(this.cropper,de)),this},disable:function(){return this.ready&&!this.disabled&&(this.disabled=!0,z(this.cropper,de)),this},destroy:function(){var t=this.element;return t[O]?(t[O]=void 0,this.isImg&&this.replaced&&(t.src=this.originalUrl),this.uncreate(),this):this},move:function(t){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:t,e=this.canvasData,n=e.left,a=e.top;return this.moveTo(Vt(t)?t:n+Number(t),Vt(i)?i:a+Number(i))},moveTo:function(t){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:t,e=this.canvasData,n=!1;return t=Number(t),i=Number(i),this.ready&&!this.disabled&&this.options.movable&&(C(t)&&(e.left=t,n=!0),C(i)&&(e.top=i,n=!0),n&&this.renderCanvas(!0)),this},zoom:function(t,i){var e=this.canvasData;return t=(t=Number(t))<0?1/(1-t):1+t,this.zoomTo(e.width*t/e.naturalWidth,null,i)},zoomTo:function(t,i,e){var n=this.options,a=this.canvasData,r=a.width,h=a.height,s=a.naturalWidth,l=a.naturalHeight;if((t=Number(t))>=0&&this.ready&&!this.disabled&&n.zoomable){var o=s*t,d=l*t;if(dt(this.element,qt,{ratio:t,oldRatio:r/s,originalEvent:e})===!1)return this;if(e){var p=this.pointers,c=_e(this.cropper),m=p&&Object.keys(p).length?function(f){var g=0,b=0,y=0;return E(f,function(w){var D=w.startX,M=w.startY;g+=D,b+=M,y+=1}),{pageX:g/=y,pageY:b/=y}}(p):{pageX:e.pageX,pageY:e.pageY};a.left-=(o-r)*((m.pageX-c.left-a.left)/r),a.top-=(d-h)*((m.pageY-c.top-a.top)/h)}else ht(i)&&C(i.x)&&C(i.y)?(a.left-=(o-r)*((i.x-a.left)/r),a.top-=(d-h)*((i.y-a.top)/h)):(a.left-=(o-r)/2,a.top-=(d-h)/2);a.width=o,a.height=d,this.renderCanvas(!0)}return this},rotate:function(t){return this.rotateTo((this.imageData.rotate||0)+Number(t))},rotateTo:function(t){return C(t=Number(t))&&this.ready&&!this.disabled&&this.options.rotatable&&(this.imageData.rotate=t%360,this.renderCanvas(!0,!0)),this},scaleX:function(t){var i=this.imageData.scaleY;return this.scale(t,C(i)?i:1)},scaleY:function(t){var i=this.imageData.scaleX;return this.scale(C(i)?i:1,t)},scale:function(t){var i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:t,e=this.imageData,n=!1;return t=Number(t),i=Number(i),this.ready&&!this.disabled&&this.options.scalable&&(C(t)&&(e.scaleX=t,n=!0),C(i)&&(e.scaleY=i,n=!0),n&&this.renderCanvas(!0,!0)),this},getData:function(){var t,i=arguments.length>0&&arguments[0]!==void 0&&arguments[0],e=this.options,n=this.imageData,a=this.canvasData,r=this.cropBoxData;if(this.ready&&this.cropped){t={x:r.left-a.left,y:r.top-a.top,width:r.width,height:r.height};var h=n.width/n.naturalWidth;if(E(t,function(o,d){t[d]=o/h}),i){var s=Math.round(t.y+t.height),l=Math.round(t.x+t.width);t.x=Math.round(t.x),t.y=Math.round(t.y),t.width=l-t.x,t.height=s-t.y}}else t={x:0,y:0,width:0,height:0};return e.rotatable&&(t.rotate=n.rotate||0),e.scalable&&(t.scaleX=n.scaleX||1,t.scaleY=n.scaleY||1),t},setData:function(t){var i=this.options,e=this.imageData,n=this.canvasData,a={};if(this.ready&&!this.disabled&&ht(t)){var r=!1;i.rotatable&&C(t.rotate)&&t.rotate!==e.rotate&&(e.rotate=t.rotate,r=!0),i.scalable&&(C(t.scaleX)&&t.scaleX!==e.scaleX&&(e.scaleX=t.scaleX,r=!0),C(t.scaleY)&&t.scaleY!==e.scaleY&&(e.scaleY=t.scaleY,r=!0)),r&&this.renderCanvas(!0,!0);var h=e.width/e.naturalWidth;C(t.x)&&(a.left=t.x*h+n.left),C(t.y)&&(a.top=t.y*h+n.top),C(t.width)&&(a.width=t.width*h),C(t.height)&&(a.height=t.height*h),this.setCropBoxData(a)}return this},getContainerData:function(){return this.ready?_({},this.containerData):{}},getImageData:function(){return this.sized?_({},this.imageData):{}},getCanvasData:function(){var t=this.canvasData,i={};return this.ready&&E(["left","top","width","height","naturalWidth","naturalHeight"],function(e){i[e]=t[e]}),i},setCanvasData:function(t){var i=this.canvasData,e=i.aspectRatio;return this.ready&&!this.disabled&&ht(t)&&(C(t.left)&&(i.left=t.left),C(t.top)&&(i.top=t.top),C(t.width)?(i.width=t.width,i.height=t.width/e):C(t.height)&&(i.height=t.height,i.width=t.height*e),this.renderCanvas(!0)),this},getCropBoxData:function(){var t,i=this.cropBoxData;return this.ready&&this.cropped&&(t={left:i.left,top:i.top,width:i.width,height:i.height}),t||{}},setCropBoxData:function(t){var i,e,n=this.cropBoxData,a=this.options.aspectRatio;return this.ready&&this.cropped&&!this.disabled&&ht(t)&&(C(t.left)&&(n.left=t.left),C(t.top)&&(n.top=t.top),C(t.width)&&t.width!==n.width&&(i=!0,n.width=t.width),C(t.height)&&t.height!==n.height&&(e=!0,n.height=t.height),a&&(i?n.height=n.width/a:e&&(n.width=n.height*a)),this.renderCropBox()),this},getCroppedCanvas:function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};if(!this.ready||!window.HTMLCanvasElement)return null;var i=this.canvasData,e=function(pt,V,ut,nt){var Ae=V.aspectRatio,Ui=V.naturalWidth,$i=V.naturalHeight,Xe=V.rotate,qi=Xe===void 0?0:Xe,Ie=V.scaleX,Vi=Ie===void 0?1:Ie,je=V.scaleY,Fi=je===void 0?1:je,Pe=ut.aspectRatio,Zi=ut.naturalWidth,Ki=ut.naturalHeight,Ue=nt.fillColor,Qi=Ue===void 0?"transparent":Ue,$e=nt.imageSmoothingEnabled,Gi=$e===void 0||$e,qe=nt.imageSmoothingQuality,Ji=qe===void 0?"low":qe,Ve=nt.maxWidth,Fe=Ve===void 0?1/0:Ve,Ze=nt.maxHeight,Ke=Ze===void 0?1/0:Ze,Qe=nt.minWidth,Ge=Qe===void 0?0:Qe,Je=nt.minHeight,ti=Je===void 0?0:Je,Et=document.createElement("canvas"),P=Et.getContext("2d"),ei=Q({aspectRatio:Pe,width:Fe,height:Ke}),ii=Q({aspectRatio:Pe,width:Ge,height:ti},"cover"),Qt=Math.min(ei.width,Math.max(ii.width,Zi)),Gt=Math.min(ei.height,Math.max(ii.height,Ki)),ai=Q({aspectRatio:Ae,width:Fe,height:Ke}),ni=Q({aspectRatio:Ae,width:Ge,height:ti},"cover"),ri=Math.min(ai.width,Math.max(ni.width,Ui)),oi=Math.min(ai.height,Math.max(ni.height,$i)),ta=[-ri/2,-oi/2,ri,oi];return Et.width=lt(Qt),Et.height=lt(Gt),P.fillStyle=Qi,P.fillRect(0,0,Qt,Gt),P.save(),P.translate(Qt/2,Gt/2),P.rotate(qi*Math.PI/180),P.scale(Vi,Fi),P.imageSmoothingEnabled=Gi,P.imageSmoothingQuality=Ji,P.drawImage.apply(P,[pt].concat(oe(ta.map(function(ea){return Math.floor(lt(ea))})))),P.restore(),Et}(this.image,this.imageData,i,t);if(!this.cropped)return e;var n=this.getData(t.rounded),a=n.x,r=n.y,h=n.width,s=n.height,l=e.width/Math.floor(i.naturalWidth);l!==1&&(a*=l,r*=l,h*=l,s*=l);var o=h/s,d=Q({aspectRatio:o,width:t.maxWidth||1/0,height:t.maxHeight||1/0}),p=Q({aspectRatio:o,width:t.minWidth||0,height:t.minHeight||0},"cover"),c=Q({aspectRatio:o,width:t.width||(l!==1?e.width:h),height:t.height||(l!==1?e.height:s)}),m=c.width,f=c.height;m=Math.min(d.width,Math.max(p.width,m)),f=Math.min(d.height,Math.max(p.height,f));var g=document.createElement("canvas"),b=g.getContext("2d");g.width=lt(m),g.height=lt(f),b.fillStyle=t.fillColor||"transparent",b.fillRect(0,0,m,f);var y=t.imageSmoothingEnabled,w=y===void 0||y,D=t.imageSmoothingQuality;b.imageSmoothingEnabled=w,D&&(b.imageSmoothingQuality=D);var M,u,x,k,Y,L,S=e.width,G=e.height,W=a,T=r;W<=-h||W>S?(W=0,M=0,x=0,Y=0):W<=0?(x=-W,W=0,Y=M=Math.min(S,h+W)):W<=S&&(x=0,Y=M=Math.min(h,S-W)),M<=0||T<=-s||T>G?(T=0,u=0,k=0,L=0):T<=0?(k=-T,T=0,L=u=Math.min(G,s+T)):T<=G&&(k=0,L=u=Math.min(s,G-T));var Dt=[W,T,M,u];if(Y>0&&L>0){var at=m/h;Dt.push(x*at,k*at,Y*at,L*at)}return b.drawImage.apply(b,[e].concat(oe(Dt.map(function(pt){return Math.floor(lt(pt))})))),g},setAspectRatio:function(t){var i=this.options;return this.disabled||Vt(t)||(i.aspectRatio=Math.max(0,t)||NaN,this.ready&&(this.initCropBox(),this.cropped&&this.renderCropBox())),this},setDragMode:function(t){var i=this.options,e=this.dragBox,n=this.face;if(this.ready&&!this.disabled){var a=t===It,r=i.movable&&t===ue;t=a||r?t:me,i.dragMode=t,Ct(e,xt,t),ct(e,At,a),ct(e,Xt,r),i.cropBoxMovable||(Ct(n,xt,t),ct(n,At,a),ct(n,Xt,r))}return this}},Pi=U.Cropper,Se=function(){function t(a){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(function(h,s){if(!(h instanceof s))throw new TypeError("Cannot call a class as a function")}(this,t),!a||!Oi.test(a.tagName))throw new Error("The first argument is required and must be an <img> or <canvas> element.");this.element=a,this.options=_({},Me,ht(r)&&r),this.cropped=!1,this.disabled=!1,this.pointers={},this.ready=!1,this.reloading=!1,this.replaced=!1,this.sized=!1,this.sizing=!1,this.init()}var i,e,n;return i=t,e=[{key:"init",value:function(){var a,r=this.element,h=r.tagName.toLowerCase();if(!r[O]){if(r[O]=this,h==="img"){if(this.isImg=!0,a=r.getAttribute("src")||"",this.originalUrl=a,!a)return;a=r.src}else h==="canvas"&&window.HTMLCanvasElement&&(a=r.toDataURL());this.load(a)}}},{key:"load",value:function(a){var r=this;if(a){this.url=a,this.imageData={};var h=this.element,s=this.options;if(s.rotatable||s.scalable||(s.checkOrientation=!1),s.checkOrientation&&window.ArrayBuffer)if(ki.test(a))Bi.test(a)?this.read((l=a.replace(Ri,""),o=atob(l),d=new ArrayBuffer(o.length),E(p=new Uint8Array(d),function(f,g){p[g]=o.charCodeAt(g)}),d)):this.clone();else{var l,o,d,p,c=new XMLHttpRequest,m=this.clone.bind(this);this.reloading=!0,this.xhr=c,c.onabort=m,c.onerror=m,c.ontimeout=m,c.onprogress=function(){c.getResponseHeader("content-type")!==Ce&&c.abort()},c.onload=function(){r.read(c.response)},c.onloadend=function(){r.reloading=!1,r.xhr=null},s.checkCrossOrigin&&Te(a)&&h.crossOrigin&&(a=Ee(a)),c.open("GET",a,!0),c.responseType="arraybuffer",c.withCredentials=h.crossOrigin==="use-credentials",c.send()}else this.clone()}}},{key:"read",value:function(a){var r=this.options,h=this.imageData,s=Ni(a),l=0,o=1,d=1;if(s>1){this.url=function(c,m){for(var f=[],g=new Uint8Array(c);g.length>0;)f.push(ze.apply(null,ke(g.subarray(0,8192)))),g=g.subarray(8192);return"data:".concat(m,";base64,").concat(btoa(f.join("")))}(a,Ce);var p=function(c){var m=0,f=1,g=1;switch(c){case 2:f=-1;break;case 3:m=-180;break;case 4:g=-1;break;case 5:m=90,g=-1;break;case 6:m=90;break;case 7:m=90,f=-1;break;case 8:m=-90}return{rotate:m,scaleX:f,scaleY:g}}(s);l=p.rotate,o=p.scaleX,d=p.scaleY}r.rotatable&&(h.rotate=l),r.scalable&&(h.scaleX=o,h.scaleY=d),this.clone()}},{key:"clone",value:function(){var a=this.element,r=this.url,h=a.crossOrigin,s=r;this.options.checkCrossOrigin&&Te(r)&&(h||(h="anonymous"),s=Ee(r)),this.crossOrigin=h,this.crossOriginUrl=s;var l=document.createElement("img");h&&(l.crossOrigin=h),l.src=s||r,l.alt=a.alt||"The image to crop",this.image=l,l.onload=this.start.bind(this),l.onerror=this.stop.bind(this),z(l,pe),a.parentNode.insertBefore(l,a.nextSibling)}},{key:"start",value:function(){var a=this,r=this.image;r.onload=null,r.onerror=null,this.sizing=!0;var h=U.navigator&&/(?:iPad|iPhone|iPod).*?AppleWebKit/i.test(U.navigator.userAgent),s=function(d,p){_(a.imageData,{naturalWidth:d,naturalHeight:p,aspectRatio:d/p}),a.initialImageData=_({},a.imageData),a.sizing=!1,a.sized=!0,a.build()};if(!r.naturalWidth||h){var l=document.createElement("img"),o=document.body||document.documentElement;this.sizingImage=l,l.onload=function(){s(l.width,l.height),h||o.removeChild(l)},l.src=r.src,h||(l.style.cssText="left:0;max-height:none!important;max-width:none!important;min-height:0!important;min-width:0!important;opacity:0;position:absolute;top:0;z-index:-1;",o.appendChild(l))}else s(r.naturalWidth,r.naturalHeight)}},{key:"stop",value:function(){var a=this.image;a.onload=null,a.onerror=null,a.parentNode.removeChild(a),this.image=null}},{key:"build",value:function(){if(this.sized&&!this.ready){var a=this.element,r=this.options,h=this.image,s=a.parentNode,l=document.createElement("div");l.innerHTML='<div class="cropper-container" touch-action="none"><div class="cropper-wrap-box"><div class="cropper-canvas"></div></div><div class="cropper-drag-box"></div><div class="cropper-crop-box"><span class="cropper-view-box"></span><span class="cropper-dashed dashed-h"></span><span class="cropper-dashed dashed-v"></span><span class="cropper-center"></span><span class="cropper-face"></span><span class="cropper-line line-e" data-cropper-action="e"></span><span class="cropper-line line-n" data-cropper-action="n"></span><span class="cropper-line line-w" data-cropper-action="w"></span><span class="cropper-line line-s" data-cropper-action="s"></span><span class="cropper-point point-e" data-cropper-action="e"></span><span class="cropper-point point-n" data-cropper-action="n"></span><span class="cropper-point point-w" data-cropper-action="w"></span><span class="cropper-point point-s" data-cropper-action="s"></span><span class="cropper-point point-ne" data-cropper-action="ne"></span><span class="cropper-point point-nw" data-cropper-action="nw"></span><span class="cropper-point point-sw" data-cropper-action="sw"></span><span class="cropper-point point-se" data-cropper-action="se"></span></div></div>';var o=l.querySelector(".".concat(O,"-container")),d=o.querySelector(".".concat(O,"-canvas")),p=o.querySelector(".".concat(O,"-drag-box")),c=o.querySelector(".".concat(O,"-crop-box")),m=c.querySelector(".".concat(O,"-face"));this.container=s,this.cropper=o,this.canvas=d,this.dragBox=p,this.cropBox=c,this.viewBox=o.querySelector(".".concat(O,"-view-box")),this.face=m,d.appendChild(h),z(a,R),s.insertBefore(o,a.nextSibling),$(h,pe),this.initPreview(),this.bind(),r.initialAspectRatio=Math.max(0,r.initialAspectRatio)||NaN,r.aspectRatio=Math.max(0,r.aspectRatio)||NaN,r.viewMode=Math.max(0,Math.min(3,Math.round(r.viewMode)))||0,z(c,R),r.guides||z(c.getElementsByClassName("".concat(O,"-dashed")),R),r.center||z(c.getElementsByClassName("".concat(O,"-center")),R),r.background&&z(o,"".concat(O,"-bg")),r.highlight||z(m,Mi),r.cropBoxMovable&&(z(m,Xt),Ct(m,xt,Yt)),r.cropBoxResizable||(z(c.getElementsByClassName("".concat(O,"-line")),R),z(c.getElementsByClassName("".concat(O,"-point")),R)),this.render(),this.ready=!0,this.setDragMode(r.dragMode),r.autoCrop&&this.crop(),this.setData(r.data),N(r.ready)&&A(a,ye,r.ready,{once:!0}),dt(a,ye)}}},{key:"unbuild",value:function(){if(this.ready){this.ready=!1,this.unbind(),this.resetPreview();var a=this.cropper.parentNode;a&&a.removeChild(this.cropper),$(this.element,R)}}},{key:"uncreate",value:function(){this.ready?(this.unbuild(),this.ready=!1,this.cropped=!1):this.sizing?(this.sizingImage.onload=null,this.sizing=!1,this.sized=!1):this.reloading?(this.xhr.onabort=null,this.xhr.abort()):this.image&&this.stop()}}],n=[{key:"noConflict",value:function(){return window.Cropper=Pi,t}},{key:"setDefaults",value:function(a){_(Me,ht(a)&&a)}}],e&&re(i.prototype,e),n&&re(i,n),Object.defineProperty(i,"prototype",{writable:!1}),t}();_(Se.prototype,Hi,Yi,Ai,Xi,Ii,ji);let We,Le,Re,Ne,He,Ye;We=["alt","crossorigin","src"],Le=mt({name:"Cropper",__name:"Cropper",props:{src:I.string.def(""),alt:I.string.def(""),circled:I.bool.def(!1),realTimePreview:I.bool.def(!0),height:I.string.def("360px"),crossorigin:{type:String,default:void 0},imageStyle:{type:Object,default:()=>({})},options:{type:Object,default:()=>({})}},emits:["cropend","ready","cropendError"],setup(t,{emit:i}){const e={aspectRatio:1,zoomable:!0,zoomOnTouch:!0,zoomOnWheel:!0,cropBoxMovable:!0,cropBoxResizable:!0,toggleDragModeOnDblclick:!0,autoCrop:!0,background:!0,highlight:!0,center:!0,responsive:!0,restore:!0,checkCrossOrigin:!0,checkOrientation:!0,scalable:!0,modal:!0,guides:!0,movable:!0,rotatable:!0},n=t,a=i,r=sa(),h=X(),s=X(),l=X(!1),{getPrefixCls:o}=te(),d=o("cropper-image"),p=ha(g,80),c=rt(()=>({height:n.height,maxWidth:"100%",...n.imageStyle})),m=rt(()=>[d,r.class,{[`${d}--circled`]:n.circled}]),f=rt(()=>({height:`${n.height}`.replace(/px/,"")+"px"}));function g(){n.realTimePreview&&function(){if(!s.value)return;let b=s.value.getData();(n.circled?function(){const y=s.value.getCroppedCanvas(),w=document.createElement("canvas"),D=w.getContext("2d"),M=y.width,u=y.height;return w.width=M,w.height=u,D.imageSmoothingEnabled=!0,D.drawImage(y,0,0,M,u),D.globalCompositeOperation="destination-in",D.beginPath(),D.arc(M/2,u/2,Math.min(M,u)/2,0,2*Math.PI,!0),D.fill(),w}():s.value.getCroppedCanvas()).toBlob(y=>{if(!y)return;let w=new FileReader;w.readAsDataURL(y),w.onloadend=D=>{var M;a("cropend",{imgBase64:((M=D.target)==null?void 0:M.result)??"",imgInfo:b})},w.onerror=()=>{a("cropendError")}},"image/png")}()}return la(async function(){const b=v(h);b&&(s.value=new Se(b,{...e,ready:()=>{l.value=!0,g(),a("ready",s.value)},crop(){p()},zoom(){p()},cropmove(){p()},...n.options}))}),ca(()=>{var b;(b=s.value)==null||b.destroy()}),(b,y)=>(q(),gt("div",{class:F(v(m)),style:ui(v(f))},[da(ot("img",{ref_key:"imgElRef",ref:h,alt:t.alt,crossorigin:t.crossorigin,src:t.src,style:ui(v(c))},null,12,We),[[pa,v(l)]])],6))}}),Re=["alt","src"],Ne=mt({name:"CopperModal",__name:"CopperModal",props:{srcValue:I.string.def(""),circled:I.bool.def(!0)},emits:["uploadSuccess"],setup(t,{expose:i,emit:e}){const n=t,a=e,{t:r}=mi.useI18n(),{getPrefixCls:h}=te(),s=h("cropper-am"),l=X(n.srcValue),o=X(""),d=X(),p=X(!1);let c="",m=1,f=1;function g(M){const u=new FileReader;return u.readAsDataURL(M),l.value="",o.value="",u.onload=function(x){var k;l.value=((k=x.target)==null?void 0:k.result)??"",c=M.name},!1}function b({imgBase64:M}){o.value=M}function y(M){d.value=M}function w(M,u){var x,k;M==="scaleX"&&(m=u=m===-1?1:-1),M==="scaleY"&&(f=u=f===-1?1:-1),(k=(x=d==null?void 0:d.value)==null?void 0:x[M])==null||k.call(x,u)}async function D(){const M=(u=>{const x=u.split(","),k=x[0].match(/:(.*?);/)[1],Y=window.atob(x[1]);let L=Y.length;const S=new Uint8Array(L);for(;L--;)S[L]=Y.charCodeAt(L);return new Blob([S],{type:k})})(o.value);a("uploadSuccess",{source:o.value,data:M,filename:c})}return i({openModal:function(){p.value=!0},closeModal:function(){p.value=!1}}),(M,u)=>{const x=Ma,k=ma,Y=ga,L=xi,S=vi,G=fi,W=xa;return q(),gt("div",null,[B(W,{modelValue:v(p),"onUpdate:modelValue":u[7]||(u[7]=T=>ua(p)?p.value=T:null),canFullscreen:!1,title:v(r)("cropper.modalTitle"),maxHeight:"380px",width:"800px"},{footer:H(()=>[B(G,{type:"primary",onClick:D},{default:H(()=>[Jt(gi(v(r)("cropper.okText")),1)]),_:1})]),default:H(()=>[ot("div",{class:F(v(s))},[ot("div",{class:F(`${v(s)}-left`)},[ot("div",{class:F(`${v(s)}-cropper`)},[v(l)?(q(),Wt(v(Le),{key:0,circled:t.circled,src:v(l),height:"300px",onCropend:b,onReady:y},null,8,["circled","src"])):ft("",!0)],2),ot("div",{class:F(`${v(s)}-toolbar`)},[B(Y,{beforeUpload:g,fileList:[],accept:"image/*"},{default:H(()=>[B(k,{content:v(r)("cropper.selectImage"),placement:"bottom"},{default:H(()=>[B(x,{preIcon:"ant-design:upload-outlined",type:"primary"})]),_:1},8,["content"])]),_:1}),B(L,null,{default:H(()=>[B(k,{content:v(r)("cropper.btn_reset"),placement:"bottom"},{default:H(()=>[B(x,{disabled:!v(l),preIcon:"ant-design:reload-outlined",size:"small",type:"primary",onClick:u[0]||(u[0]=T=>w("reset"))},null,8,["disabled"])]),_:1},8,["content"]),B(k,{content:v(r)("cropper.btn_rotate_left"),placement:"bottom"},{default:H(()=>[B(x,{disabled:!v(l),preIcon:"ant-design:rotate-left-outlined",size:"small",type:"primary",onClick:u[1]||(u[1]=T=>w("rotate",-45))},null,8,["disabled"])]),_:1},8,["content"]),B(k,{content:v(r)("cropper.btn_rotate_right"),placement:"bottom"},{default:H(()=>[B(x,{disabled:!v(l),preIcon:"ant-design:rotate-right-outlined",size:"small",type:"primary",onClick:u[2]||(u[2]=T=>w("rotate",45))},null,8,["disabled"])]),_:1},8,["content"]),B(k,{content:v(r)("cropper.btn_scale_x"),placement:"bottom"},{default:H(()=>[B(x,{disabled:!v(l),preIcon:"vaadin:arrows-long-h",size:"small",type:"primary",onClick:u[3]||(u[3]=T=>w("scaleX"))},null,8,["disabled"])]),_:1},8,["content"]),B(k,{content:v(r)("cropper.btn_scale_y"),placement:"bottom"},{default:H(()=>[B(x,{disabled:!v(l),preIcon:"vaadin:arrows-long-v",size:"small",type:"primary",onClick:u[4]||(u[4]=T=>w("scaleY"))},null,8,["disabled"])]),_:1},8,["content"]),B(k,{content:v(r)("cropper.btn_zoom_in"),placement:"bottom"},{default:H(()=>[B(x,{disabled:!v(l),preIcon:"ant-design:zoom-in-outlined",size:"small",type:"primary",onClick:u[5]||(u[5]=T=>w("zoom",.1))},null,8,["disabled"])]),_:1},8,["content"]),B(k,{content:v(r)("cropper.btn_zoom_out"),placement:"bottom"},{default:H(()=>[B(x,{disabled:!v(l),preIcon:"ant-design:zoom-out-outlined",size:"small",type:"primary",onClick:u[6]||(u[6]=T=>w("zoom",-.1))},null,8,["disabled"])]),_:1},8,["content"])]),_:1})],2)],2),ot("div",{class:F(`${v(s)}-right`)},[ot("div",{class:F(`${v(s)}-preview`)},[v(o)?(q(),gt("img",{key:0,alt:v(r)("cropper.preview"),src:v(o)},null,8,Re)):ft("",!0)],2),v(o)?(q(),gt("div",{key:0,class:F(`${v(s)}-group`)},[B(S,{src:v(o),size:"large"},null,8,["src"]),B(S,{size:48,src:v(o)},null,8,["src"]),B(S,{size:64,src:v(o)},null,8,["src"]),B(S,{size:80,src:v(o)},null,8,["src"])],2)):ft("",!0)],2)],2)]),_:1},8,["modelValue","title"])])}}}),He=bi(mt({name:"CropperAvatar",__name:"CropperAvatar",props:{width:I.string.def("200px"),value:I.string.def(""),showBtn:I.bool.def(!0),btnText:I.string.def("")},emits:["update:value","change"],setup(t,{expose:i,emit:e}){const n=t,a=e,r=X(n.value),{getPrefixCls:h}=te(),s=h("cropper-avatar"),l=ka(),{t:o}=mi.useI18n(),d=X();function p({source:m,data:f,filename:g}){r.value=m,a("change",{source:m,data:f,filename:g}),l.success(o("cropper.uploadSuccess"))}function c(){d.value.openModal()}return ci(()=>{r.value=n.value}),fa(()=>r.value,m=>{a("update:value",m)}),i({open:c,close:function(){d.value.closeModal()}}),(m,f)=>{const g=vi,b=fi;return q(),gt("div",{class:"user-info-head",onClick:f[1]||(f[1]=y=>c())},[v(r)?(q(),Wt(g,{key:0,src:v(r),alt:"avatar",class:"img-circle img-lg"},null,8,["src"])):ft("",!0),v(r)?ft("",!0):(q(),Wt(g,{key:1,src:v("/admin-ui-vue3/assets/avatar-3e7710d1.jpg"),alt:"avatar",class:"img-circle img-lg"},null,8,["src"])),t.showBtn?(q(),Wt(b,{key:2,class:F(`${v(s)}-upload-btn`),onClick:f[0]||(f[0]=y=>c())},{default:H(()=>[Jt(gi(t.btnText?t.btnText:v(o)("cropper.selectImage")),1)]),_:1},8,["class"])):ft("",!0),B(Ne,{ref_key:"cropperModelRef",ref:d,srcValue:v(r),onUploadSuccess:p},null,8,["srcValue"])])}}}),[["__scopeId","data-v-45525b4a"]]),Ye={class:"change-avatar"},yi=bi(mt({name:"UserAvatar",__name:"UserAvatar",props:{img:I.string.def("")},setup(t){const i=X(),e=async({data:n})=>{await ba({avatarFile:n}),i.value.close()};return(n,a)=>(q(),gt("div",Ye,[B(v(He),{ref_key:"cropperRef",ref:i,btnProps:{preIcon:"ant-design:cloud-upload-outlined"},showBtn:!1,value:t.img,width:"120px",onChange:e},null,8,["value"])]))}}),[["__scopeId","data-v-417e758e"]])});export{Oa as __tla,yi as default};
