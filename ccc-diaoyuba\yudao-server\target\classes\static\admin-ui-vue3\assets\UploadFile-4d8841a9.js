import{d as g,r as b,f as v,o as x,q as I,w as r,g as U,aP as H,i as j,j as q,a as t,H as w,b9 as P,__tla as S}from"./index-97fffa0c.js";import{b as E,a as F,U as $,H as k,__tla as z}from"./upload-5ef645a3.js";import{u as A,__tla as B}from"./useMessage-18385d4a.js";import{U as i,__tla as C}from"./useUpload-36312237.js";import{_ as D}from"./_plugin-vue_export-helper-1b428a4d.js";let _,G=Promise.all([(()=>{try{return S}catch{}})(),(()=>{try{return z}catch{}})(),(()=>{try{return B}catch{}})(),(()=>{try{return C}catch{}})()]).then(async()=>{let l;l={class:"el-upload__tip",style:{"margin-left":"5px"}},_=D(g({__name:"UploadFile",props:{type:{}},emits:["uploaded"],setup(p,{emit:c}){const e=A(),n=p,o=b([]),u=c,s=v({type:i.Image,title:"",introduction:""}),d=n.type===i.Image?E:F,m=a=>{if(a.code!==0)return e.alertError("\u4E0A\u4F20\u51FA\u9519\uFF1A"+a.msg),!1;o.value=[],s.title="",s.introduction="",e.notifySuccess("\u4E0A\u4F20\u6210\u529F"),u("uploaded")},f=a=>e.error("\u4E0A\u4F20\u5931\u8D25: "+a.message);return(a,J)=>{const y=w,h=P;return x(),I(h,{action:t($),headers:t(k),multiple:"",limit:1,"file-list":t(o),data:t(s),"on-error":f,"before-upload":t(d),"on-success":m},{tip:r(()=>[U("span",l,[H(a.$slots,"default",{},void 0,!0)])]),default:r(()=>[j(y,{type:"primary",plain:""},{default:r(()=>[q(" \u70B9\u51FB\u4E0A\u4F20 ")]),_:1})]),_:3},8,["action","headers","file-list","data","before-upload"])}}}),[["__scopeId","data-v-7295c27c"]])});export{G as __tla,_ as default};
