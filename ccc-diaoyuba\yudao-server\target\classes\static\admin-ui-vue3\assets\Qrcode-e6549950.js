import{d as Me,p as rt,r as Ht,b as Kt,ax as Pe,B as Ne,o as mt,c as Jt,q as Re,aG as Le,V as Yt,a as z,g as Ot,i as xe,t as Se,a3 as Ue,au as ke,Z as _e,L as ze,_ as De,al as Fe,ar as He,aK as jt,__tla as Ke}from"./index-97fffa0c.js";import{_ as Je}from"./_plugin-vue_export-helper-1b428a4d.js";let Qt,Ye=Promise.all([(()=>{try{return Ke}catch{}})()]).then(async()=>{var Y={},yt={},R={};let ot;const Vt=[0,26,44,70,100,134,172,196,242,292,346,404,466,532,581,655,733,815,901,991,1085,1156,1258,1364,1474,1588,1706,1828,1921,2051,2185,2323,2465,2611,2761,2876,3034,3196,3362,3532,3706];R.getSymbolSize=function(t){if(!t)throw new Error('"version" cannot be null or undefined');if(t<1||t>40)throw new Error('"version" should be in range from 1 to 40');return 4*t+17},R.getSymbolTotalCodewords=function(t){return Vt[t]},R.getBCHDigit=function(t){let e=0;for(;t!==0;)e++,t>>>=1;return e},R.setToSJISFunction=function(t){if(typeof t!="function")throw new Error('"toSJISFunc" is not a valid function.');ot=t},R.isKanjiModeEnabled=function(){return ot!==void 0},R.toSJIS=function(t){return ot(t)};var S,q={};function Et(){this.buffer=[],this.length=0}(S=q).L={bit:1},S.M={bit:0},S.Q={bit:3},S.H={bit:2},S.isValid=function(t){return t&&t.bit!==void 0&&t.bit>=0&&t.bit<4},S.from=function(t,e){if(S.isValid(t))return t;try{return function(r){if(typeof r!="string")throw new Error("Param is not a string");switch(r.toLowerCase()){case"l":case"low":return S.L;case"m":case"medium":return S.M;case"q":case"quartile":return S.Q;case"h":case"high":return S.H;default:throw new Error("Unknown EC Level: "+r)}}(t)}catch{return e}},Et.prototype={get:function(t){const e=Math.floor(t/8);return(this.buffer[e]>>>7-t%8&1)==1},put:function(t,e){for(let r=0;r<e;r++)this.putBit((t>>>e-r-1&1)==1)},getLengthInBits:function(){return this.length},putBit:function(t){const e=Math.floor(this.length/8);this.buffer.length<=e&&this.buffer.push(0),t&&(this.buffer[e]|=128>>>this.length%8),this.length++}};var qt=Et;function O(t){if(!t||t<1)throw new Error("BitMatrix size must be defined and greater than 0");this.size=t,this.data=new Uint8Array(t*t),this.reservedBit=new Uint8Array(t*t)}O.prototype.set=function(t,e,r,n){const o=t*this.size+e;this.data[o]=r,n&&(this.reservedBit[o]=!0)},O.prototype.get=function(t,e){return this.data[t*this.size+e]},O.prototype.xor=function(t,e,r){this.data[t*this.size+e]^=r},O.prototype.isReserved=function(t,e){return this.reservedBit[t*this.size+e]};var $t=O,vt={};(function(t){const e=R.getSymbolSize;t.getRowColCoords=function(r){if(r===1)return[];const n=Math.floor(r/7)+2,o=e(r),a=o===145?26:2*Math.ceil((o-13)/(2*n-2)),i=[o-7];for(let l=1;l<n-1;l++)i[l]=i[l-1]-a;return i.push(6),i.reverse()},t.getPositions=function(r){const n=[],o=t.getRowColCoords(r),a=o.length;for(let i=0;i<a;i++)for(let l=0;l<a;l++)i===0&&l===0||i===0&&l===a-1||i===a-1&&l===0||n.push([o[i],o[l]]);return n}})(vt);var Ct={};const Zt=R.getSymbolSize;Ct.getPositions=function(t){const e=Zt(t);return[[0,0],[e-7,0],[0,e-7]]};var At={};(function(t){t.Patterns={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7};const e=3,r=3,n=40,o=10;function a(i,l,f){switch(i){case t.Patterns.PATTERN000:return(l+f)%2==0;case t.Patterns.PATTERN001:return l%2==0;case t.Patterns.PATTERN010:return f%3==0;case t.Patterns.PATTERN011:return(l+f)%3==0;case t.Patterns.PATTERN100:return(Math.floor(l/2)+Math.floor(f/3))%2==0;case t.Patterns.PATTERN101:return l*f%2+l*f%3==0;case t.Patterns.PATTERN110:return(l*f%2+l*f%3)%2==0;case t.Patterns.PATTERN111:return(l*f%3+(l+f)%2)%2==0;default:throw new Error("bad maskPattern:"+i)}}t.isValid=function(i){return i!=null&&i!==""&&!isNaN(i)&&i>=0&&i<=7},t.from=function(i){return t.isValid(i)?parseInt(i,10):void 0},t.getPenaltyN1=function(i){const l=i.size;let f=0,s=0,u=0,c=null,h=null;for(let w=0;w<l;w++){s=u=0,c=h=null;for(let d=0;d<l;d++){let p=i.get(w,d);p===c?s++:(s>=5&&(f+=e+(s-5)),c=p,s=1),p=i.get(d,w),p===h?u++:(u>=5&&(f+=e+(u-5)),h=p,u=1)}s>=5&&(f+=e+(s-5)),u>=5&&(f+=e+(u-5))}return f},t.getPenaltyN2=function(i){const l=i.size;let f=0;for(let s=0;s<l-1;s++)for(let u=0;u<l-1;u++){const c=i.get(s,u)+i.get(s,u+1)+i.get(s+1,u)+i.get(s+1,u+1);c!==4&&c!==0||f++}return f*r},t.getPenaltyN3=function(i){const l=i.size;let f=0,s=0,u=0;for(let c=0;c<l;c++){s=u=0;for(let h=0;h<l;h++)s=s<<1&2047|i.get(c,h),h>=10&&(s===1488||s===93)&&f++,u=u<<1&2047|i.get(h,c),h>=10&&(u===1488||u===93)&&f++}return f*n},t.getPenaltyN4=function(i){let l=0;const f=i.data.length;for(let s=0;s<f;s++)l+=i.data[s];return Math.abs(Math.ceil(100*l/f/5)-10)*o},t.applyMask=function(i,l){const f=l.size;for(let s=0;s<f;s++)for(let u=0;u<f;u++)l.isReserved(u,s)||l.xor(u,s,a(i,u,s))},t.getBestMask=function(i,l){const f=Object.keys(t.Patterns).length;let s=0,u=1/0;for(let c=0;c<f;c++){l(c),t.applyMask(c,i);const h=t.getPenaltyN1(i)+t.getPenaltyN2(i)+t.getPenaltyN3(i)+t.getPenaltyN4(i);t.applyMask(c,i),h<u&&(u=h,s=c)}return s}})(At);var $={};const k=q,Z=[1,1,1,1,1,1,1,1,1,1,2,2,1,2,2,4,1,2,4,4,2,4,4,4,2,4,6,5,2,4,6,6,2,5,8,8,4,5,8,8,4,5,8,11,4,8,10,11,4,9,12,16,4,9,16,16,6,10,12,18,6,10,17,16,6,11,16,19,6,13,18,21,7,14,21,25,8,16,20,25,8,17,23,25,9,17,23,34,9,18,25,30,10,20,27,32,12,21,29,35,12,23,34,37,12,25,34,40,13,26,35,42,14,28,38,45,15,29,40,48,16,31,43,51,17,33,45,54,18,35,48,57,19,37,51,60,19,38,53,63,20,40,56,66,21,43,59,70,22,45,62,74,24,47,65,77,25,49,68,81],X=[7,10,13,17,10,16,22,28,15,26,36,44,20,36,52,64,26,48,72,88,36,64,96,112,40,72,108,130,48,88,132,156,60,110,160,192,72,130,192,224,80,150,224,264,96,176,260,308,104,198,288,352,120,216,320,384,132,240,360,432,144,280,408,480,168,308,448,532,180,338,504,588,196,364,546,650,224,416,600,700,224,442,644,750,252,476,690,816,270,504,750,900,300,560,810,960,312,588,870,1050,336,644,952,1110,360,700,1020,1200,390,728,1050,1260,420,784,1140,1350,450,812,1200,1440,480,868,1290,1530,510,924,1350,1620,540,980,1440,1710,570,1036,1530,1800,570,1064,1590,1890,600,1120,1680,1980,630,1204,1770,2100,660,1260,1860,2220,720,1316,1950,2310,750,1372,2040,2430];$.getBlocksCount=function(t,e){switch(e){case k.L:return Z[4*(t-1)+0];case k.M:return Z[4*(t-1)+1];case k.Q:return Z[4*(t-1)+2];case k.H:return Z[4*(t-1)+3];default:return}},$.getTotalCodewordsCount=function(t,e){switch(e){case k.L:return X[4*(t-1)+0];case k.M:return X[4*(t-1)+1];case k.Q:return X[4*(t-1)+2];case k.H:return X[4*(t-1)+3];default:return}};var bt={},W={};const j=new Uint8Array(512),G=new Uint8Array(256);(function(){let t=1;for(let e=0;e<255;e++)j[e]=t,G[t]=e,t<<=1,256&t&&(t^=285);for(let e=255;e<512;e++)j[e]=j[e-255]})(),W.log=function(t){if(t<1)throw new Error("log("+t+")");return G[t]},W.exp=function(t){return j[t]},W.mul=function(t,e){return t===0||e===0?0:j[G[t]+G[e]]},function(t){const e=W;t.mul=function(r,n){const o=new Uint8Array(r.length+n.length-1);for(let a=0;a<r.length;a++)for(let i=0;i<n.length;i++)o[a+i]^=e.mul(r[a],n[i]);return o},t.mod=function(r,n){let o=new Uint8Array(r);for(;o.length-n.length>=0;){const a=o[0];for(let l=0;l<n.length;l++)o[l]^=e.mul(n[l],a);let i=0;for(;i<o.length&&o[i]===0;)i++;o=o.slice(i)}return o},t.generateECPolynomial=function(r){let n=new Uint8Array([1]);for(let o=0;o<r;o++)n=t.mul(n,new Uint8Array([1,e.exp(o)]));return n}}(bt);const Bt=bt;function it(t){this.genPoly=void 0,this.degree=t,this.degree&&this.initialize(this.degree)}it.prototype.initialize=function(t){this.degree=t,this.genPoly=Bt.generateECPolynomial(this.degree)},it.prototype.encode=function(t){if(!this.genPoly)throw new Error("Encoder not initialized");const e=new Uint8Array(t.length+this.degree);e.set(t);const r=Bt.mod(e,this.genPoly),n=this.degree-r.length;if(n>0){const o=new Uint8Array(this.degree);return o.set(r,n),o}return r};var Xt=it,It={},_={},Tt={isValid:function(t){return!isNaN(t)&&t>=1&&t<=40}},U={};const Mt="[0-9]+";let Q="(?:[u3000-u303F]|[u3040-u309F]|[u30A0-u30FF]|[uFF00-uFFEF]|[u4E00-u9FAF]|[u2605-u2606]|[u2190-u2195]|u203B|[u2010u2015u2018u2019u2025u2026u201Cu201Du2225u2260]|[u0391-u0451]|[u00A7u00A8u00B1u00B4u00D7u00F7])+";Q=Q.replace(/u/g,"\\u");const Wt="(?:(?![A-Z0-9 $%*+\\-./:]|"+Q+`)(?:.|[\r
]))+`;U.KANJI=new RegExp(Q,"g"),U.BYTE_KANJI=new RegExp("[^A-Z0-9 $%*+\\-./:]+","g"),U.BYTE=new RegExp(Wt,"g"),U.NUMERIC=new RegExp(Mt,"g"),U.ALPHANUMERIC=new RegExp("[A-Z $%*+\\-./:]+","g");const Gt=new RegExp("^"+Q+"$"),te=new RegExp("^"+Mt+"$"),ee=new RegExp("^[A-Z0-9 $%*+\\-./:]+$");U.testKanji=function(t){return Gt.test(t)},U.testNumeric=function(t){return te.test(t)},U.testAlphanumeric=function(t){return ee.test(t)},function(t){const e=Tt,r=U;t.NUMERIC={id:"Numeric",bit:1,ccBits:[10,12,14]},t.ALPHANUMERIC={id:"Alphanumeric",bit:2,ccBits:[9,11,13]},t.BYTE={id:"Byte",bit:4,ccBits:[8,16,16]},t.KANJI={id:"Kanji",bit:8,ccBits:[8,10,12]},t.MIXED={bit:-1},t.getCharCountIndicator=function(n,o){if(!n.ccBits)throw new Error("Invalid mode: "+n);if(!e.isValid(o))throw new Error("Invalid version: "+o);return o>=1&&o<10?n.ccBits[0]:o<27?n.ccBits[1]:n.ccBits[2]},t.getBestModeForData=function(n){return r.testNumeric(n)?t.NUMERIC:r.testAlphanumeric(n)?t.ALPHANUMERIC:r.testKanji(n)?t.KANJI:t.BYTE},t.toString=function(n){if(n&&n.id)return n.id;throw new Error("Invalid mode")},t.isValid=function(n){return n&&n.bit&&n.ccBits},t.from=function(n,o){if(t.isValid(n))return n;try{return function(a){if(typeof a!="string")throw new Error("Param is not a string");switch(a.toLowerCase()){case"numeric":return t.NUMERIC;case"alphanumeric":return t.ALPHANUMERIC;case"kanji":return t.KANJI;case"byte":return t.BYTE;default:throw new Error("Unknown mode: "+a)}}(n)}catch{return o}}}(_),function(t){const e=R,r=$,n=q,o=_,a=Tt,i=e.getBCHDigit(7973);function l(s,u){return o.getCharCountIndicator(s,u)+4}function f(s,u){let c=0;return s.forEach(function(h){const w=l(h.mode,u);c+=w+h.getBitsLength()}),c}t.from=function(s,u){return a.isValid(s)?parseInt(s,10):u},t.getCapacity=function(s,u,c){if(!a.isValid(s))throw new Error("Invalid QR Code version");c===void 0&&(c=o.BYTE);const h=8*(e.getSymbolTotalCodewords(s)-r.getTotalCodewordsCount(s,u));if(c===o.MIXED)return h;const w=h-l(c,s);switch(c){case o.NUMERIC:return Math.floor(w/10*3);case o.ALPHANUMERIC:return Math.floor(w/11*2);case o.KANJI:return Math.floor(w/13);case o.BYTE:default:return Math.floor(w/8)}},t.getBestVersionForData=function(s,u){let c;const h=n.from(u,n.M);if(Array.isArray(s)){if(s.length>1)return function(w,d){for(let p=1;p<=40;p++)if(f(w,p)<=t.getCapacity(p,d,o.MIXED))return p}(s,h);if(s.length===0)return 1;c=s[0]}else c=s;return function(w,d,p){for(let g=1;g<=40;g++)if(d<=t.getCapacity(g,p,w))return g}(c.mode,c.getLength(),h)},t.getEncodedBits=function(s){if(!a.isValid(s)||s<7)throw new Error("Invalid QR Code version");let u=s<<12;for(;e.getBCHDigit(u)-i>=0;)u^=7973<<e.getBCHDigit(u)-i;return s<<12|u}}(It);var Pt={};const st=R,Nt=st.getBCHDigit(1335);Pt.getEncodedBits=function(t,e){const r=t.bit<<3|e;let n=r<<10;for(;st.getBCHDigit(n)-Nt>=0;)n^=1335<<st.getBCHDigit(n)-Nt;return 21522^(r<<10|n)};var Rt={};const ne=_;function D(t){this.mode=ne.NUMERIC,this.data=t.toString()}D.getBitsLength=function(t){return 10*Math.floor(t/3)+(t%3?t%3*3+1:0)},D.prototype.getLength=function(){return this.data.length},D.prototype.getBitsLength=function(){return D.getBitsLength(this.data.length)},D.prototype.write=function(t){let e,r,n;for(e=0;e+3<=this.data.length;e+=3)r=this.data.substr(e,3),n=parseInt(r,10),t.put(n,10);const o=this.data.length-e;o>0&&(r=this.data.substr(e),n=parseInt(r,10),t.put(n,3*o+1))};var re=D;const oe=_,at=["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"," ","$","%","*","+","-",".","/",":"];function F(t){this.mode=oe.ALPHANUMERIC,this.data=t}F.getBitsLength=function(t){return 11*Math.floor(t/2)+t%2*6},F.prototype.getLength=function(){return this.data.length},F.prototype.getBitsLength=function(){return F.getBitsLength(this.data.length)},F.prototype.write=function(t){let e;for(e=0;e+2<=this.data.length;e+=2){let r=45*at.indexOf(this.data[e]);r+=at.indexOf(this.data[e+1]),t.put(r,11)}this.data.length%2&&t.put(at.indexOf(this.data[e]),6)};var ie=F;const se=function(t){for(var e=[],r=t.length,n=0;n<r;n++){var o=t.charCodeAt(n);if(o>=55296&&o<=56319&&r>n+1){var a=t.charCodeAt(n+1);a>=56320&&a<=57343&&(o=1024*(o-55296)+a-56320+65536,n+=1)}o<128?e.push(o):o<2048?(e.push(o>>6|192),e.push(63&o|128)):o<55296||o>=57344&&o<65536?(e.push(o>>12|224),e.push(o>>6&63|128),e.push(63&o|128)):o>=65536&&o<=1114111?(e.push(o>>18|240),e.push(o>>12&63|128),e.push(o>>6&63|128),e.push(63&o|128)):e.push(239,191,189)}return new Uint8Array(e).buffer},ae=_;function H(t){this.mode=ae.BYTE,typeof t=="string"&&(t=se(t)),this.data=new Uint8Array(t)}H.getBitsLength=function(t){return 8*t},H.prototype.getLength=function(){return this.data.length},H.prototype.getBitsLength=function(){return H.getBitsLength(this.data.length)},H.prototype.write=function(t){for(let e=0,r=this.data.length;e<r;e++)t.put(this.data[e],8)};var ue=H;const ce=_,le=R;function K(t){this.mode=ce.KANJI,this.data=t}K.getBitsLength=function(t){return 13*t},K.prototype.getLength=function(){return this.data.length},K.prototype.getBitsLength=function(){return K.getBitsLength(this.data.length)},K.prototype.write=function(t){let e;for(e=0;e<this.data.length;e++){let r=le.toSJIS(this.data[e]);if(r>=33088&&r<=40956)r-=33088;else{if(!(r>=57408&&r<=60351))throw new Error("Invalid SJIS character: "+this.data[e]+`
Make sure your charset is UTF-8`);r-=49472}r=192*(r>>>8&255)+(255&r),t.put(r,13)}};var fe=K,Lt={exports:{}};(function(t){var e={single_source_shortest_paths:function(r,n,o){var a={},i={};i[n]=0;var l,f,s,u,c,h,w,d=e.PriorityQueue.make();for(d.push(n,0);!d.empty();)for(s in f=(l=d.pop()).value,u=l.cost,c=r[f]||{})c.hasOwnProperty(s)&&(h=u+c[s],w=i[s],(i[s]===void 0||w>h)&&(i[s]=h,d.push(s,h),a[s]=f));if(o!==void 0&&i[o]===void 0){var p=["Could not find a path from ",n," to ",o,"."].join("");throw new Error(p)}return a},extract_shortest_path_from_predecessor_list:function(r,n){for(var o=[],a=n;a;)o.push(a),r[a],a=r[a];return o.reverse(),o},find_path:function(r,n,o){var a=e.single_source_shortest_paths(r,n,o);return e.extract_shortest_path_from_predecessor_list(a,o)},PriorityQueue:{make:function(r){var n,o=e.PriorityQueue,a={};for(n in r=r||{},o)o.hasOwnProperty(n)&&(a[n]=o[n]);return a.queue=[],a.sorter=r.sorter||o.default_sorter,a},default_sorter:function(r,n){return r.cost-n.cost},push:function(r,n){var o={value:r,cost:n};this.queue.push(o),this.queue.sort(this.sorter)},pop:function(){return this.queue.shift()},empty:function(){return this.queue.length===0}}};t.exports=e})(Lt);var he=Lt.exports;(function(t){const e=_,r=re,n=ie,o=ue,a=fe,i=U,l=R,f=he;function s(d){return unescape(encodeURIComponent(d)).length}function u(d,p,g){const v=[];let m;for(;(m=d.exec(g))!==null;)v.push({data:m[0],index:m.index,mode:p,length:m[0].length});return v}function c(d){const p=u(i.NUMERIC,e.NUMERIC,d),g=u(i.ALPHANUMERIC,e.ALPHANUMERIC,d);let v,m;return l.isKanjiModeEnabled()?(v=u(i.BYTE,e.BYTE,d),m=u(i.KANJI,e.KANJI,d)):(v=u(i.BYTE_KANJI,e.BYTE,d),m=[]),p.concat(g,v,m).sort(function(E,y){return E.index-y.index}).map(function(E){return{data:E.data,mode:E.mode,length:E.length}})}function h(d,p){switch(p){case e.NUMERIC:return r.getBitsLength(d);case e.ALPHANUMERIC:return n.getBitsLength(d);case e.KANJI:return a.getBitsLength(d);case e.BYTE:return o.getBitsLength(d)}}function w(d,p){let g;const v=e.getBestModeForData(d);if(g=e.from(p,v),g!==e.BYTE&&g.bit<v.bit)throw new Error('"'+d+'" cannot be encoded with mode '+e.toString(g)+`.
 Suggested mode is: `+e.toString(v));switch(g!==e.KANJI||l.isKanjiModeEnabled()||(g=e.BYTE),g){case e.NUMERIC:return new r(d);case e.ALPHANUMERIC:return new n(d);case e.KANJI:return new a(d);case e.BYTE:return new o(d)}}t.fromArray=function(d){return d.reduce(function(p,g){return typeof g=="string"?p.push(w(g,null)):g.data&&p.push(w(g.data,g.mode)),p},[])},t.fromString=function(d,p){const g=function(y){const A=[];for(let b=0;b<y.length;b++){const C=y[b];switch(C.mode){case e.NUMERIC:A.push([C,{data:C.data,mode:e.ALPHANUMERIC,length:C.length},{data:C.data,mode:e.BYTE,length:C.length}]);break;case e.ALPHANUMERIC:A.push([C,{data:C.data,mode:e.BYTE,length:C.length}]);break;case e.KANJI:A.push([C,{data:C.data,mode:e.BYTE,length:s(C.data)}]);break;case e.BYTE:A.push([{data:C.data,mode:e.BYTE,length:s(C.data)}])}}return A}(c(d,l.isKanjiModeEnabled())),v=function(y,A){const b={},C={start:{}};let L=["start"];for(let P=0;P<y.length;P++){const T=y[P],N=[];for(let M=0;M<T.length;M++){const B=T[M],x=""+P+M;N.push(x),b[x]={node:B,lastCount:0},C[x]={};for(let J=0;J<L.length;J++){const I=L[J];b[I]&&b[I].node.mode===B.mode?(C[I][x]=h(b[I].lastCount+B.length,B.mode)-h(b[I].lastCount,B.mode),b[I].lastCount+=B.length):(b[I]&&(b[I].lastCount=B.length),C[I][x]=h(B.length,B.mode)+4+e.getCharCountIndicator(B.mode,A))}}L=N}for(let P=0;P<L.length;P++)C[L[P]].end=0;return{map:C,table:b}}(g,p),m=f.find_path(v.map,"start","end"),E=[];for(let y=1;y<m.length-1;y++)E.push(v.table[m[y]].node);return t.fromArray(function(y){return y.reduce(function(A,b){const C=A.length-1>=0?A[A.length-1]:null;return C&&C.mode===b.mode?(A[A.length-1].data+=b.data,A):(A.push(b),A)},[])}(E))},t.rawSplit=function(d){return t.fromArray(c(d,l.isKanjiModeEnabled()))}})(Rt);const tt=R,ut=q,de=qt,ge=$t,pe=vt,we=Ct,ct=At,lt=$,me=Xt,et=It,ye=Pt,Ee=_,ft=Rt;function ht(t,e,r){const n=t.size,o=ye.getEncodedBits(e,r);let a,i;for(a=0;a<15;a++)i=(o>>a&1)==1,a<6?t.set(a,8,i,!0):a<8?t.set(a+1,8,i,!0):t.set(n-15+a,8,i,!0),a<8?t.set(8,n-a-1,i,!0):a<9?t.set(8,15-a-1+1,i,!0):t.set(8,15-a-1,i,!0);t.set(n-8,8,1,!0)}function ve(t,e,r){const n=new de;r.forEach(function(i){n.put(i.mode.bit,4),n.put(i.getLength(),Ee.getCharCountIndicator(i.mode,t)),i.write(n)});const o=8*(tt.getSymbolTotalCodewords(t)-lt.getTotalCodewordsCount(t,e));for(n.getLengthInBits()+4<=o&&n.put(0,4);n.getLengthInBits()%8!=0;)n.putBit(0);const a=(o-n.getLengthInBits())/8;for(let i=0;i<a;i++)n.put(i%2?17:236,8);return function(i,l,f){const s=tt.getSymbolTotalCodewords(l),u=lt.getTotalCodewordsCount(l,f),c=s-u,h=lt.getBlocksCount(l,f),w=s%h,d=h-w,p=Math.floor(s/h),g=Math.floor(c/h),v=g+1,m=p-g,E=new me(m);let y=0;const A=new Array(h),b=new Array(h);let C=0;const L=new Uint8Array(i.buffer);for(let B=0;B<h;B++){const x=B<d?g:v;A[B]=L.slice(y,y+x),b[B]=E.encode(A[B]),y+=x,C=Math.max(C,x)}const P=new Uint8Array(s);let T,N,M=0;for(T=0;T<C;T++)for(N=0;N<h;N++)T<A[N].length&&(P[M++]=A[N][T]);for(T=0;T<m;T++)for(N=0;N<h;N++)P[M++]=b[N][T];return P}(n,t,e)}function Ce(t,e,r,n){let o;if(Array.isArray(t))o=ft.fromArray(t);else{if(typeof t!="string")throw new Error("Invalid data");{let s=e;if(!s){const u=ft.rawSplit(t);s=et.getBestVersionForData(u,r)}o=ft.fromString(t,s||40)}}const a=et.getBestVersionForData(o,r);if(!a)throw new Error("The amount of data is too big to be stored in a QR Code");if(e){if(e<a)throw new Error(`
The chosen QR Code version cannot contain this amount of data.
Minimum version required to store current data is: `+a+`.
`)}else e=a;const i=ve(e,r,o),l=tt.getSymbolSize(e),f=new ge(l);return function(s,u){const c=s.size,h=we.getPositions(u);for(let w=0;w<h.length;w++){const d=h[w][0],p=h[w][1];for(let g=-1;g<=7;g++)if(!(d+g<=-1||c<=d+g))for(let v=-1;v<=7;v++)p+v<=-1||c<=p+v||(g>=0&&g<=6&&(v===0||v===6)||v>=0&&v<=6&&(g===0||g===6)||g>=2&&g<=4&&v>=2&&v<=4?s.set(d+g,p+v,!0,!0):s.set(d+g,p+v,!1,!0))}}(f,e),function(s){const u=s.size;for(let c=8;c<u-8;c++){const h=c%2==0;s.set(c,6,h,!0),s.set(6,c,h,!0)}}(f),function(s,u){const c=pe.getPositions(u);for(let h=0;h<c.length;h++){const w=c[h][0],d=c[h][1];for(let p=-2;p<=2;p++)for(let g=-2;g<=2;g++)p===-2||p===2||g===-2||g===2||p===0&&g===0?s.set(w+p,d+g,!0,!0):s.set(w+p,d+g,!1,!0)}}(f,e),ht(f,r,0),e>=7&&function(s,u){const c=s.size,h=et.getEncodedBits(u);let w,d,p;for(let g=0;g<18;g++)w=Math.floor(g/3),d=g%3+c-8-3,p=(h>>g&1)==1,s.set(w,d,p,!0),s.set(d,w,p,!0)}(f,e),function(s,u){const c=s.size;let h=-1,w=c-1,d=7,p=0;for(let g=c-1;g>0;g-=2)for(g===6&&g--;;){for(let v=0;v<2;v++)if(!s.isReserved(w,g-v)){let m=!1;p<u.length&&(m=(u[p]>>>d&1)==1),s.set(w,g-v,m),d--,d===-1&&(p++,d=7)}if(w+=h,w<0||c<=w){w-=h,h=-h;break}}}(f,i),isNaN(n)&&(n=ct.getBestMask(f,ht.bind(null,f,r))),ct.applyMask(n,f),ht(f,r,n),{modules:f,version:e,errorCorrectionLevel:r,maskPattern:n,segments:o}}yt.create=function(t,e){if(t===void 0||t==="")throw new Error("No input text");let r,n,o=ut.M;return e!==void 0&&(o=ut.from(e.errorCorrectionLevel,ut.M),r=et.from(e.version),n=ct.from(e.maskPattern),e.toSJISFunc&&tt.setToSJISFunction(e.toSJISFunc)),Ce(t,r,o,n)};var xt={},dt={};(function(t){function e(r){if(typeof r=="number"&&(r=r.toString()),typeof r!="string")throw new Error("Color should be defined as hex string");let n=r.slice().replace("#","").split("");if(n.length<3||n.length===5||n.length>8)throw new Error("Invalid hex color: "+r);n.length!==3&&n.length!==4||(n=Array.prototype.concat.apply([],n.map(function(a){return[a,a]}))),n.length===6&&n.push("F","F");const o=parseInt(n.join(""),16);return{r:o>>24&255,g:o>>16&255,b:o>>8&255,a:255&o,hex:"#"+n.slice(0,6).join("")}}t.getOptions=function(r){r||(r={}),r.color||(r.color={});const n=r.margin===void 0||r.margin===null||r.margin<0?4:r.margin,o=r.width&&r.width>=21?r.width:void 0,a=r.scale||4;return{width:o,scale:o?4:a,margin:n,color:{dark:e(r.color.dark||"#000000ff"),light:e(r.color.light||"#ffffffff")},type:r.type,rendererOpts:r.rendererOpts||{}}},t.getScale=function(r,n){return n.width&&n.width>=r+2*n.margin?n.width/(r+2*n.margin):n.scale},t.getImageWidth=function(r,n){const o=t.getScale(r,n);return Math.floor((r+2*n.margin)*o)},t.qrToImageData=function(r,n,o){const a=n.modules.size,i=n.modules.data,l=t.getScale(a,o),f=Math.floor((a+2*o.margin)*l),s=o.margin*l,u=[o.color.light,o.color.dark];for(let c=0;c<f;c++)for(let h=0;h<f;h++){let w=4*(c*f+h),d=o.color.light;c>=s&&h>=s&&c<f-s&&h<f-s&&(d=u[i[Math.floor((c-s)/l)*a+Math.floor((h-s)/l)]?1:0]),r[w++]=d.r,r[w++]=d.g,r[w++]=d.b,r[w]=d.a}}})(dt),function(t){const e=dt;t.render=function(r,n,o){let a=o,i=n;a!==void 0||n&&n.getContext||(a=n,n=void 0),n||(i=function(){try{return document.createElement("canvas")}catch{throw new Error("You need to specify a canvas element")}}()),a=e.getOptions(a);const l=e.getImageWidth(r.modules.size,a),f=i.getContext("2d"),s=f.createImageData(l,l);return e.qrToImageData(s.data,r,a),function(u,c,h){u.clearRect(0,0,c.width,c.height),c.style||(c.style={}),c.height=h,c.width=h,c.style.height=h+"px",c.style.width=h+"px"}(f,i,l),f.putImageData(s,0,0),i},t.renderToDataURL=function(r,n,o){let a=o;a!==void 0||n&&n.getContext||(a=n,n=void 0),a||(a={});const i=t.render(r,n,a),l=a.type||"image/png",f=a.rendererOpts||{};return i.toDataURL(l,f.quality)}}(xt);var St={};const Ae=dt;function Ut(t,e){const r=t.a/255,n=e+'="'+t.hex+'"';return r<1?n+" "+e+'-opacity="'+r.toFixed(2).slice(1)+'"':n}function gt(t,e,r){let n=t+e;return r!==void 0&&(n+=" "+r),n}St.render=function(t,e,r){const n=Ae.getOptions(e),o=t.modules.size,a=t.modules.data,i=o+2*n.margin,l=n.color.light.a?"<path "+Ut(n.color.light,"fill")+' d="M0 0h'+i+"v"+i+'H0z"/>':"",f="<path "+Ut(n.color.dark,"stroke")+' d="'+function(c,h,w){let d="",p=0,g=!1,v=0;for(let m=0;m<c.length;m++){const E=Math.floor(m%h),y=Math.floor(m/h);E||g||(g=!0),c[m]?(v++,m>0&&E>0&&c[m-1]||(d+=g?gt("M",E+w,.5+y+w):gt("m",p,0),p=0,g=!1),E+1<h&&c[m+1]||(d+=gt("h",v),v=0)):p++}return d}(a,o,n.margin)+'"/>',s='viewBox="0 0 '+i+" "+i+'"',u='<svg xmlns="http://www.w3.org/2000/svg" '+(n.width?'width="'+n.width+'" height="'+n.width+'" ':"")+s+' shape-rendering="crispEdges">'+l+f+`</svg>
`;return typeof r=="function"&&r(null,u),u};const be=function(){return typeof Promise=="function"&&Promise.prototype&&Promise.prototype.then},pt=yt,kt=xt,Be=St;function wt(t,e,r,n,o){const a=[].slice.call(arguments,1),i=a.length,l=typeof a[i-1]=="function";if(!l&&!be())throw new Error("Callback required as last argument");if(!l){if(i<1)throw new Error("Too few arguments provided");return i===1?(r=e,e=n=void 0):i!==2||e.getContext||(n=r,r=e,e=void 0),new Promise(function(f,s){try{const u=pt.create(r,n);f(t(u,e,n))}catch(u){s(u)}})}if(i<2)throw new Error("Too few arguments provided");i===2?(o=r,r=e,e=n=void 0):i===3&&(e.getContext&&o===void 0?(o=n,n=void 0):(o=n,n=r,r=e,e=void 0));try{const f=pt.create(r,n);o(null,t(f,e,n))}catch(f){o(f)}}Y.create=pt.create,Y.toCanvas=wt.bind(null,kt.render),Y.toDataURL=wt.bind(null,kt.renderToDataURL),Y.toString=wt.bind(null,function(t,e,r){return Be.render(t,r)});let _t;_t={class:"absolute left-[50%] top-[50%] font-bold"},Qt=Je(Me({name:"Qrcode",__name:"Qrcode",props:{tag:rt.string.validate(t=>["canvas","img"].includes(t)).def("canvas"),text:{type:[String,Array],default:null},options:{type:Object,default:()=>({})},width:rt.number.def(200),logo:{type:[String,Object],default:""},disabled:rt.bool.def(!1),disabledText:rt.string.def("")},emits:["done","click","disabled-click"],setup(t,{emit:e}){const r=t,n=e,{getPrefixCls:o}=_e(),a=o("qrcode"),{toCanvas:i,toDataURL:l}=Y,f=Ht(!0),s=Ht(null),u=Kt(()=>String(r.text)),c=Kt(()=>({width:r.width+"px",height:r.width+"px"}));Pe(()=>u.value,m=>{m&&(async()=>{await Fe();const E=He(r.options||{});if(r.tag==="canvas"){E.errorCorrectionLevel=E.errorCorrectionLevel||d(z(u));const y=await w(z(u),E);E.scale=r.width===0?void 0:r.width/y*4;const A=await i(z(s),z(u),E);if(r.logo){const b=await h(A);n("done",b),f.value=!1}else n("done",A.toDataURL()),f.value=!1}else{const y=await l(u.value,{errorCorrectionLevel:"H",width:r.width,...E});z(s).src=y,n("done",y),f.value=!1}})()},{deep:!0,immediate:!0});const h=m=>{const E=m.width,y=Object.assign({logoSize:.15,bgColor:"#ffffff",borderSize:.05,crossOrigin:"anonymous",borderRadius:8,logoRadius:0},jt(r.logo)?{}:r.logo),{logoSize:A=.15,bgColor:b="#ffffff",borderSize:C=.05,crossOrigin:L="anonymous",borderRadius:P=8,logoRadius:T=0}=y,N=jt(r.logo)?r.logo:r.logo.src,M=E*A,B=E*(1-A)/2,x=E*(A+C),J=E*(1-A-C)/2,I=m.getContext("2d");if(!I)return;p(I)(J,J,x,x,P),I.fillStyle=b,I.fill();const V=new Image;return(L||T)&&V.setAttribute("crossOrigin",L),V.src=N,new Promise(Ie=>{V.onload=()=>{var zt;T?(Te=>{const nt=document.createElement("canvas");nt.width=B+M,nt.height=B+M;const Dt=nt.getContext("2d");if(!Dt||!I||(Dt.drawImage(Te,B,B,M,M),p(I)(B,B,M,M,T),!I))return;const Ft=I.createPattern(nt,"no-repeat");Ft&&(I.fillStyle=Ft,I.fill())})(V):(zt=V,I.drawImage(zt,B,B,M,M)),Ie(m.toDataURL())}})},w=async(m,E)=>{const y=document.createElement("canvas");return await i(y,m,E),y.width},d=m=>m.length>36?"M":m.length>16?"Q":"H",p=m=>(E,y,A,b,C)=>{const L=Math.min(A,b);return C>L/2&&(C=L/2),m.beginPath(),m.moveTo(E+C,y),m.arcTo(E+A,y,E+A,y+b,C),m.arcTo(E+A,y+b,E,y+b,C),m.arcTo(E,y+b,E,y,C),m.arcTo(E,y,E+A,y,C),m.closePath(),m},g=()=>{n("click")},v=()=>{n("disabled-click")};return(m,E)=>{const y=De,A=ze;return Ne((mt(),Jt("div",{class:Yt([z(a),"relative inline-block"]),style:ke(c.value)},[(mt(),Re(Le(t.tag),{ref_key:"wrapRef",ref:s,onClick:g},null,512)),t.disabled?(mt(),Jt("div",{key:0,class:Yt([`${z(a)}--disabled`,"absolute left-0 top-0 h-full w-full flex items-center justify-center"]),onClick:v},[Ot("div",_t,[xe(y,{size:30,color:"var(--el-color-primary)",icon:"ep:refresh-right"}),Ot("div",null,Se(t.disabledText),1)])],2)):Ue("",!0)],6)),[[A,f.value]])}}}),[["__scopeId","data-v-8fc6cf2d"]])});export{Qt as Q,Ye as __tla};
