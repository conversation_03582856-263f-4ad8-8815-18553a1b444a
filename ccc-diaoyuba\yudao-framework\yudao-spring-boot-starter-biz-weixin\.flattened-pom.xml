<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>cn.iocoder.boot</groupId>
    <artifactId>yudao-framework</artifactId>
    <version>1.8.3-snapshot</version>
  </parent>
  <groupId>cn.iocoder.boot</groupId>
  <artifactId>yudao-spring-boot-starter-biz-weixin</artifactId>
  <version>1.8.3-snapshot</version>
  <name>${project.artifactId}</name>
  <description>微信拓展
        1. 基于 weixin-java-mp 库，对接微信公众号平台。目前主要解决微信公众号的支付场景。
        2. 基于 weixin-java-miniapp 库，对接微信小程序。目前主要解决微信小程序的一键登录场景。</description>
  <url>https://github.com/YunaiV/ruoyi-vue-pro</url>
  <dependencies>
    <dependency>
      <groupId>cn.iocoder.boot</groupId>
      <artifactId>yudao-common</artifactId>
    </dependency>
    <dependency>
      <groupId>cn.iocoder.boot</groupId>
      <artifactId>yudao-spring-boot-starter-test</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.github.binarywang</groupId>
      <artifactId>wx-java-mp-spring-boot-starter</artifactId>
    </dependency>
    <dependency>
      <groupId>com.github.binarywang</groupId>
      <artifactId>wx-java-miniapp-spring-boot-starter</artifactId>
    </dependency>
  </dependencies>
</project>
