import{_ as t,__tla as r}from"./ShortcutCard.vue_vue_type_script_setup_true_lang-044e9973.js";import{__tla as _}from"./index-97fffa0c.js";import{__tla as a}from"./el-card-6c7c099d.js";import{__tla as l}from"./CardTitle-c3925800.js";import"./_plugin-vue_export-helper-1b428a4d.js";let o=Promise.all([(()=>{try{return r}catch{}})(),(()=>{try{return _}catch{}})(),(()=>{try{return a}catch{}})(),(()=>{try{return l}catch{}})()]).then(async()=>{});export{o as __tla,t as default};
