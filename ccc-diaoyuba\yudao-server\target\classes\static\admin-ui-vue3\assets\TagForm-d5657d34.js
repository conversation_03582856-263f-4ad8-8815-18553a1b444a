import{_ as t,__tla as _}from"./TagForm.vue_vue_type_script_setup_true_lang-be7a57c3.js";import{__tla as a}from"./index-97fffa0c.js";import{__tla as r}from"./Dialog.vue_vue_type_style_index_0_lang-5af0bc2b.js";import{__tla as l}from"./useMessage-18385d4a.js";let e=Promise.all([(()=>{try{return _}catch{}})(),(()=>{try{return a}catch{}})(),(()=>{try{return r}catch{}})(),(()=>{try{return l}catch{}})()]).then(async()=>{});export{e as __tla,t as default};
