import{_ as t,__tla as r}from"./UserPointList.vue_vue_type_script_setup_true_lang-9f197d2d.js";import{__tla as _}from"./index-97fffa0c.js";import{__tla as a}from"./index.vue_vue_type_script_setup_true_lang-d31568cf.js";import{__tla as l}from"./index-8d6db4ce.js";import{__tla as o}from"./DictTag.vue_vue_type_script_lang-5679ad7b.js";import"./color-a8b4eb58.js";import{__tla as m}from"./dict-6a82eb12.js";import{__tla as c}from"./ContentWrap.vue_vue_type_script_setup_true_lang-a574ccef.js";import{__tla as e}from"./el-card-6c7c099d.js";import{__tla as s}from"./formatTime-9d54d2c5.js";import{__tla as i}from"./index-ce39d4b0.js";let n=Promise.all([(()=>{try{return r}catch{}})(),(()=>{try{return _}catch{}})(),(()=>{try{return a}catch{}})(),(()=>{try{return l}catch{}})(),(()=>{try{return o}catch{}})(),(()=>{try{return m}catch{}})(),(()=>{try{return c}catch{}})(),(()=>{try{return e}catch{}})(),(()=>{try{return s}catch{}})(),(()=>{try{return i}catch{}})()]).then(async()=>{});export{n as __tla,t as default};
