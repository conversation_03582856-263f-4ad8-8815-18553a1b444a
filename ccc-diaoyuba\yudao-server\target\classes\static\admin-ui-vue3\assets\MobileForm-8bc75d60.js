import{d as J,a1 as K,u as Q,r as g,b as W,a as e,f as w,ax as X,B as Y,a2 as Z,o as _,q as C,w as l,i as a,a3 as b,c as S,t as V,l as $,db as ee,ad as ae,ae as le,a9 as I,dc as te,a6 as oe,D as ne,E as ie,T as re,n as se,I as de,__tla as me}from"./index-97fffa0c.js";import{_ as ce,__tla as pe}from"./XButton-dd4d8780.js";import{u as h,__tla as ue}from"./useIcon-4b1d730a.js";import{u as ge,L as _e,_ as fe,a as be,__tla as he}from"./LoginFormTitle.vue_vue_type_script_setup_true_lang-f56773d4.js";import{u as ye,__tla as xe}from"./useMessage-18385d4a.js";import{r as y,__tla as ve}from"./formRules-8010a921.js";import{_ as Fe}from"./_plugin-vue_export-helper-1b428a4d.js";let L,Ne=Promise.all([(()=>{try{return me}catch{}})(),(()=>{try{return pe}catch{}})(),(()=>{try{return ue}catch{}})(),(()=>{try{return he}catch{}})(),(()=>{try{return xe}catch{}})(),(()=>{try{return ve}catch{}})()]).then(async()=>{let x;x={key:1,class:"getMobileCode",style:{cursor:"pointer"}},L=Fe(J({name:"MobileForm",__name:"MobileForm",setup(ke){const{t:i}=$(),M=ye(),E=K(),{currentRoute:P,push:U}=Q(),v=g(),p=g(!1),B=h({icon:"ep:house"}),R=h({icon:"ep:cellphone"}),q=h({icon:"ep:circle-check"}),{validForm:T}=be(v),{handleBackLogin:j,getLoginState:z}=ge(),D=W(()=>e(z)===_e.MOBILE),O={tenantName:[y],mobileNumber:[y],code:[y]},t=w({codeImg:"",tenantEnable:"true",token:"",loading:{signIn:!1},loginForm:{uuid:"",tenantName:"\u828B\u9053\u6E90\u7801",mobileNumber:"",code:""}}),c=w({smsCode:{mobile:"",scene:21},loginSms:{mobile:"",code:""}}),r=g(0),u=g(""),A=async()=>{await F(),c.smsCode.mobile=t.loginForm.mobileNumber,await ee(c.smsCode).then(async()=>{M.success(i("login.SmsSendMsg")),r.value=60;let n=setInterval(()=>{r.value=r.value-1,r.value<=0&&clearInterval(n)},1e3)})};X(()=>P.value,n=>{var o;u.value=(o=n==null?void 0:n.query)==null?void 0:o.redirect},{immediate:!0});const F=async()=>{if(t.tenantEnable==="true"){const n=await ae(t.loginForm.tenantName);le(n)}};return(n,o)=>{const m=ne,s=ie,f=re,N=se,k=ce,G=de;return Y((_(),C(G,{ref_key:"formSmsLogin",ref:v,model:e(t).loginForm,rules:O,class:"login-form","label-position":"top","label-width":"120px",size:"large"},{default:l(()=>[a(N,{style:{"margin-right":"-10px","margin-left":"-10px"}},{default:l(()=>[a(s,{span:24,style:{"padding-right":"10px","padding-left":"10px"}},{default:l(()=>[a(m,null,{default:l(()=>[a(fe,{style:{width:"100%"}})]),_:1})]),_:1}),a(s,{span:24,style:{"padding-right":"10px","padding-left":"10px"}},{default:l(()=>[e(t).tenantEnable==="true"?(_(),C(m,{key:0,prop:"tenantName"},{default:l(()=>[a(f,{modelValue:e(t).loginForm.tenantName,"onUpdate:modelValue":o[0]||(o[0]=d=>e(t).loginForm.tenantName=d),placeholder:e(i)("login.tenantNamePlaceholder"),"prefix-icon":e(B),type:"primary",link:""},null,8,["modelValue","placeholder","prefix-icon"])]),_:1})):b("",!0)]),_:1}),a(s,{span:24,style:{"padding-right":"10px","padding-left":"10px"}},{default:l(()=>[a(m,{prop:"mobileNumber"},{default:l(()=>[a(f,{modelValue:e(t).loginForm.mobileNumber,"onUpdate:modelValue":o[1]||(o[1]=d=>e(t).loginForm.mobileNumber=d),placeholder:e(i)("login.mobileNumberPlaceholder"),"prefix-icon":e(R)},null,8,["modelValue","placeholder","prefix-icon"])]),_:1})]),_:1}),a(s,{span:24,style:{"padding-right":"10px","padding-left":"10px"}},{default:l(()=>[a(m,{prop:"code"},{default:l(()=>[a(N,{gutter:5,justify:"space-between",style:{width:"100%"}},{default:l(()=>[a(s,{span:24},{default:l(()=>[a(f,{modelValue:e(t).loginForm.code,"onUpdate:modelValue":o[2]||(o[2]=d=>e(t).loginForm.code=d),placeholder:e(i)("login.codePlaceholder"),"prefix-icon":e(q)},{append:l(()=>[e(r)<=0?(_(),S("span",{key:0,class:"getMobileCode",style:{cursor:"pointer"},onClick:A},V(e(i)("login.getSmsCode")),1)):b("",!0),e(r)>0?(_(),S("span",x,V(e(r))+"\u79D2\u540E\u53EF\u91CD\u65B0\u83B7\u53D6 ",1)):b("",!0)]),_:1},8,["modelValue","placeholder","prefix-icon"])]),_:1})]),_:1})]),_:1})]),_:1}),a(s,{span:24,style:{"padding-right":"10px","padding-left":"10px"}},{default:l(()=>[a(m,null,{default:l(()=>[a(k,{loading:e(p),title:e(i)("login.login"),class:"w-[100%]",type:"primary",onClick:o[3]||(o[3]=d=>(async()=>{await F(),await T()&&(I.service({lock:!0,text:"\u6B63\u5728\u52A0\u8F7D\u7CFB\u7EDF\u4E2D...",background:"rgba(0, 0, 0, 0.7)"}),p.value=!0,c.loginSms.mobile=t.loginForm.mobileNumber,c.loginSms.code=t.loginForm.code,await te(c.loginSms).then(async H=>{oe(H),u.value||(u.value="/"),U({path:u.value||E.addRouters[0].path})}).catch(()=>{}).finally(()=>{p.value=!1,setTimeout(()=>{I.service().close()},400)}))})())},null,8,["loading","title"])]),_:1})]),_:1}),a(s,{span:24,style:{"padding-right":"10px","padding-left":"10px"}},{default:l(()=>[a(m,null,{default:l(()=>[a(k,{loading:e(p),title:e(i)("login.backLogin"),class:"w-[100%]",onClick:o[4]||(o[4]=d=>e(j)())},null,8,["loading","title"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])),[[Z,e(D)]])}}}),[["__scopeId","data-v-a3cd5b9c"]])});export{Ne as __tla,L as default};
