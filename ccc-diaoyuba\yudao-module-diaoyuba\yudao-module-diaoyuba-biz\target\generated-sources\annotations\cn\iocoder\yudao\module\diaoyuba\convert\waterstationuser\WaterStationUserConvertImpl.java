package cn.iocoder.yudao.module.diaoyuba.convert.waterstationuser;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.diaoyuba.controller.app.waterstationuser.vo.AppWaterStationUserCreateReqVO;
import cn.iocoder.yudao.module.diaoyuba.controller.app.waterstationuser.vo.AppWaterStationUserExcelVO;
import cn.iocoder.yudao.module.diaoyuba.controller.app.waterstationuser.vo.AppWaterStationUserRespVO;
import cn.iocoder.yudao.module.diaoyuba.controller.app.waterstationuser.vo.AppWaterStationUserUpdateReqVO;
import cn.iocoder.yudao.module.diaoyuba.dal.dataobject.waterstationuser.WaterStationUserDO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-21T09:34:05+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_181 (Oracle Corporation)"
)
public class WaterStationUserConvertImpl implements WaterStationUserConvert {

    @Override
    public WaterStationUserDO convert(AppWaterStationUserCreateReqVO bean) {
        if ( bean == null ) {
            return null;
        }

        WaterStationUserDO.WaterStationUserDOBuilder waterStationUserDO = WaterStationUserDO.builder();

        waterStationUserDO.stationId( bean.getStationId() );
        waterStationUserDO.userId( bean.getUserId() );

        return waterStationUserDO.build();
    }

    @Override
    public WaterStationUserDO convert(AppWaterStationUserUpdateReqVO bean) {
        if ( bean == null ) {
            return null;
        }

        WaterStationUserDO.WaterStationUserDOBuilder waterStationUserDO = WaterStationUserDO.builder();

        waterStationUserDO.id( bean.getId() );
        waterStationUserDO.stationId( bean.getStationId() );
        waterStationUserDO.userId( bean.getUserId() );

        return waterStationUserDO.build();
    }

    @Override
    public AppWaterStationUserRespVO convert(WaterStationUserDO bean) {
        if ( bean == null ) {
            return null;
        }

        AppWaterStationUserRespVO appWaterStationUserRespVO = new AppWaterStationUserRespVO();

        appWaterStationUserRespVO.setStationId( bean.getStationId() );
        appWaterStationUserRespVO.setUserId( bean.getUserId() );
        appWaterStationUserRespVO.setId( bean.getId() );
        appWaterStationUserRespVO.setCreateTime( bean.getCreateTime() );

        return appWaterStationUserRespVO;
    }

    @Override
    public List<AppWaterStationUserRespVO> convertList(List<WaterStationUserDO> list) {
        if ( list == null ) {
            return null;
        }

        List<AppWaterStationUserRespVO> list1 = new ArrayList<AppWaterStationUserRespVO>( list.size() );
        for ( WaterStationUserDO waterStationUserDO : list ) {
            list1.add( convert( waterStationUserDO ) );
        }

        return list1;
    }

    @Override
    public PageResult<AppWaterStationUserRespVO> convertPage(PageResult<WaterStationUserDO> page) {
        if ( page == null ) {
            return null;
        }

        PageResult<AppWaterStationUserRespVO> pageResult = new PageResult<AppWaterStationUserRespVO>();

        pageResult.setList( convertList( page.getList() ) );
        pageResult.setTotal( page.getTotal() );

        return pageResult;
    }

    @Override
    public List<AppWaterStationUserExcelVO> convertList02(List<WaterStationUserDO> list) {
        if ( list == null ) {
            return null;
        }

        List<AppWaterStationUserExcelVO> list1 = new ArrayList<AppWaterStationUserExcelVO>( list.size() );
        for ( WaterStationUserDO waterStationUserDO : list ) {
            list1.add( waterStationUserDOToAppWaterStationUserExcelVO( waterStationUserDO ) );
        }

        return list1;
    }

    protected AppWaterStationUserExcelVO waterStationUserDOToAppWaterStationUserExcelVO(WaterStationUserDO waterStationUserDO) {
        if ( waterStationUserDO == null ) {
            return null;
        }

        AppWaterStationUserExcelVO appWaterStationUserExcelVO = new AppWaterStationUserExcelVO();

        appWaterStationUserExcelVO.setId( waterStationUserDO.getId() );
        appWaterStationUserExcelVO.setStationId( waterStationUserDO.getStationId() );
        appWaterStationUserExcelVO.setUserId( waterStationUserDO.getUserId() );
        appWaterStationUserExcelVO.setCreateTime( waterStationUserDO.getCreateTime() );

        return appWaterStationUserExcelVO;
    }
}
