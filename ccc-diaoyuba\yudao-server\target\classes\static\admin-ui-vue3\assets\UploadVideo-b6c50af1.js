import{_ as t,__tla as _}from"./UploadVideo.vue_vue_type_script_setup_true_lang-c2909efb.js";import{__tla as r}from"./index-97fffa0c.js";import{__tla as a}from"./upload-5ef645a3.js";import{__tla as l}from"./useUpload-36312237.js";import{__tla as o}from"./useMessage-18385d4a.js";let c=Promise.all([(()=>{try{return _}catch{}})(),(()=>{try{return r}catch{}})(),(()=>{try{return a}catch{}})(),(()=>{try{return l}catch{}})(),(()=>{try{return o}catch{}})()]).then(async()=>{});export{c as __tla,t as default};
