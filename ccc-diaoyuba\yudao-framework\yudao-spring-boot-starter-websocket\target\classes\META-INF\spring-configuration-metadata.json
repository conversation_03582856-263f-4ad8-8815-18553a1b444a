{"groups": [{"name": "yudao.websocket", "type": "cn.iocoder.yudao.framework.websocket.config.WebSocketProperties", "sourceType": "cn.iocoder.yudao.framework.websocket.config.WebSocketProperties"}], "properties": [{"name": "yudao.websocket.max-online-count", "type": "java.lang.Integer", "description": "默认最多允许同时在线用户数", "sourceType": "cn.iocoder.yudao.framework.websocket.config.WebSocketProperties", "defaultValue": 0}, {"name": "yudao.websocket.path", "type": "java.lang.String", "description": "路径", "sourceType": "cn.iocoder.yudao.framework.websocket.config.WebSocketProperties", "defaultValue": ""}, {"name": "yudao.websocket.session-map", "type": "java.lang.Bo<PERSON>an", "description": "是否保存session", "sourceType": "cn.iocoder.yudao.framework.websocket.config.WebSocketProperties", "defaultValue": true}], "hints": []}