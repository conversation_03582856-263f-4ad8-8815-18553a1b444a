package cn.iocoder.yudao.module.diaoyuba.convert.notecommentslike;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.diaoyuba.controller.app.notecommentslike.vo.AppNoteCommentsLikeCreateReqVO;
import cn.iocoder.yudao.module.diaoyuba.controller.app.notecommentslike.vo.AppNoteCommentsLikeExcelVO;
import cn.iocoder.yudao.module.diaoyuba.controller.app.notecommentslike.vo.AppNoteCommentsLikeRespVO;
import cn.iocoder.yudao.module.diaoyuba.controller.app.notecommentslike.vo.AppNoteCommentsLikeUpdateReqVO;
import cn.iocoder.yudao.module.diaoyuba.dal.dataobject.notecommentslike.NoteCommentsLikeDO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-21T09:34:05+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_181 (Oracle Corporation)"
)
public class NoteCommentsLikeConvertImpl implements NoteCommentsLikeConvert {

    @Override
    public NoteCommentsLikeDO convert(AppNoteCommentsLikeCreateReqVO bean) {
        if ( bean == null ) {
            return null;
        }

        NoteCommentsLikeDO.NoteCommentsLikeDOBuilder noteCommentsLikeDO = NoteCommentsLikeDO.builder();

        noteCommentsLikeDO.noteCommentsId( bean.getNoteCommentsId() );

        return noteCommentsLikeDO.build();
    }

    @Override
    public NoteCommentsLikeDO convert(AppNoteCommentsLikeUpdateReqVO bean) {
        if ( bean == null ) {
            return null;
        }

        NoteCommentsLikeDO.NoteCommentsLikeDOBuilder noteCommentsLikeDO = NoteCommentsLikeDO.builder();

        noteCommentsLikeDO.id( bean.getId() );
        noteCommentsLikeDO.noteCommentsId( bean.getNoteCommentsId() );

        return noteCommentsLikeDO.build();
    }

    @Override
    public AppNoteCommentsLikeRespVO convert(NoteCommentsLikeDO bean) {
        if ( bean == null ) {
            return null;
        }

        AppNoteCommentsLikeRespVO appNoteCommentsLikeRespVO = new AppNoteCommentsLikeRespVO();

        appNoteCommentsLikeRespVO.setNoteCommentsId( bean.getNoteCommentsId() );
        appNoteCommentsLikeRespVO.setId( bean.getId() );
        appNoteCommentsLikeRespVO.setCreateTime( bean.getCreateTime() );

        return appNoteCommentsLikeRespVO;
    }

    @Override
    public List<AppNoteCommentsLikeRespVO> convertList(List<NoteCommentsLikeDO> list) {
        if ( list == null ) {
            return null;
        }

        List<AppNoteCommentsLikeRespVO> list1 = new ArrayList<AppNoteCommentsLikeRespVO>( list.size() );
        for ( NoteCommentsLikeDO noteCommentsLikeDO : list ) {
            list1.add( convert( noteCommentsLikeDO ) );
        }

        return list1;
    }

    @Override
    public PageResult<AppNoteCommentsLikeRespVO> convertPage(PageResult<NoteCommentsLikeDO> page) {
        if ( page == null ) {
            return null;
        }

        PageResult<AppNoteCommentsLikeRespVO> pageResult = new PageResult<AppNoteCommentsLikeRespVO>();

        pageResult.setList( convertList( page.getList() ) );
        pageResult.setTotal( page.getTotal() );

        return pageResult;
    }

    @Override
    public List<AppNoteCommentsLikeExcelVO> convertList02(List<NoteCommentsLikeDO> list) {
        if ( list == null ) {
            return null;
        }

        List<AppNoteCommentsLikeExcelVO> list1 = new ArrayList<AppNoteCommentsLikeExcelVO>( list.size() );
        for ( NoteCommentsLikeDO noteCommentsLikeDO : list ) {
            list1.add( noteCommentsLikeDOToAppNoteCommentsLikeExcelVO( noteCommentsLikeDO ) );
        }

        return list1;
    }

    protected AppNoteCommentsLikeExcelVO noteCommentsLikeDOToAppNoteCommentsLikeExcelVO(NoteCommentsLikeDO noteCommentsLikeDO) {
        if ( noteCommentsLikeDO == null ) {
            return null;
        }

        AppNoteCommentsLikeExcelVO appNoteCommentsLikeExcelVO = new AppNoteCommentsLikeExcelVO();

        appNoteCommentsLikeExcelVO.setId( noteCommentsLikeDO.getId() );
        appNoteCommentsLikeExcelVO.setUserId( noteCommentsLikeDO.getUserId() );
        appNoteCommentsLikeExcelVO.setNoteCommentsId( noteCommentsLikeDO.getNoteCommentsId() );
        appNoteCommentsLikeExcelVO.setCreateTime( noteCommentsLikeDO.getCreateTime() );

        return appNoteCommentsLikeExcelVO;
    }
}
