import{ao as e,__tla as c}from"./index-97fffa0c.js";let a,t,l,n,m,r,u=Promise.all([(()=>{try{return c}catch{}})()]).then(async()=>{a=s=>e.get({url:"/system/sms-channel/page",params:s}),t=function(){return e.get({url:"/system/sms-channel/list-all-simple"})},m=s=>e.get({url:"/system/sms-channel/get?id="+s}),l=s=>e.post({url:"/system/sms-channel/create",data:s}),r=s=>e.put({url:"/system/sms-channel/update",data:s}),n=s=>e.delete({url:"/system/sms-channel/delete?id="+s})});export{u as __tla,a,t as b,l as c,n as d,m as g,r as u};
