import{d as R,u as G,N as H,r as _,f as I,A as J,ai as K,O as Y,o as i,c as Q,i as l,w as t,B as q,a as e,q as s,g as C,t as w,j as m,a3 as A,x as b,ah as W,F as X,J as Z,H as $,aj as aa,K as ta,L as ea,__tla as la}from"./index-97fffa0c.js";import{_ as ra,__tla as oa}from"./Dialog.vue_vue_type_style_index_0_lang-5af0bc2b.js";import{_ as ia,__tla as na}from"./ContentWrap.vue_vue_type_script_setup_true_lang-a574ccef.js";import{_ as _a,__tla as sa}from"./index.vue_vue_type_script_setup_true_lang-d31568cf.js";import{_ as ua,__tla as pa}from"./DictTag.vue_vue_type_script_lang-5679ad7b.js";import{D as ma,__tla as ca}from"./dict-6a82eb12.js";import{d as da,__tla as fa}from"./formatTime-9d54d2c5.js";import{j as ya,__tla as ha}from"./bpmn-embedded-5f95dcf2.js";import{g as wa,a as ga,__tla as ka}from"./index-a2bf77df.js";import{b as va}from"./formCreate-a3356cdc.js";import{__tla as Ca}from"./el-card-6c7c099d.js";import{__tla as ba}from"./index-8d6db4ce.js";import"./color-a8b4eb58.js";import{__tla as Va}from"./XTextButton-41b6d860.js";import"./_plugin-vue_export-helper-1b428a4d.js";import{__tla as xa}from"./XButton-dd4d8780.js";import{__tla as Pa}from"./el-link-f00f9c89.js";import{__tla as Ta}from"./useMessage-18385d4a.js";import{__tla as Ua}from"./el-drawer-0535e62a.js";let B,Na=Promise.all([(()=>{try{return la}catch{}})(),(()=>{try{return oa}catch{}})(),(()=>{try{return na}catch{}})(),(()=>{try{return sa}catch{}})(),(()=>{try{return pa}catch{}})(),(()=>{try{return ca}catch{}})(),(()=>{try{return fa}catch{}})(),(()=>{try{return ha}catch{}})(),(()=>{try{return ka}catch{}})(),(()=>{try{return Ca}catch{}})(),(()=>{try{return ba}catch{}})(),(()=>{try{return Va}catch{}})(),(()=>{try{return xa}catch{}})(),(()=>{try{return Pa}catch{}})(),(()=>{try{return Ta}catch{}})(),(()=>{try{return Ua}catch{}})()]).then(async()=>{B=R({name:"BpmProcessDefinition",__name:"index",setup(Sa){const{push:V}=G(),{query:D}=H(),g=_(!0),x=_(0),P=_([]),u=I({pageNo:1,pageSize:10,key:D.key}),T=async()=>{g.value=!0;try{const n=await wa(u);P.value=n.list,x.value=n.total}finally{g.value=!1}},c=_(!1),k=_({rule:[],option:{}}),U=async n=>{n.formType==10?(va(k,n.formConf,n.formFields),c.value=!0):await V({path:n.formCustomCreatePath})},d=_(!1),p=_(null),N=_({prefix:"flowable"});return J(()=>{T()}),(n,r)=>{const o=Z,f=$,L=ua,y=aa,j=ta,z=_a,E=ia,O=K("form-create"),S=ra,F=Y("hasPermi"),M=ea;return i(),Q(X,null,[l(E,null,{default:t(()=>[q((i(),s(j,{data:e(P)},{default:t(()=>[l(o,{label:"\u5B9A\u4E49\u7F16\u53F7",align:"center",prop:"id",width:"400"}),l(o,{label:"\u6D41\u7A0B\u540D\u79F0",align:"center",prop:"name",width:"200"},{default:t(a=>[l(f,{type:"primary",link:"",onClick:v=>(async h=>{p.value=await ga(h.id),d.value=!0})(a.row)},{default:t(()=>[C("span",null,w(a.row.name),1)]),_:2},1032,["onClick"])]),_:1}),l(o,{label:"\u5B9A\u4E49\u5206\u7C7B",align:"center",prop:"category",width:"100"},{default:t(a=>[l(L,{type:e(ma).BPM_MODEL_CATEGORY,value:a.row.category},null,8,["type","value"])]),_:1}),l(o,{label:"\u8868\u5355\u4FE1\u606F",align:"center",prop:"formType",width:"200"},{default:t(a=>[a.row.formType===10?(i(),s(f,{key:0,type:"primary",link:"",onClick:v=>U(a.row)},{default:t(()=>[C("span",null,w(a.row.formName),1)]),_:2},1032,["onClick"])):(i(),s(f,{key:1,type:"primary",link:"",onClick:v=>U(a.row)},{default:t(()=>[C("span",null,w(a.row.formCustomCreatePath),1)]),_:2},1032,["onClick"]))]),_:1}),l(o,{label:"\u6D41\u7A0B\u7248\u672C",align:"center",prop:"processDefinition.version",width:"80"},{default:t(a=>[a.row?(i(),s(y,{key:0},{default:t(()=>[m("v"+w(a.row.version),1)]),_:2},1024)):(i(),s(y,{key:1,type:"warning"},{default:t(()=>[m("\u672A\u90E8\u7F72")]),_:1}))]),_:1}),l(o,{label:"\u72B6\u6001",align:"center",prop:"version",width:"80"},{default:t(a=>[a.row.suspensionState===1?(i(),s(y,{key:0,type:"success"},{default:t(()=>[m("\u6FC0\u6D3B")]),_:1})):A("",!0),a.row.suspensionState===2?(i(),s(y,{key:1,type:"warning"},{default:t(()=>[m("\u6302\u8D77")]),_:1})):A("",!0)]),_:1}),l(o,{label:"\u90E8\u7F72\u65F6\u95F4",align:"center",prop:"deploymentTime",width:"180",formatter:e(da)},null,8,["formatter"]),l(o,{label:"\u5B9A\u4E49\u63CF\u8FF0",align:"center",prop:"description",width:"300","show-overflow-tooltip":""}),l(o,{label:"\u64CD\u4F5C",align:"center",width:"150",fixed:"right"},{default:t(a=>[q((i(),s(f,{link:"",type:"primary",onClick:v=>{return h=a.row,void V({name:"BpmTaskAssignRuleList",query:{modelId:h.id}});var h}},{default:t(()=>[m(" \u5206\u914D\u89C4\u5219 ")]),_:2},1032,["onClick"])),[[F,["bpm:task-assign-rule:query"]]])]),_:1})]),_:1},8,["data"])),[[M,e(g)]]),l(z,{total:e(x),page:e(u).pageNo,"onUpdate:page":r[0]||(r[0]=a=>e(u).pageNo=a),limit:e(u).pageSize,"onUpdate:limit":r[1]||(r[1]=a=>e(u).pageSize=a),onPagination:T},null,8,["total","page","limit"])]),_:1}),l(S,{title:"\u8868\u5355\u8BE6\u60C5",modelValue:e(c),"onUpdate:modelValue":r[2]||(r[2]=a=>b(c)?c.value=a:null),width:"800"},{default:t(()=>[l(O,{rule:e(k).rule,option:e(k).option},null,8,["rule","option"])]),_:1},8,["modelValue"]),l(S,{title:"\u6D41\u7A0B\u56FE",modelValue:e(d),"onUpdate:modelValue":r[4]||(r[4]=a=>b(d)?d.value=a:null),width:"800"},{default:t(()=>[l(e(ya),W({key:"designer",modelValue:e(p),"onUpdate:modelValue":r[3]||(r[3]=a=>b(p)?p.value=a:null),value:e(p)},e(N),{prefix:e(N).prefix}),null,16,["modelValue","value","prefix"])]),_:1},8,["modelValue"])],64)}}})});export{Na as __tla,B as default};
