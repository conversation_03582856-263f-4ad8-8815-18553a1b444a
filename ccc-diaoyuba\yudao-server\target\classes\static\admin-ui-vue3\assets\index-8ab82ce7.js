import{_ as I,__tla as M}from"./Dialog.vue_vue_type_style_index_0_lang-5af0bc2b.js";import{d as O,u as R,N as j,r as _,f as z,A as H,o as p,c as b,i as e,w as t,a as l,j as n,F as g,k as L,x as P,l as G,_ as J,H as K,T as Q,D as W,af as X,I as Y,q as Z,t as $,ag as aa,__tla as ea}from"./index-97fffa0c.js";import{_ as la,__tla as ta}from"./ContentWrap.vue_vue_type_script_setup_true_lang-a574ccef.js";import{a as ra,D as sa,__tla as ua}from"./dict-6a82eb12.js";import{C as oa}from"./constants-3933cd3a.js";import{g as _a,c as ma,u as na,__tla as da}from"./index-472475f8.js";import{F as ca,__tla as ia}from"./index.es-40f43cbb.js";import{s as fa,e as pa,a as ya}from"./formCreate-a3356cdc.js";import{u as ha,__tla as Va}from"./tagsView-c5b6677c.js";import{u as ba,__tla as ga}from"./useMessage-18385d4a.js";import{__tla as va}from"./el-card-6c7c099d.js";let v,ka=Promise.all([(()=>{try{return M}catch{}})(),(()=>{try{return ea}catch{}})(),(()=>{try{return ta}catch{}})(),(()=>{try{return ua}catch{}})(),(()=>{try{return da}catch{}})(),(()=>{try{return ia}catch{}})(),(()=>{try{return Va}catch{}})(),(()=>{try{return ga}catch{}})(),(()=>{try{return va}catch{}})()]).then(async()=>{v=O({name:"BpmFormEditor",__name:"index",setup(wa){const{t:y}=G(),h=ba(),{push:k,currentRoute:w}=R(),{query:x}=j(),{delView:C}=ha(),m=_(),o=_(!1),d=_(!1),s=_({name:"",status:oa.ENABLE,remark:""}),S=z({name:[{required:!0,message:"\u8868\u5355\u540D\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],status:[{required:!0,message:"\u5F00\u542F\u72B6\u6001\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),c=_(),U=()=>{o.value=!0},q=async()=>{if(c&&await c.value.validate()){d.value=!0;try{const u=s.value;u.conf=pa(m),u.fields=ya(m),u.id?(await na(u),h.success(y("common.updateSuccess"))):(await ma(u),h.success(y("common.createSuccess"))),o.value=!1,A()}finally{d.value=!1}}},A=()=>{C(l(w)),k("/bpm/manager/form")};return H(async()=>{const u=x.id;if(!u)return;const a=await _a(u);s.value=a,fa(m,a.conf,a.fields)}),(u,a)=>{const N=J,i=K,D=la,V=Q,f=W,E=aa,F=X,T=Y,B=I;return p(),b(g,null,[e(D,null,{default:t(()=>[e(l(ca),{ref_key:"designer",ref:m,height:"780px"},{handle:t(()=>[e(i,{round:"",size:"small",type:"primary",onClick:U},{default:t(()=>[e(N,{class:"mr-5px",icon:"ep:plus"}),n(" \u4FDD\u5B58 ")]),_:1})]),_:1},512)]),_:1}),e(B,{modelValue:l(o),"onUpdate:modelValue":a[4]||(a[4]=r=>P(o)?o.value=r:null),title:"\u4FDD\u5B58\u8868\u5355",width:"600"},{footer:t(()=>[e(i,{disabled:l(d),type:"primary",onClick:q},{default:t(()=>[n("\u786E \u5B9A")]),_:1},8,["disabled"]),e(i,{onClick:a[3]||(a[3]=r=>o.value=!1)},{default:t(()=>[n("\u53D6 \u6D88")]),_:1})]),default:t(()=>[e(T,{ref_key:"formRef",ref:c,model:l(s),rules:l(S),"label-width":"80px"},{default:t(()=>[e(f,{label:"\u8868\u5355\u540D",prop:"name"},{default:t(()=>[e(V,{modelValue:l(s).name,"onUpdate:modelValue":a[0]||(a[0]=r=>l(s).name=r),placeholder:"\u8BF7\u8F93\u5165\u8868\u5355\u540D"},null,8,["modelValue"])]),_:1}),e(f,{label:"\u72B6\u6001",prop:"status"},{default:t(()=>[e(F,{modelValue:l(s).status,"onUpdate:modelValue":a[1]||(a[1]=r=>l(s).status=r)},{default:t(()=>[(p(!0),b(g,null,L(l(ra)(l(sa).COMMON_STATUS),r=>(p(),Z(E,{key:r.value,label:r.value},{default:t(()=>[n($(r.label),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1}),e(f,{label:"\u5907\u6CE8",prop:"remark"},{default:t(()=>[e(V,{modelValue:l(s).remark,"onUpdate:modelValue":a[2]||(a[2]=r=>l(s).remark=r),placeholder:"\u8BF7\u8F93\u5165\u5907\u6CE8",type:"textarea"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue"])],64)}}})});export{ka as __tla,v as default};
