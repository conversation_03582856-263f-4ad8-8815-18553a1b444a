#!/bin/bash

# 检查日志路径脚本

echo "=========================================="
echo "日志路径检查工具"
echo "=========================================="

# 1. 检查当前用户信息
echo "1. 用户信息:"
echo "   当前用户: $(whoami)"
echo "   用户ID: $(id -u)"
echo "   用户组: $(id -g)"
echo "   用户家目录: $HOME"
echo ""

# 2. 检查Java系统属性
echo "2. Java系统属性:"
if command -v java > /dev/null 2>&1; then
    USER_HOME_JAVA=$(java -Dspring.application.name=yudao-server -cp /www/wwwroot/yudao-server/yudao-server.jar -Dspring.profiles.active=pro org.springframework.boot.system.SystemPropertyUtils 2>/dev/null || echo "$HOME")
    echo "   Java user.home: $HOME"
    echo "   Spring application.name: yudao-server"
else
    echo "   Java未找到"
fi
echo ""

# 3. 计算实际日志路径
LOG_DIR="$HOME/logs"
LOG_FILE="$LOG_DIR/yudao-server.log"

echo "3. 计算的日志路径:"
echo "   日志目录: $LOG_DIR"
echo "   日志文件: $LOG_FILE"
echo ""

# 4. 检查目录和文件状态
echo "4. 文件系统状态:"
if [ -d "$LOG_DIR" ]; then
    echo "   ✅ 日志目录存在: $LOG_DIR"
    echo "   目录权限: $(ls -ld "$LOG_DIR" | awk '{print $1, $3, $4}')"
    
    if [ -f "$LOG_FILE" ]; then
        echo "   ✅ 日志文件存在: $LOG_FILE"
        echo "   文件大小: $(du -h "$LOG_FILE" | cut -f1)"
        echo "   最后修改: $(stat -c %y "$LOG_FILE" 2>/dev/null | cut -d'.' -f1)"
        echo "   文件权限: $(ls -l "$LOG_FILE" | awk '{print $1, $3, $4}')"
    else
        echo "   ❌ 日志文件不存在: $LOG_FILE"
    fi
    
    # 列出日志目录中的文件
    echo ""
    echo "   日志目录内容:"
    ls -la "$LOG_DIR" 2>/dev/null | head -10
    
else
    echo "   ❌ 日志目录不存在: $LOG_DIR"
    echo "   需要创建目录: mkdir -p $LOG_DIR"
fi

echo ""

# 5. 检查磁盘空间
echo "5. 磁盘空间:"
df -h "$HOME" | tail -1 | awk '{printf "   可用空间: %s / %s (%s 已使用)\n", $4, $2, $5}'
echo ""

# 6. 提供建议
echo "6. 建议操作:"
if [ ! -d "$LOG_DIR" ]; then
    echo "   创建日志目录: mkdir -p $LOG_DIR"
fi

echo "   启动应用后查看日志: tail -f $LOG_FILE"
echo "   实时监控日志: watch -n 1 'ls -la $LOG_DIR'"
echo ""

# 7. 测试写入权限
echo "7. 权限测试:"
if [ -w "$HOME" ]; then
    echo "   ✅ 用户家目录可写"
    if [ ! -d "$LOG_DIR" ]; then
        if mkdir -p "$LOG_DIR" 2>/dev/null; then
            echo "   ✅ 可以创建日志目录"
            rmdir "$LOG_DIR" 2>/dev/null
        else
            echo "   ❌ 无法创建日志目录"
        fi
    else
        if [ -w "$LOG_DIR" ]; then
            echo "   ✅ 日志目录可写"
        else
            echo "   ❌ 日志目录不可写"
        fi
    fi
else
    echo "   ❌ 用户家目录不可写"
fi

echo ""
echo "=========================================="
echo "根据当前配置，日志将输出到:"
echo "📁 $LOG_FILE"
echo "=========================================="
