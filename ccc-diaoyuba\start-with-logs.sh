#!/bin/bash

# 钓鱼吧应用启动脚本
# 确保日志目录存在并启动应用

APP_NAME="diaoyuba"
LOG_DIR="/log"
JAR_FILE="/www/wwwroot/yudao-server/yudao-server.jar"
JAVA_HOME="/usr/local/btjdk/jdk8"
JAVA_BIN="$JAVA_HOME/bin/java"
PROFILE="pro"
SERVER_PORT="48080"

echo "=========================================="
echo "启动 $APP_NAME 应用"
echo "=========================================="

# 检查并创建日志目录
echo "检查日志目录: $LOG_DIR"
if [ ! -d "$LOG_DIR" ]; then
    echo "创建日志目录: $LOG_DIR"
    sudo mkdir -p "$LOG_DIR"
    sudo chmod 755 "$LOG_DIR"
    echo "✅ 日志目录创建成功"
else
    echo "✅ 日志目录已存在"
fi

# 设置日志目录权限
echo "设置日志目录权限..."
sudo chown -R $(whoami):$(whoami) "$LOG_DIR" 2>/dev/null || echo "⚠️ 无法更改目录所有者，请确保有写入权限"
sudo chmod -R 755 "$LOG_DIR"

# 检查 JAR 文件是否存在
if [ ! -f "$JAR_FILE" ]; then
    echo "❌ JAR 文件不存在: $JAR_FILE"
    echo "请先执行 mvn clean package 构建项目"
    exit 1
fi

echo "✅ JAR 文件存在: $JAR_FILE"

# 检查是否已有进程在运行
PID=$(ps aux | grep "$JAR_FILE" | grep -v grep | awk '{print $2}')
if [ ! -z "$PID" ]; then
    echo "⚠️ 发现已运行的进程 PID: $PID"
    read -p "是否停止现有进程并重新启动? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo "停止进程 $PID..."
        kill -9 $PID
        sleep 2
    else
        echo "取消启动"
        exit 0
    fi
fi

# 启动应用
echo "启动应用..."
echo "配置文件: $PROFILE"
echo "日志目录: $LOG_DIR"
echo "日志文件: $LOG_DIR/diaoyuba.log"
echo "错误日志: $LOG_DIR/diaoyuba-error.log"
echo ""

# 使用 nohup 后台启动
nohup $JAVA_BIN -jar \
    -Xmx1024M \
    -Xms256M \
    -Dlogging.file.name=$LOG_DIR/diaoyuba.log \
    -Dlogging.file.path=$LOG_DIR \
    -XX:+UseG1GC \
    -XX:+PrintGCDetails \
    -XX:+PrintGCTimeStamps \
    -XX:+PrintGCApplicationStoppedTime \
    -Xloggc:$LOG_DIR/gc.log \
    "$JAR_FILE" \
    --server.port=$SERVER_PORT \
    --spring.profiles.active=$PROFILE > /dev/null 2>&1 &

# 获取新进程 PID
NEW_PID=$!
echo "🚀 应用已启动，PID: $NEW_PID"

# 等待几秒钟检查启动状态
echo "等待应用启动..."
sleep 5

# 检查进程是否还在运行
if ps -p $NEW_PID > /dev/null; then
    echo "✅ 应用启动成功！"
    echo ""
    echo "📋 应用信息:"
    echo "  PID: $NEW_PID"
    echo "  配置: $PROFILE"
    echo "  日志: $LOG_DIR/diaoyuba.log"
    echo "  错误日志: $LOG_DIR/diaoyuba-error.log"
    echo "  GC日志: $LOG_DIR/gc.log"
    echo ""
    echo "📖 查看日志命令:"
    echo "  实时日志: tail -f $LOG_DIR/diaoyuba.log"
    echo "  错误日志: tail -f $LOG_DIR/diaoyuba-error.log"
    echo "  查看最近100行: tail -n 100 $LOG_DIR/diaoyuba.log"
    echo ""
    echo "🛑 停止应用命令:"
    echo "  kill -9 $NEW_PID"
else
    echo "❌ 应用启动失败！"
    echo "请检查日志文件: $LOG_DIR/diaoyuba.log"
    exit 1
fi
