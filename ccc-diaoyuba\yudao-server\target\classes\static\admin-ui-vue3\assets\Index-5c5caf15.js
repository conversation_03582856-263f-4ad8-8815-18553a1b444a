import{d as de,p as ue,b as ke,h as me,a,e as be,r as je,f as _,o as f,c as b,g as s,i as e,w as t,t as l,j as A,F as D,k as G,l as Ae,E as De,m as Ee,n as $e,q as pe,s as xe,v as H,_ as Me,__tla as Oe}from"./index-97fffa0c.js";import{_ as Ve,__tla as Ce}from"./Echart.vue_vue_type_script_setup_true_lang-470d3b7c.js";import{E as Re,__tla as Ue}from"./el-link-f00f9c89.js";import{E as We,__tla as Ge}from"./el-card-6c7c099d.js";import{E as He,__tla as Pe}from"./el-skeleton-item-942999da.js";import{_ as qe,__tla as ze}from"./CountTo.vue_vue_type_script_setup_true_lang-9925bfe0.js";import{u as Fe,a as Ie}from"./avatar-f3058573.js";import{p as Qe,b as Be,__tla as Je}from"./echarts-data-bb49321b.js";let ye,Ke=Promise.all([(()=>{try{return Oe}catch{}})(),(()=>{try{return Ce}catch{}})(),(()=>{try{return Ue}catch{}})(),(()=>{try{return Ge}catch{}})(),(()=>{try{return Pe}catch{}})(),(()=>{try{return ze}catch{}})(),(()=>{try{return Je}catch{}})()]).then(async()=>{let P,q,z,F,I,Q,B,J,K,L,N,S,T,X,Y,Z,ee,ae,te,se,le,ne,ie,re;P=de({name:"Highlight",props:{tag:ue.string.def("span"),keys:{type:Array,default:()=>[]},color:ue.string.def("var(--el-color-primary)")},emits:["click"],setup(h,{emit:n,slots:r}){const E=ke(()=>h.keys.map(u=>me("span",{onClick:()=>{n("click",u)},style:{color:h.color,cursor:"pointer"}},u))),o=()=>{if(!(r!=null&&r.default))return null;const u=r==null?void 0:r.default()[0].children;if(!u)return r==null?void 0:r.default()[0];const $=(m=u,h.keys.forEach((c,O)=>{const w=new RegExp(c,"g");m=m.replace(w,`{{${O}}}`)}),m.split(/{{|}}/));var m;const g=/^[0-9]*$/,M=$.map(c=>g.test(c)&&a(E)[c]||c);return me(h.tag,M)};return()=>o()}}),q={class:"flex items-center"},z=["src"],F={class:"text-20px"},I={class:"mt-10px text-14px text-gray-500"},Q={class:"h-70px flex items-center justify-end lt-sm:mt-10px"},B={class:"px-8px text-right"},J={class:"mb-20px text-14px text-gray-400"},K={class:"px-8px text-right"},L={class:"mb-20px text-14px text-gray-400"},N={class:"px-8px text-right"},S={class:"mb-20px text-14px text-gray-400"},T={class:"h-3 flex justify-between"},X={class:"flex items-center"},Y={class:"text-16px"},Z={class:"mt-15px text-14px text-gray-400"},ee={class:"mt-20px flex justify-between text-12px text-gray-400"},ae={class:"h-3 flex justify-between"},te={class:"flex items-center"},se={class:"h-3 flex justify-between"},le={class:"flex items-center"},ne=["src"],ie={class:"text-14px"},re={class:"mt-15px text-12px text-gray-400"},ye=de({name:"Home",__name:"Index",setup(h){const{t:n}=Ae(),r=be(),{setWatermark:E}=Fe(),o=je(!0),u=r.getUser.avatar?r.getUser.avatar:Ie,$=r.getUser.nickname,m=_(Qe);let g=_({project:0,access:0,todo:0});const M=async()=>{g=Object.assign(g,{project:40,access:2340,todo:10})};let c=_([]);const O=async()=>{c=Object.assign(c,[{name:"Github",icon:"akar-icons:github-fill",message:"workplace.introduction",personal:"Archer",time:new Date},{name:"Vue",icon:"logos:vue",message:"workplace.introduction",personal:"Archer",time:new Date},{name:"Angular",icon:"logos:angular-icon",message:"workplace.introduction",personal:"Archer",time:new Date},{name:"React",icon:"logos:react",message:"workplace.introduction",personal:"Archer",time:new Date},{name:"Webpack",icon:"logos:webpack",message:"workplace.introduction",personal:"Archer",time:new Date},{name:"Vite",icon:"vscode-icons:file-type-vite",message:"workplace.introduction",personal:"Archer",time:new Date}])};let w=_([]);const ge=async()=>{w=Object.assign(w,[{title:"\u7CFB\u7EDF\u5347\u7EA7\u7248\u672C",type:"\u901A\u77E5",keys:["\u901A\u77E5","\u5347\u7EA7"],date:new Date},{title:"\u7CFB\u7EDF\u51CC\u6668\u7EF4\u62A4",type:"\u516C\u544A",keys:["\u516C\u544A","\u7EF4\u62A4"],date:new Date},{title:"\u7CFB\u7EDF\u5347\u7EA7\u7248\u672C",type:"\u901A\u77E5",keys:["\u901A\u77E5","\u5347\u7EA7"],date:new Date},{title:"\u7CFB\u7EDF\u51CC\u6668\u7EF4\u62A4",type:"\u516C\u544A",keys:["\u516C\u544A","\u7EF4\u62A4"],date:new Date}])};let V=_([]);const ve=async()=>{V=Object.assign(V,[{name:"Github",icon:"akar-icons:github-fill",url:"github.io"},{name:"Vue",icon:"logos:vue",url:"vuejs.org"},{name:"Vite",icon:"vscode-icons:file-type-vite",url:"https://vitejs.dev/"},{name:"Angular",icon:"logos:angular-icon",url:"github.io"},{name:"React",icon:"logos:react",url:"github.io"},{name:"Webpack",icon:"logos:webpack",url:"github.io"}])},fe=async()=>{const p=[{value:335,name:"analysis.directAccess"},{value:310,name:"analysis.mailMarketing"},{value:234,name:"analysis.allianceAdvertising"},{value:135,name:"analysis.videoAdvertising"},{value:1548,name:"analysis.searchEngines"}];H(m,"legend.data",p.map(d=>n(d.name))),m.series[0].data=p.map(d=>({name:n(d.name),value:d.value}))},C=_(Be),_e=async()=>{const p=[{value:13253,name:"analysis.monday"},{value:34235,name:"analysis.tuesday"},{value:26321,name:"analysis.wednesday"},{value:12340,name:"analysis.thursday"},{value:24643,name:"analysis.friday"},{value:1322,name:"analysis.saturday"},{value:1324,name:"analysis.sunday"}];H(C,"xAxis.data",p.map(d=>n(d.name))),H(C,"series",[{name:n("analysis.activeQuantity"),data:p.map(d=>d.value),type:"bar"}])};return(async()=>(await Promise.all([M(),O(),ge(),ve(),fe(),_e()]),o.value=!1))(),(p,d)=>{const x=De,R=qe,U=Ee,k=$e,v=He,y=We,W=Re,oe=Me,ce=Ve,he=P;return f(),b(D,null,[s("div",null,[e(y,{shadow:"never"},{default:t(()=>[e(v,{loading:a(o),animated:""},{default:t(()=>[e(k,{gutter:20,justify:"space-between"},{default:t(()=>[e(x,{xl:12,lg:12,md:12,sm:24,xs:24},{default:t(()=>[s("div",q,[s("img",{src:a(u),alt:"",class:"mr-20px h-70px w-70px rounded-[50%]"},null,8,z),s("div",null,[s("div",F,l(a(n)("workplace.welcome"))+" "+l(a($))+" "+l(a(n)("workplace.happyDay")),1),s("div",I,l(a(n)("workplace.toady"))+"\uFF0C20\u2103 - 32\u2103\uFF01 ",1)])])]),_:1}),e(x,{xl:12,lg:12,md:12,sm:24,xs:24},{default:t(()=>[s("div",Q,[s("div",B,[s("div",J,l(a(n)("workplace.project")),1),e(R,{class:"text-20px","start-val":0,"end-val":a(g).project,duration:2600},null,8,["end-val"])]),e(U,{direction:"vertical"}),s("div",K,[s("div",L,l(a(n)("workplace.toDo")),1),e(R,{class:"text-20px","start-val":0,"end-val":a(g).todo,duration:2600},null,8,["end-val"])]),e(U,{direction:"vertical","border-style":"dashed"}),s("div",N,[s("div",S,l(a(n)("workplace.access")),1),e(R,{class:"text-20px","start-val":0,"end-val":a(g).access,duration:2600},null,8,["end-val"])])])]),_:1})]),_:1})]),_:1},8,["loading"])]),_:1})]),e(k,{class:"mt-5px",gutter:20,justify:"space-between"},{default:t(()=>[e(x,{xl:16,lg:16,md:24,sm:24,xs:24,class:"mb-10px"},{default:t(()=>[e(y,{shadow:"never"},{header:t(()=>[s("div",T,[s("span",null,l(a(n)("workplace.project")),1),e(W,{type:"primary",underline:!1},{default:t(()=>[A(l(a(n)("action.more")),1)]),_:1})])]),default:t(()=>[e(v,{loading:a(o),animated:""},{default:t(()=>[e(k,null,{default:t(()=>[(f(!0),b(D,null,G(a(c),(i,j)=>(f(),pe(x,{key:`card-${j}`,xl:8,lg:8,md:8,sm:24,xs:24},{default:t(()=>[e(y,{shadow:"hover"},{default:t(()=>[s("div",X,[e(oe,{icon:i.icon,size:25,class:"mr-10px"},null,8,["icon"]),s("span",Y,l(i.name),1)]),s("div",Z,l(a(n)(i.message)),1),s("div",ee,[s("span",null,l(i.personal),1),s("span",null,l(a(xe)(i.time,"yyyy-MM-dd")),1)])]),_:2},1024)]),_:2},1024))),128))]),_:1})]),_:1},8,["loading"])]),_:1}),e(y,{shadow:"never",class:"mt-5px"},{default:t(()=>[e(v,{loading:a(o),animated:""},{default:t(()=>[e(k,{gutter:20,justify:"space-between"},{default:t(()=>[e(x,{xl:10,lg:10,md:24,sm:24,xs:24},{default:t(()=>[e(y,{shadow:"hover",class:"mb-10px"},{default:t(()=>[e(v,{loading:a(o),animated:""},{default:t(()=>[e(ce,{options:a(m),height:280},null,8,["options"])]),_:1},8,["loading"])]),_:1})]),_:1}),e(x,{xl:14,lg:14,md:24,sm:24,xs:24},{default:t(()=>[e(y,{shadow:"hover",class:"mb-10px"},{default:t(()=>[e(v,{loading:a(o),animated:""},{default:t(()=>[e(ce,{options:a(C),height:280},null,8,["options"])]),_:1},8,["loading"])]),_:1})]),_:1})]),_:1})]),_:1},8,["loading"])]),_:1})]),_:1}),e(x,{xl:8,lg:8,md:24,sm:24,xs:24,class:"mb-10px"},{default:t(()=>[e(y,{shadow:"never"},{header:t(()=>[s("div",ae,[s("span",null,l(a(n)("workplace.shortcutOperation")),1)])]),default:t(()=>[e(v,{loading:a(o),animated:""},{default:t(()=>[e(k,null,{default:t(()=>[(f(!0),b(D,null,G(a(V),i=>(f(),pe(x,{key:`team-${i.name}`,span:8,class:"mb-10px"},{default:t(()=>[s("div",te,[e(oe,{icon:i.icon,class:"mr-10px"},null,8,["icon"]),e(W,{type:"default",underline:!1,onClick:j=>a(E)(i.name)},{default:t(()=>[A(l(i.name),1)]),_:2},1032,["onClick"])])]),_:2},1024))),128))]),_:1})]),_:1},8,["loading"])]),_:1}),e(y,{shadow:"never",class:"mt-10px"},{header:t(()=>[s("div",se,[s("span",null,l(a(n)("workplace.notice")),1),e(W,{type:"primary",underline:!1},{default:t(()=>[A(l(a(n)("action.more")),1)]),_:1})])]),default:t(()=>[e(v,{loading:a(o),animated:""},{default:t(()=>[(f(!0),b(D,null,G(a(w),(i,j)=>(f(),b("div",{key:`dynamics-${j}`},[s("div",le,[s("img",{src:a(u),alt:"",class:"mr-20px h-35px w-35px rounded-[50%]"},null,8,ne),s("div",null,[s("div",ie,[e(he,{keys:i.keys.map(we=>a(n)(we))},{default:t(()=>[A(l(i.type)+" : "+l(i.title),1)]),_:2},1032,["keys"])]),s("div",re,l(a(xe)(i.date,"yyyy-MM-dd")),1)])]),e(U)]))),128))]),_:1},8,["loading"])]),_:1})]),_:1})]),_:1})],64)}}})});export{Ke as __tla,ye as default};
