<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.yudao.module.diaoyuba.dal.mysql.waterstation.WaterStationMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="selectPageDistance"
            resultType="cn.iocoder.yudao.module.diaoyuba.dal.dataobject.waterstation.WaterStationDO">
        SELECT
            *,
            6371 * ACOS(COS(RADIANS(#{pageReqVO.latitude})) * COS(RADIANS(latitude)) * COS(RADIANS(longitude) - RADIANS(#{pageReqVO.longitude})) + SIN(RADIANS(#{pageReqVO.latitude})) * SIN(RADIANS(latitude))) AS distance
        FROM
            ccc_water_station
        WHERE
            deleted = 0
        <if test="pageReqVO.searchKeyword != null and pageReqVO.searchKeyword != ''">
            AND address LIKE CONCAT('%', #{pageReqVO.searchKeyword}, '%')
        </if>
        ORDER BY
            distance

    </select>
    <select id="selectListByUserView"
            resultType="cn.iocoder.yudao.module.diaoyuba.dal.dataobject.waterstation.WaterStationDO">
        SELECT
            ws.*  from ccc_water_station ws LEFT JOIN
                       (
                           SELECT
                               station_id,
                               create_time,
                               ROW_NUMBER() OVER (PARTITION BY station_id ORDER BY create_time DESC) AS rn
                           FROM
                               ccc_water_station_user where user_id = #{userId}
                       ) t1 on ws.id = t1.station_id
        WHERE
            t1.rn = 1
        ORDER BY
            t1.create_time DESC
        limit 5

    </select>
</mapper>
