import{_ as p,__tla as u}from"./ImageViewer.vue_vue_type_script_setup_true_lang-cdbc76a9.js";import{dw as m,i as h,dx as x,__tla as f}from"./index-97fffa0c.js";let i,y=Promise.all([(()=>{try{return u}catch{}})(),(()=>{try{return f}catch{}})()]).then(async()=>{let e=null;i=function(a){if(!m)return;const{urlList:l,initialIndex:d=0,infinite:o=!0,hideOnClickModal:r=!1,appendToBody:s=!1,zIndex:c=2e3,show:_=!0}=a,t={},n=document.createElement("div");t.urlList=l,t.initialIndex=d,t.infinite=o,t.hideOnClickModal=r,t.appendToBody=s,t.zIndex=c,t.show=_,document.body.appendChild(n),e=h(p,t),x(e,n)}});export{y as __tla,i as c};
