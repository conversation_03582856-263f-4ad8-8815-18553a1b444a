package cn.iocoder.yudao.module.diaoyuba.convert.team;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.diaoyuba.controller.app.team.vo.message.AppTeamMessageCreateReqVO;
import cn.iocoder.yudao.module.diaoyuba.controller.app.team.vo.message.AppTeamMessageRespVO;
import cn.iocoder.yudao.module.diaoyuba.dal.dataobject.team.TeamMessageDO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-21T09:34:05+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_181 (Oracle Corporation)"
)
public class TeamMessageConvertImpl implements TeamMessageConvert {

    @Override
    public TeamMessageDO convert(AppTeamMessageCreateReqVO bean) {
        if ( bean == null ) {
            return null;
        }

        TeamMessageDO.TeamMessageDOBuilder teamMessageDO = TeamMessageDO.builder();

        teamMessageDO.extra( mapStringToMap( bean.getExtra() ) );
        teamMessageDO.teamId( bean.getTeamId() );
        teamMessageDO.messageType( bean.getMessageType() );
        teamMessageDO.content( bean.getContent() );
        teamMessageDO.replyToId( bean.getReplyToId() );

        return teamMessageDO.build();
    }

    @Override
    public AppTeamMessageRespVO convert(TeamMessageDO bean) {
        if ( bean == null ) {
            return null;
        }

        AppTeamMessageRespVO appTeamMessageRespVO = new AppTeamMessageRespVO();

        appTeamMessageRespVO.setExtra( mapMapToString( bean.getExtra() ) );
        appTeamMessageRespVO.setId( bean.getId() );
        appTeamMessageRespVO.setTeamId( bean.getTeamId() );
        appTeamMessageRespVO.setUserId( bean.getUserId() );
        appTeamMessageRespVO.setMessageType( bean.getMessageType() );
        appTeamMessageRespVO.setContent( bean.getContent() );
        appTeamMessageRespVO.setReplyToId( bean.getReplyToId() );
        appTeamMessageRespVO.setReadCount( bean.getReadCount() );
        appTeamMessageRespVO.setCreateTime( bean.getCreateTime() );

        return appTeamMessageRespVO;
    }

    @Override
    public List<AppTeamMessageRespVO> convertList(List<TeamMessageDO> list) {
        if ( list == null ) {
            return null;
        }

        List<AppTeamMessageRespVO> list1 = new ArrayList<AppTeamMessageRespVO>( list.size() );
        for ( TeamMessageDO teamMessageDO : list ) {
            list1.add( convert( teamMessageDO ) );
        }

        return list1;
    }

    @Override
    public PageResult<AppTeamMessageRespVO> convertPage(PageResult<TeamMessageDO> page) {
        if ( page == null ) {
            return null;
        }

        PageResult<AppTeamMessageRespVO> pageResult = new PageResult<AppTeamMessageRespVO>();

        pageResult.setList( convertList( page.getList() ) );
        pageResult.setTotal( page.getTotal() );

        return pageResult;
    }
}
