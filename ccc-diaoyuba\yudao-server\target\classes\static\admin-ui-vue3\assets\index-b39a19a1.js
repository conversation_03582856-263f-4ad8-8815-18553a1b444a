import{d as n,p as a,o as l,q as c,w as i,cB as _,g as d,t as p,__tla as u}from"./index-97fffa0c.js";import{_ as m}from"./_plugin-vue_export-helper-1b428a4d.js";let s,w=Promise.all([(()=>{try{return u}catch{}})()]).then(async()=>{s=m(n({name:"DocAlert",__name:"index",props:{title:a.string,url:a.string},setup(t){const e=t,r=()=>{window.open(e.url)};return(y,g)=>{const o=_;return l(),c(o,{key:0,type:"success","show-icon":""},{title:i(()=>[d("div",{onClick:r},p("\u3010"+t.title+"\u3011\u6587\u6863\u5730\u5740\uFF1A"+t.url),1)]),_:1})}}}),[["__scopeId","data-v-ddd442d3"]])});export{s as _,w as __tla};
