package cn.iocoder.yudao.module.diaoyuba.convert.waterstationflow;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.diaoyuba.controller.app.waterstationflow.vo.AppWaterStationFlowCreateReqVO;
import cn.iocoder.yudao.module.diaoyuba.controller.app.waterstationflow.vo.AppWaterStationFlowExcelVO;
import cn.iocoder.yudao.module.diaoyuba.controller.app.waterstationflow.vo.AppWaterStationFlowRespVO;
import cn.iocoder.yudao.module.diaoyuba.controller.app.waterstationflow.vo.AppWaterStationFlowUpdateReqVO;
import cn.iocoder.yudao.module.diaoyuba.dal.dataobject.waterstationflow.WaterStationFlowDO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-21T09:34:05+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_181 (Oracle Corporation)"
)
public class WaterStationFlowConvertImpl implements WaterStationFlowConvert {

    @Override
    public WaterStationFlowDO convert(AppWaterStationFlowCreateReqVO bean) {
        if ( bean == null ) {
            return null;
        }

        WaterStationFlowDO.WaterStationFlowDOBuilder waterStationFlowDO = WaterStationFlowDO.builder();

        waterStationFlowDO.stationId( bean.getStationId() );
        waterStationFlowDO.waterLevel( bean.getWaterLevel() );
        waterStationFlowDO.waterFlow( bean.getWaterFlow() );
        waterStationFlowDO.waterUpdateTime( bean.getWaterUpdateTime() );

        return waterStationFlowDO.build();
    }

    @Override
    public WaterStationFlowDO convert(AppWaterStationFlowUpdateReqVO bean) {
        if ( bean == null ) {
            return null;
        }

        WaterStationFlowDO.WaterStationFlowDOBuilder waterStationFlowDO = WaterStationFlowDO.builder();

        waterStationFlowDO.id( bean.getId() );
        waterStationFlowDO.stationId( bean.getStationId() );
        waterStationFlowDO.waterLevel( bean.getWaterLevel() );
        waterStationFlowDO.waterFlow( bean.getWaterFlow() );
        waterStationFlowDO.waterUpdateTime( bean.getWaterUpdateTime() );

        return waterStationFlowDO.build();
    }

    @Override
    public AppWaterStationFlowRespVO convert(WaterStationFlowDO bean) {
        if ( bean == null ) {
            return null;
        }

        AppWaterStationFlowRespVO appWaterStationFlowRespVO = new AppWaterStationFlowRespVO();

        appWaterStationFlowRespVO.setStationId( bean.getStationId() );
        appWaterStationFlowRespVO.setWaterLevel( bean.getWaterLevel() );
        appWaterStationFlowRespVO.setWaterFlow( bean.getWaterFlow() );
        appWaterStationFlowRespVO.setWaterUpdateTime( bean.getWaterUpdateTime() );
        appWaterStationFlowRespVO.setId( bean.getId() );
        appWaterStationFlowRespVO.setCreateTime( bean.getCreateTime() );

        return appWaterStationFlowRespVO;
    }

    @Override
    public List<AppWaterStationFlowRespVO> convertList(List<WaterStationFlowDO> list) {
        if ( list == null ) {
            return null;
        }

        List<AppWaterStationFlowRespVO> list1 = new ArrayList<AppWaterStationFlowRespVO>( list.size() );
        for ( WaterStationFlowDO waterStationFlowDO : list ) {
            list1.add( convert( waterStationFlowDO ) );
        }

        return list1;
    }

    @Override
    public PageResult<AppWaterStationFlowRespVO> convertPage(PageResult<WaterStationFlowDO> page) {
        if ( page == null ) {
            return null;
        }

        PageResult<AppWaterStationFlowRespVO> pageResult = new PageResult<AppWaterStationFlowRespVO>();

        pageResult.setList( convertList( page.getList() ) );
        pageResult.setTotal( page.getTotal() );

        return pageResult;
    }

    @Override
    public List<AppWaterStationFlowExcelVO> convertList02(List<WaterStationFlowDO> list) {
        if ( list == null ) {
            return null;
        }

        List<AppWaterStationFlowExcelVO> list1 = new ArrayList<AppWaterStationFlowExcelVO>( list.size() );
        for ( WaterStationFlowDO waterStationFlowDO : list ) {
            list1.add( waterStationFlowDOToAppWaterStationFlowExcelVO( waterStationFlowDO ) );
        }

        return list1;
    }

    protected AppWaterStationFlowExcelVO waterStationFlowDOToAppWaterStationFlowExcelVO(WaterStationFlowDO waterStationFlowDO) {
        if ( waterStationFlowDO == null ) {
            return null;
        }

        AppWaterStationFlowExcelVO appWaterStationFlowExcelVO = new AppWaterStationFlowExcelVO();

        appWaterStationFlowExcelVO.setId( waterStationFlowDO.getId() );
        appWaterStationFlowExcelVO.setStationId( waterStationFlowDO.getStationId() );
        appWaterStationFlowExcelVO.setWaterLevel( waterStationFlowDO.getWaterLevel() );
        appWaterStationFlowExcelVO.setWaterFlow( waterStationFlowDO.getWaterFlow() );
        appWaterStationFlowExcelVO.setWaterUpdateTime( waterStationFlowDO.getWaterUpdateTime() );
        appWaterStationFlowExcelVO.setCreateTime( waterStationFlowDO.getCreateTime() );

        return appWaterStationFlowExcelVO;
    }
}
