package cn.iocoder.yudao.module.diaoyuba.convert.note;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.diaoyuba.controller.app.note.vo.AppNoteCreateReqVO;
import cn.iocoder.yudao.module.diaoyuba.controller.app.note.vo.AppNoteExcelVO;
import cn.iocoder.yudao.module.diaoyuba.controller.app.note.vo.AppNoteInfoRespVO;
import cn.iocoder.yudao.module.diaoyuba.controller.app.note.vo.AppNoteRespVO;
import cn.iocoder.yudao.module.diaoyuba.controller.app.note.vo.AppNoteUpdateReqVO;
import cn.iocoder.yudao.module.diaoyuba.dal.dataobject.note.NoteDO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-21T09:34:05+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_181 (Oracle Corporation)"
)
public class NoteConvertImpl implements NoteConvert {

    @Override
    public NoteDO convert(AppNoteCreateReqVO bean) {
        if ( bean == null ) {
            return null;
        }

        NoteDO.NoteDOBuilder noteDO = NoteDO.builder();

        noteDO.positionId( bean.getPositionId() );
        noteDO.title( bean.getTitle() );
        noteDO.content( bean.getContent() );

        return noteDO.build();
    }

    @Override
    public NoteDO convert(AppNoteUpdateReqVO bean) {
        if ( bean == null ) {
            return null;
        }

        NoteDO.NoteDOBuilder noteDO = NoteDO.builder();

        noteDO.id( bean.getId() );
        noteDO.title( bean.getTitle() );
        noteDO.content( bean.getContent() );
        noteDO.avatar( bean.getAvatar() );

        return noteDO.build();
    }

    @Override
    public AppNoteRespVO convert(NoteDO bean) {
        if ( bean == null ) {
            return null;
        }

        AppNoteRespVO appNoteRespVO = new AppNoteRespVO();

        appNoteRespVO.setPositionId( bean.getPositionId() );
        appNoteRespVO.setUserId( bean.getUserId() );
        appNoteRespVO.setTitle( bean.getTitle() );
        appNoteRespVO.setContent( bean.getContent() );
        appNoteRespVO.setViewCount( bean.getViewCount() );
        appNoteRespVO.setLikeCount( bean.getLikeCount() );
        appNoteRespVO.setIsSticky( bean.getIsSticky() );
        appNoteRespVO.setCommentCount( bean.getCommentCount() );
        appNoteRespVO.setLastCommentUserId( bean.getLastCommentUserId() );
        appNoteRespVO.setAvatar( bean.getAvatar() );
        appNoteRespVO.setPublishDate( bean.getPublishDate() );
        appNoteRespVO.setLastModifyDate( bean.getLastModifyDate() );
        appNoteRespVO.setCityCode( bean.getCityCode() );
        appNoteRespVO.setCityName( bean.getCityName() );
        appNoteRespVO.setId( bean.getId() );
        appNoteRespVO.setCreateTime( bean.getCreateTime() );

        return appNoteRespVO;
    }

    @Override
    public AppNoteInfoRespVO convertToInfo(NoteDO bean) {
        if ( bean == null ) {
            return null;
        }

        AppNoteInfoRespVO appNoteInfoRespVO = new AppNoteInfoRespVO();

        appNoteInfoRespVO.setPositionId( bean.getPositionId() );
        appNoteInfoRespVO.setUserId( bean.getUserId() );
        appNoteInfoRespVO.setTitle( bean.getTitle() );
        appNoteInfoRespVO.setContent( bean.getContent() );
        appNoteInfoRespVO.setViewCount( bean.getViewCount() );
        appNoteInfoRespVO.setLikeCount( bean.getLikeCount() );
        appNoteInfoRespVO.setIsSticky( bean.getIsSticky() );
        appNoteInfoRespVO.setCommentCount( bean.getCommentCount() );
        appNoteInfoRespVO.setLastCommentUserId( bean.getLastCommentUserId() );
        appNoteInfoRespVO.setAvatar( bean.getAvatar() );
        appNoteInfoRespVO.setPublishDate( bean.getPublishDate() );
        appNoteInfoRespVO.setLastModifyDate( bean.getLastModifyDate() );
        appNoteInfoRespVO.setCityCode( bean.getCityCode() );
        appNoteInfoRespVO.setCityName( bean.getCityName() );
        appNoteInfoRespVO.setId( bean.getId() );
        appNoteInfoRespVO.setCreateTime( bean.getCreateTime() );

        return appNoteInfoRespVO;
    }

    @Override
    public List<AppNoteRespVO> convertList(List<NoteDO> list) {
        if ( list == null ) {
            return null;
        }

        List<AppNoteRespVO> list1 = new ArrayList<AppNoteRespVO>( list.size() );
        for ( NoteDO noteDO : list ) {
            list1.add( convert( noteDO ) );
        }

        return list1;
    }

    @Override
    public PageResult<AppNoteRespVO> convertPage(PageResult<NoteDO> page) {
        if ( page == null ) {
            return null;
        }

        PageResult<AppNoteRespVO> pageResult = new PageResult<AppNoteRespVO>();

        pageResult.setList( convertList( page.getList() ) );
        pageResult.setTotal( page.getTotal() );

        return pageResult;
    }

    @Override
    public List<AppNoteExcelVO> convertList02(List<NoteDO> list) {
        if ( list == null ) {
            return null;
        }

        List<AppNoteExcelVO> list1 = new ArrayList<AppNoteExcelVO>( list.size() );
        for ( NoteDO noteDO : list ) {
            list1.add( noteDOToAppNoteExcelVO( noteDO ) );
        }

        return list1;
    }

    protected AppNoteExcelVO noteDOToAppNoteExcelVO(NoteDO noteDO) {
        if ( noteDO == null ) {
            return null;
        }

        AppNoteExcelVO appNoteExcelVO = new AppNoteExcelVO();

        appNoteExcelVO.setId( noteDO.getId() );
        appNoteExcelVO.setPositionId( noteDO.getPositionId() );
        appNoteExcelVO.setUserId( noteDO.getUserId() );
        appNoteExcelVO.setCategoryId( noteDO.getCategoryId() );
        appNoteExcelVO.setTitle( noteDO.getTitle() );
        appNoteExcelVO.setContent( noteDO.getContent() );
        appNoteExcelVO.setExcerpt( noteDO.getExcerpt() );
        appNoteExcelVO.setArticleStatus( noteDO.getArticleStatus() );
        appNoteExcelVO.setViewCount( noteDO.getViewCount() );
        appNoteExcelVO.setLikeCount( noteDO.getLikeCount() );
        appNoteExcelVO.setIsSticky( noteDO.getIsSticky() );
        appNoteExcelVO.setIsEssence( noteDO.getIsEssence() );
        appNoteExcelVO.setCommentStatus( noteDO.getCommentStatus() );
        appNoteExcelVO.setCommentCount( noteDO.getCommentCount() );
        appNoteExcelVO.setLastCommentUserId( noteDO.getLastCommentUserId() );
        appNoteExcelVO.setAvatar( noteDO.getAvatar() );
        appNoteExcelVO.setPublishDate( noteDO.getPublishDate() );
        appNoteExcelVO.setPublishIp( noteDO.getPublishIp() );
        appNoteExcelVO.setLastModifyDate( noteDO.getLastModifyDate() );
        appNoteExcelVO.setLastModifyIp( noteDO.getLastModifyIp() );
        appNoteExcelVO.setMode( noteDO.getMode() );
        appNoteExcelVO.setCreateTime( noteDO.getCreateTime() );

        return appNoteExcelVO;
    }
}
