import{_ as u,__tla as m}from"./ContentWrap.vue_vue_type_script_setup_true_lang-a574ccef.js";import{_ as d,__tla as y}from"./IFrame.vue_vue_type_script_setup_true_lang-f3d5b5e9.js";import{_ as h,__tla as p}from"./index-b39a19a1.js";import{b as f,__tla as x}from"./index-74e8f36e.js";import{d as b,r as s,A as v,o as e,c as w,i as a,w as g,a as c,q as k,a3 as q,F as A,__tla as B}from"./index-97fffa0c.js";import{__tla as D}from"./el-card-6c7c099d.js";import"./_plugin-vue_export-helper-1b428a4d.js";let o,F=Promise.all([(()=>{try{return m}catch{}})(),(()=>{try{return y}catch{}})(),(()=>{try{return p}catch{}})(),(()=>{try{return x}catch{}})(),(()=>{try{return B}catch{}})(),(()=>{try{return D}catch{}})()]).then(async()=>{o=b({name:"InfraDruid",__name:"index",setup(I){const r=s(!0),l=s("http://localhost:48080/druid/index.html");return v(async()=>{try{const t=await f("url.druid");t&&t.length>0&&(l.value=t)}finally{r.value=!1}}),(t,M)=>{const _=h,n=d,i=u;return e(),w(A,null,[a(_,{title:"\u6570\u636E\u5E93 MyBatis",url:"https://doc.iocoder.cn/mybatis/"}),a(_,{title:"\u591A\u6570\u636E\u6E90\uFF08\u8BFB\u5199\u5206\u79BB\uFF09",url:"https://doc.iocoder.cn/dynamic-datasource/"}),a(i,null,{default:g(()=>[c(r)?q("",!0):(e(),k(n,{key:0,src:c(l)},null,8,["src"]))]),_:1})],64)}}})});export{F as __tla,o as default};
