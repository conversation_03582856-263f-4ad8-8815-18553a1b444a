#!/bin/bash

# 应用状态检查脚本 - 钓鱼吧应用

APP_NAME="diaoyuba"
PID_FILE="/var/run/diaoyuba.pid"
LOG_DIR="/log"
SERVER_PORT="48080"

echo "=========================================="
echo "$APP_NAME 应用状态检查"
echo "=========================================="

# 检查进程状态
check_process() {
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        if ps -p $PID > /dev/null 2>&1; then
            echo "✅ 应用进程: 运行中 (PID: $PID)"
            
            # 显示进程详细信息
            echo "   启动时间: $(ps -o lstart= -p $PID)"
            echo "   CPU使用: $(ps -o %cpu= -p $PID)%"
            echo "   内存使用: $(ps -o %mem= -p $PID)%"
            echo "   内存大小: $(ps -o rss= -p $PID | awk '{printf "%.1f MB", $1/1024}')"
            return 0
        else
            echo "❌ 应用进程: PID文件存在但进程不存在"
            return 1
        fi
    else
        # 尝试通过进程名查找
        PID=$(ps aux | grep "yudao-server.jar" | grep -v grep | awk '{print $2}')
        if [ ! -z "$PID" ]; then
            echo "⚠️ 应用进程: 运行中但无PID文件 (PID: $PID)"
            return 0
        else
            echo "❌ 应用进程: 未运行"
            return 1
        fi
    fi
}

# 检查端口状态
check_port() {
    if netstat -tuln | grep ":$SERVER_PORT " > /dev/null 2>&1; then
        echo "✅ 端口状态: $SERVER_PORT 已监听"
        return 0
    else
        echo "❌ 端口状态: $SERVER_PORT 未监听"
        return 1
    fi
}

# 检查HTTP服务
check_http() {
    local url="http://localhost:$SERVER_PORT/actuator/health"
    echo "🔍 检查HTTP服务: $url"
    
    if command -v curl > /dev/null 2>&1; then
        response=$(curl -s -w "%{http_code}" -o /dev/null --connect-timeout 5 "$url" 2>/dev/null)
        if [ "$response" = "200" ]; then
            echo "✅ HTTP服务: 正常响应"
            return 0
        else
            echo "❌ HTTP服务: 响应异常 (状态码: $response)"
            return 1
        fi
    else
        echo "⚠️ HTTP服务: 无法检查 (curl未安装)"
        return 2
    fi
}

# 检查日志文件
check_logs() {
    echo ""
    echo "📁 日志文件状态:"
    
    if [ -f "$LOG_DIR/diaoyuba.log" ]; then
        local size=$(du -h "$LOG_DIR/diaoyuba.log" | cut -f1)
        local modified=$(stat -c %y "$LOG_DIR/diaoyuba.log" 2>/dev/null | cut -d'.' -f1)
        echo "   应用日志: 存在 ($size, 修改时间: $modified)"
        
        # 检查最近的错误
        local errors=$(tail -100 "$LOG_DIR/diaoyuba.log" | grep -i error | wc -l)
        if [ $errors -gt 0 ]; then
            echo "   ⚠️ 最近100行中有 $errors 个错误"
        fi
    else
        echo "   ❌ 应用日志: 不存在"
    fi
    
    if [ -f "$LOG_DIR/diaoyuba-error.log" ]; then
        local size=$(du -h "$LOG_DIR/diaoyuba-error.log" | cut -f1)
        local lines=$(wc -l < "$LOG_DIR/diaoyuba-error.log")
        echo "   ⚠️ 错误日志: 存在 ($size, $lines 行)"
    else
        echo "   ✅ 错误日志: 不存在"
    fi
    
    if [ -f "$LOG_DIR/gc.log" ]; then
        local size=$(du -h "$LOG_DIR/gc.log" | cut -f1)
        echo "   📊 GC日志: 存在 ($size)"
    fi
}

# 显示系统资源
check_resources() {
    echo ""
    echo "💻 系统资源:"
    echo "   CPU负载: $(uptime | awk -F'load average:' '{print $2}')"
    echo "   内存使用: $(free -h | grep '^Mem:' | awk '{printf "%s/%s (%.1f%%)", $3, $2, ($3/$2)*100}')"
    echo "   磁盘使用: $(df -h / | tail -1 | awk '{printf "%s/%s (%s)", $3, $2, $5}')"
    
    if [ -d "$LOG_DIR" ]; then
        echo "   日志目录: $(du -sh "$LOG_DIR" | cut -f1)"
    fi
}

# 主检查流程
main() {
    local exit_code=0
    
    # 检查进程
    if ! check_process; then
        exit_code=1
    fi
    
    echo ""
    
    # 检查端口
    if ! check_port; then
        exit_code=1
    fi
    
    echo ""
    
    # 检查HTTP服务
    check_http
    
    # 检查日志
    check_logs
    
    # 检查系统资源
    check_resources
    
    echo ""
    echo "=========================================="
    
    if [ $exit_code -eq 0 ]; then
        echo "✅ 应用状态: 正常运行"
    else
        echo "❌ 应用状态: 异常"
        echo ""
        echo "🔧 建议操作:"
        echo "   查看日志: tail -f $LOG_DIR/diaoyuba.log"
        echo "   重启应用: ./stop-production.sh && ./start-production.sh"
    fi
    
    return $exit_code
}

# 执行检查
main
