<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="ALL" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="c8e63bfc-c405-4fbf-8e78-bc247dab98b1" name="Changes" comment="[team] dev1.1 附近排序" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="CompilerWorkspaceConfiguration">
    <option name="MAKE_PROJECT_ON_SAVE" value="true" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Interface" />
        <option value="Enum" />
        <option value="Class" />
        <option value="JUnit5 Test Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="feature/20240814-provincial_water" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
    <option name="RESET_MODE" value="MIXED" />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="jar://$PROJECT_DIR$/../../../developSoftWare/jdk1.8/java/Jdk1.8/src.zip!/java/awt/Font.java" root0="SKIP_INSPECTION" />
  </component>
  <component name="HttpClientSelectedEnvironments">
    <file url="jar://$APPLICATION_HOME_DIR$/plugins/restClient/lib/restClient.jar!/com/intellij/ws/rest/client/requests/collection/get-requests.http" environment="test" />
  </component>
  <component name="JRebelWorkspace">
    <option name="jrebelEnabledAutocompile" value="true" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="localRepository" value="D:\develop\maven_repository" />
        <option name="mavenHome" value="$PROJECT_DIR$/../../../../developSoftWare/maven/apache-maven-3.6.3" />
        <option name="useMavenConfig" value="true" />
        <option name="userSettingsFile" value="D:\developSoftWare\maven\apache-maven-3.6.3\conf\settings-local.xml" />
      </MavenGeneralSettings>
    </option>
    <option name="enabledProfiles">
      <list>
        <option value="repo4" />
        <option value="aliyun" />
      </list>
    </option>
  </component>
  <component name="MavenRunner">
    <option name="skipTests" value="true" />
    <option name="vmOptions" value="-DarchetypeCatalog=internal" />
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectId" id="2XtDEpTylZ6HLnDzMD9Jf6qIrJA" />
  <component name="ProjectLevelVcsManager" settingsEditedManually="true" />
  <component name="ProjectViewState">
    <option name="autoscrollToSource" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;WebServerToolWindowFactoryState&quot;: &quot;false&quot;,
    &quot;com.intellij.testIntegration.createTest.CreateTestDialog.defaultLibrary&quot;: &quot;JUnit5&quot;,
    &quot;com.intellij.testIntegration.createTest.CreateTestDialog.defaultLibrarySuperClass.JUnit5&quot;: &quot;&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/project/study/diaodianba/ccc-diaoyuba&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;Modules&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.0&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.0&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;MavenSettings&quot;,
    &quot;spring.configuration.checksum&quot;: &quot;18fb4989fc3a6387b509247dd9e72839&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RebelAgentSelection">
    <selection>jr</selection>
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\project\study\ccc-diaoyuba\sql" />
      <recent name="D:\project\study\ccc-diaoyuba\yudao-module-diaoyuba\yudao-module-diaoyuba-biz\src\main\java\cn\iocoder\yudao\module\diaoyuba" />
      <recent name="D:\project\study\ccc-diaoyuba\yudao-module-diaoyuba\yudao-module-diaoyuba-biz\src\main\resources\mapper" />
      <recent name="D:\project\study\ccc-diaoyuba\yudao-module-diaoyuba\yudao-module-diaoyuba-biz\src\main\java\cn\iocoder\yudao\module\diaoyuba\dal\mysql" />
      <recent name="D:\project\study\ccc-diaoyuba\yudao-module-diaoyuba\yudao-module-diaoyuba-biz\src\main\java\cn\iocoder\yudao\module\diaoyuba\convert" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\project\study\ccc-diaoyuba\sql\mysql" />
    </key>
    <key name="CreateTestDialog.Recents.Supers">
      <recent name="" />
    </key>
    <key name="CreateTestDialog.RecentsKey">
      <recent name="cn.iocoder.yudao.module.diaoyuba.job" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="cn.iocoder.yudao.module.diaoyuba.controller.app.waterstation" />
      <recent name="cn.iocoder.yudao.module.diaoyuba.controller.app.waterstationflow" />
      <recent name="cn.iocoder.yudao.module.member.controller.app.check.vo" />
      <recent name="cn.iocoder.yudao.module.diaoyuba.controller.app.fishpositionimg.vo" />
      <recent name="cn.iocoder.yudao.module.diaoyuba.dal.mysql.fishpositioninfo" />
    </key>
  </component>
  <component name="RunManager" selected="Spring Boot.YudaoServerApplication">
    <configuration name="AreaUtils" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="cn.iocoder.yudao.framework.ip.core.utils.AreaUtils" />
      <module name="yudao-spring-boot-starter-biz-ip" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="cn.iocoder.yudao.framework.ip.core.utils.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ProjectReactor" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="cn.iocoder.yudao.ProjectReactor" />
      <module name="yudao-server" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="cn.iocoder.yudao.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="WaterLevelChongQingJob" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="cn.iocoder.yudao.module.diaoyuba.job.WaterLevelChongQingJob" />
      <module name="yudao-module-diaoyuba-biz" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="cn.iocoder.yudao.module.diaoyuba.job.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="WaterLevelChongQingJobTest" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="yudao-module-diaoyuba-biz" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="cn.iocoder.yudao.module.diaoyuba.job.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="cn.iocoder.yudao.module.diaoyuba.job" />
      <option name="MAIN_CLASS_NAME" value="cn.iocoder.yudao.module.diaoyuba.job.WaterLevelChongQingJobTest" />
      <option name="TEST_OBJECT" value="class" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="WaterLevelChongQingJobTest.execute" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="yudao-module-diaoyuba-biz" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="cn.iocoder.yudao.module.diaoyuba.job.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="cn.iocoder.yudao.module.diaoyuba.job" />
      <option name="MAIN_CLASS_NAME" value="cn.iocoder.yudao.module.diaoyuba.job.WaterLevelChongQingJobTest" />
      <option name="METHOD_NAME" value="execute" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="YudaoServerApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="yudao-server" />
      <selectedOptions>
        <option name="environmentVariables" />
      </selectedOptions>
      <option name="SHORTEN_COMMAND_LINE" value="CLASSPATH_FILE" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="cn.iocoder.yudao.server.YudaoServerApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <list>
      <item itemvalue="Application.ProjectReactor" />
      <item itemvalue="Application.AreaUtils" />
      <item itemvalue="Application.WaterLevelChongQingJob" />
      <item itemvalue="JUnit.WaterLevelChongQingJobTest" />
      <item itemvalue="JUnit.WaterLevelChongQingJobTest.execute" />
      <item itemvalue="Spring Boot.YudaoServerApplication" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="Application.ProjectReactor" />
        <item itemvalue="JUnit.WaterLevelChongQingJobTest" />
        <item itemvalue="JUnit.WaterLevelChongQingJobTest.execute" />
        <item itemvalue="Application.WaterLevelChongQingJob" />
        <item itemvalue="Application.AreaUtils" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="SvnConfiguration">
    <configuration>C:\Users\<USER>\AppData\Roaming\Subversion</configuration>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="c8e63bfc-c405-4fbf-8e78-bc247dab98b1" name="Changes" comment="" />
      <created>1699439373927</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1699439373927</updated>
      <workItem from="1699439375746" duration="502000" />
      <workItem from="1710725600358" duration="23000" />
      <workItem from="1715918147958" duration="1298000" />
      <workItem from="1716168993110" duration="1193000" />
      <workItem from="1716341854390" duration="397000" />
      <workItem from="1716428075616" duration="425000" />
      <workItem from="1716428857487" duration="3517000" />
      <workItem from="1716453817189" duration="248000" />
      <workItem from="1716454069974" duration="61000" />
      <workItem from="1716455297264" duration="1401000" />
      <workItem from="1716456708477" duration="2664000" />
      <workItem from="1716515622728" duration="15217000" />
      <workItem from="1716773741592" duration="3382000" />
      <workItem from="1716789425061" duration="41000" />
      <workItem from="1716789489127" duration="1057000" />
      <workItem from="1716791373776" duration="608000" />
      <workItem from="1716803004068" duration="2095000" />
      <workItem from="1716815060151" duration="1817000" />
      <workItem from="1716860453215" duration="21795000" />
      <workItem from="1716946386138" duration="23296000" />
      <workItem from="1717032451423" duration="3539000" />
      <workItem from="1717119684077" duration="1258000" />
      <workItem from="1717378637637" duration="1320000" />
      <workItem from="1717464994223" duration="5684000" />
      <workItem from="1717552032482" duration="7626000" />
      <workItem from="1717638109893" duration="20223000" />
      <workItem from="1717724183426" duration="599000" />
      <workItem from="1718009976470" duration="1188000" />
      <workItem from="1718069692622" duration="2433000" />
      <workItem from="1718156055736" duration="4714000" />
      <workItem from="1718242617318" duration="596000" />
      <workItem from="1718617092675" duration="2090000" />
      <workItem from="1718674431297" duration="7636000" />
      <workItem from="1718784103235" duration="5193000" />
      <workItem from="1718847315644" duration="12894000" />
      <workItem from="1719192795763" duration="16322000" />
      <workItem from="1719279826597" duration="2454000" />
      <workItem from="1719366726318" duration="1798000" />
      <workItem from="1719815344373" duration="6875000" />
      <workItem from="1719884088954" duration="5100000" />
      <workItem from="1719970826383" duration="595000" />
      <workItem from="1719990971845" duration="3107000" />
      <workItem from="1720056541596" duration="1323000" />
      <workItem from="1720064969696" duration="12770000" />
      <workItem from="1720143843437" duration="8115000" />
      <workItem from="1720402551563" duration="595000" />
      <workItem from="1720488840151" duration="17571000" />
      <workItem from="1720575256135" duration="9081000" />
      <workItem from="1720662426694" duration="470000" />
      <workItem from="1720776515473" duration="569000" />
      <workItem from="1720777095621" duration="2067000" />
      <workItem from="1720779317089" duration="942000" />
      <workItem from="1721006966360" duration="9304000" />
      <workItem from="1721093669718" duration="3913000" />
      <workItem from="1721186029929" duration="723000" />
      <workItem from="1721266568930" duration="1188000" />
      <workItem from="1721719755104" duration="6361000" />
      <workItem from="1721784801764" duration="1746000" />
      <workItem from="1721811626399" duration="226000" />
      <workItem from="1722507737880" duration="674000" />
      <workItem from="1722563663244" duration="875000" />
      <workItem from="1722565375255" duration="2787000" />
      <workItem from="1722822215894" duration="1259000" />
      <workItem from="1722846081572" duration="4805000" />
      <workItem from="1722908171429" duration="15529000" />
      <workItem from="1722994468784" duration="3408000" />
      <workItem from="1723080870916" duration="5699000" />
      <workItem from="1723097461224" duration="9093000" />
      <workItem from="1723167458876" duration="6143000" />
      <workItem from="1723182695699" duration="17212000" />
      <workItem from="1723283173621" duration="7864000" />
      <workItem from="1723348582828" duration="1510000" />
      <workItem from="1723426636227" duration="18208000" />
      <workItem from="1723513006952" duration="4118000" />
      <workItem from="1723540476428" duration="67000" />
      <workItem from="1723616676073" duration="13090000" />
      <workItem from="1723685686933" duration="22118000" />
      <workItem from="1723772054979" duration="7466000" />
      <workItem from="1723809879327" duration="615000" />
      <workItem from="1724032112596" duration="2803000" />
      <workItem from="1724117522107" duration="594000" />
      <workItem from="1724310834392" duration="880000" />
      <workItem from="1724376876365" duration="21000" />
      <workItem from="1724722680704" duration="6000" />
      <workItem from="1724808832896" duration="593000" />
      <workItem from="1724895430152" duration="1191000" />
      <workItem from="1724981746919" duration="1105000" />
      <workItem from="1725242290056" duration="594000" />
      <workItem from="1725601746930" duration="405000" />
      <workItem from="1725845691764" duration="642000" />
      <workItem from="1725932153161" duration="718000" />
      <workItem from="1726020619665" duration="4348000" />
      <workItem from="1726104924729" duration="5414000" />
      <workItem from="1726208670674" duration="1942000" />
      <workItem from="1736927894036" duration="626000" />
      <workItem from="1736929410172" duration="563000" />
      <workItem from="1752040835005" duration="6598000" />
      <workItem from="1752111666211" duration="12206000" />
      <workItem from="1752197162631" duration="4131000" />
      <workItem from="1752415573626" duration="4082000" />
      <workItem from="1752581433052" duration="2162000" />
      <workItem from="1752629679892" duration="15326000" />
      <workItem from="1752716336077" duration="18418000" />
      <workItem from="1752802884164" duration="13119000" />
      <workItem from="1752849273391" duration="2449000" />
      <workItem from="1753018662160" duration="7209000" />
      <workItem from="1753061415145" duration="1459000" />
      <workItem from="1753148244095" duration="1741000" />
    </task>
    <task id="LOCAL-00038" summary="[notes] 文件修改及安全验证接口开发">
      <created>1720516360343</created>
      <option name="number" value="00038" />
      <option name="presentableId" value="LOCAL-00038" />
      <option name="project" value="LOCAL" />
      <updated>1720516360343</updated>
    </task>
    <task id="LOCAL-00039" summary="[notes] 删除无用文件">
      <created>1720516984516</created>
      <option name="number" value="00039" />
      <option name="presentableId" value="LOCAL-00039" />
      <option name="project" value="LOCAL" />
      <updated>1720516984516</updated>
    </task>
    <task id="LOCAL-00040" summary="[notes] 增加幂等校验">
      <created>1720581214034</created>
      <option name="number" value="00040" />
      <option name="presentableId" value="LOCAL-00040" />
      <option name="project" value="LOCAL" />
      <updated>1720581214034</updated>
    </task>
    <task id="LOCAL-00041" summary="[notes] 修改安全校验">
      <created>1720777653811</created>
      <option name="number" value="00041" />
      <option name="presentableId" value="LOCAL-00041" />
      <option name="project" value="LOCAL" />
      <updated>1720777653811</updated>
    </task>
    <task id="LOCAL-00042" summary="[notes] 修改安全校验">
      <created>1720778348896</created>
      <option name="number" value="00042" />
      <option name="presentableId" value="LOCAL-00042" />
      <option name="project" value="LOCAL" />
      <updated>1720778348896</updated>
    </task>
    <task id="LOCAL-00043" summary="[picture] 增加系统消息发送及笔记照片放到钓点图片中">
      <created>1721722019053</created>
      <option name="number" value="00043" />
      <option name="presentableId" value="LOCAL-00043" />
      <option name="project" value="LOCAL" />
      <updated>1721722019053</updated>
    </task>
    <task id="LOCAL-00044" summary="[picture] 增加系统消息发送及笔记照片放到钓点图片中">
      <created>1721725965068</created>
      <option name="number" value="00044" />
      <option name="presentableId" value="LOCAL-00044" />
      <option name="project" value="LOCAL" />
      <updated>1721725965068</updated>
    </task>
    <task id="LOCAL-00045" summary="[waterStation] generate code">
      <created>1723105896767</created>
      <option name="number" value="00045" />
      <option name="presentableId" value="LOCAL-00045" />
      <option name="project" value="LOCAL" />
      <updated>1723105896767</updated>
    </task>
    <task id="LOCAL-00046" summary="[waterStation] generate code">
      <created>1723177433550</created>
      <option name="number" value="00046" />
      <option name="presentableId" value="LOCAL-00046" />
      <option name="project" value="LOCAL" />
      <updated>1723177433550</updated>
    </task>
    <task id="LOCAL-00047" summary="[waterStation] generate code">
      <created>1723194429621</created>
      <option name="number" value="00047" />
      <option name="presentableId" value="LOCAL-00047" />
      <option name="project" value="LOCAL" />
      <updated>1723194429621</updated>
    </task>
    <task id="LOCAL-00048" summary="[waterStation] Decryption success">
      <created>1723199063503</created>
      <option name="number" value="00048" />
      <option name="presentableId" value="LOCAL-00048" />
      <option name="project" value="LOCAL" />
      <updated>1723199063503</updated>
    </task>
    <task id="LOCAL-00049" summary="[waterStation] Decryption success">
      <created>1723199594053</created>
      <option name="number" value="00049" />
      <option name="presentableId" value="LOCAL-00049" />
      <option name="project" value="LOCAL" />
      <updated>1723199594053</updated>
    </task>
    <task id="LOCAL-00050" summary="[waterStation] Decryption success">
      <created>1723207179742</created>
      <option name="number" value="00050" />
      <option name="presentableId" value="LOCAL-00050" />
      <option name="project" value="LOCAL" />
      <updated>1723207179742</updated>
    </task>
    <task id="LOCAL-00051" summary="[waterStation] Decryption success">
      <created>1723286483549</created>
      <option name="number" value="00051" />
      <option name="presentableId" value="LOCAL-00051" />
      <option name="project" value="LOCAL" />
      <updated>1723286483549</updated>
    </task>
    <task id="LOCAL-00052" summary="[waterStation] Decryption success">
      <created>1723298244160</created>
      <option name="number" value="00052" />
      <option name="presentableId" value="LOCAL-00052" />
      <option name="project" value="LOCAL" />
      <updated>1723298244160</updated>
    </task>
    <task id="LOCAL-00053" summary="[waterStation] Decryption success">
      <created>1723300915191</created>
      <option name="number" value="00053" />
      <option name="presentableId" value="LOCAL-00053" />
      <option name="project" value="LOCAL" />
      <updated>1723300915191</updated>
    </task>
    <task id="LOCAL-00054" summary="[waterStation] Decryption success">
      <created>1723349210732</created>
      <option name="number" value="00054" />
      <option name="presentableId" value="LOCAL-00054" />
      <option name="project" value="LOCAL" />
      <updated>1723349210732</updated>
    </task>
    <task id="LOCAL-00055" summary="[waterStation] develop">
      <created>1723430341124</created>
      <option name="number" value="00055" />
      <option name="presentableId" value="LOCAL-00055" />
      <option name="project" value="LOCAL" />
      <updated>1723430341124</updated>
    </task>
    <task id="LOCAL-00056" summary="[waterStation] develop">
      <created>1723433817993</created>
      <option name="number" value="00056" />
      <option name="presentableId" value="LOCAL-00056" />
      <option name="project" value="LOCAL" />
      <updated>1723433817993</updated>
    </task>
    <task id="LOCAL-00057" summary="[waterStation] develop">
      <created>1723441102867</created>
      <option name="number" value="00057" />
      <option name="presentableId" value="LOCAL-00057" />
      <option name="project" value="LOCAL" />
      <updated>1723441102867</updated>
    </task>
    <task id="LOCAL-00058" summary="[waterStation] develop">
      <created>1723444454435</created>
      <option name="number" value="00058" />
      <option name="presentableId" value="LOCAL-00058" />
      <option name="project" value="LOCAL" />
      <updated>1723444454435</updated>
    </task>
    <task id="LOCAL-00059" summary="[waterStation] develop done">
      <created>1723448180111</created>
      <option name="number" value="00059" />
      <option name="presentableId" value="LOCAL-00059" />
      <option name="project" value="LOCAL" />
      <updated>1723448180111</updated>
    </task>
    <task id="LOCAL-00060" summary="[waterStation] develop done">
      <created>1723448198383</created>
      <option name="number" value="00060" />
      <option name="presentableId" value="LOCAL-00060" />
      <option name="project" value="LOCAL" />
      <updated>1723448198383</updated>
    </task>
    <task id="LOCAL-00061" summary="[waterStation] develop done">
      <created>1723450303138</created>
      <option name="number" value="00061" />
      <option name="presentableId" value="LOCAL-00061" />
      <option name="project" value="LOCAL" />
      <updated>1723450303138</updated>
    </task>
    <task id="LOCAL-00062" summary="[waterStation] develop done">
      <created>1723450325538</created>
      <option name="number" value="00062" />
      <option name="presentableId" value="LOCAL-00062" />
      <option name="project" value="LOCAL" />
      <updated>1723450325538</updated>
    </task>
    <task id="LOCAL-00063" summary="[waterStation] develop done">
      <created>1723453369508</created>
      <option name="number" value="00063" />
      <option name="presentableId" value="LOCAL-00063" />
      <option name="project" value="LOCAL" />
      <updated>1723453369508</updated>
    </task>
    <task id="LOCAL-00064" summary="[waterStation] develop done">
      <created>1723518220907</created>
      <option name="number" value="00064" />
      <option name="presentableId" value="LOCAL-00064" />
      <option name="project" value="LOCAL" />
      <updated>1723518220908</updated>
    </task>
    <task id="LOCAL-00065" summary="[waterStation] develop done">
      <created>1723540524941</created>
      <option name="number" value="00065" />
      <option name="presentableId" value="LOCAL-00065" />
      <option name="project" value="LOCAL" />
      <updated>1723540524941</updated>
    </task>
    <task id="LOCAL-00066" summary="[waterStation] develop done">
      <created>1723620031338</created>
      <option name="number" value="00066" />
      <option name="presentableId" value="LOCAL-00066" />
      <option name="project" value="LOCAL" />
      <updated>1723620031338</updated>
    </task>
    <task id="LOCAL-00067" summary="[waterStation] 重庆江河">
      <created>1723632707698</created>
      <option name="number" value="00067" />
      <option name="presentableId" value="LOCAL-00067" />
      <option name="project" value="LOCAL" />
      <updated>1723632707698</updated>
    </task>
    <task id="LOCAL-00068" summary="[waterStation] 重庆湖库">
      <created>1723693623246</created>
      <option name="number" value="00068" />
      <option name="presentableId" value="LOCAL-00068" />
      <option name="project" value="LOCAL" />
      <updated>1723693623246</updated>
    </task>
    <task id="LOCAL-00069" summary="[waterStation] 重庆湖库">
      <created>1723704077298</created>
      <option name="number" value="00069" />
      <option name="presentableId" value="LOCAL-00069" />
      <option name="project" value="LOCAL" />
      <updated>1723704077298</updated>
    </task>
    <task id="LOCAL-00070" summary="[waterStation] 重庆湖库">
      <created>1723704426004</created>
      <option name="number" value="00070" />
      <option name="presentableId" value="LOCAL-00070" />
      <option name="project" value="LOCAL" />
      <updated>1723704426004</updated>
    </task>
    <task id="LOCAL-00071" summary="[waterStation] 重庆湖库">
      <created>1723706313655</created>
      <option name="number" value="00071" />
      <option name="presentableId" value="LOCAL-00071" />
      <option name="project" value="LOCAL" />
      <updated>1723706313655</updated>
    </task>
    <task id="LOCAL-00072" summary="[waterStation] 重庆湖库">
      <created>1723707286584</created>
      <option name="number" value="00072" />
      <option name="presentableId" value="LOCAL-00072" />
      <option name="project" value="LOCAL" />
      <updated>1723707286584</updated>
    </task>
    <task id="LOCAL-00073" summary="[waterStation] 重庆湖库">
      <created>1723787157328</created>
      <option name="number" value="00073" />
      <option name="presentableId" value="LOCAL-00073" />
      <option name="project" value="LOCAL" />
      <updated>1723787157328</updated>
    </task>
    <task id="LOCAL-00074" summary="[team] dev1.1">
      <created>1752056645928</created>
      <option name="number" value="00074" />
      <option name="presentableId" value="LOCAL-00074" />
      <option name="project" value="LOCAL" />
      <updated>1752056645928</updated>
    </task>
    <task id="LOCAL-00075" summary="[team] dev1.1 list">
      <created>1752163860476</created>
      <option name="number" value="00075" />
      <option name="presentableId" value="LOCAL-00075" />
      <option name="project" value="LOCAL" />
      <updated>1752163860476</updated>
    </task>
    <task id="LOCAL-00076" summary="[team] dev1.1 list">
      <created>1752423860727</created>
      <option name="number" value="00076" />
      <option name="presentableId" value="LOCAL-00076" />
      <option name="project" value="LOCAL" />
      <updated>1752423860727</updated>
    </task>
    <task id="LOCAL-00077" summary="[team] dev1.1 team">
      <created>1752646599710</created>
      <option name="number" value="00077" />
      <option name="presentableId" value="LOCAL-00077" />
      <option name="project" value="LOCAL" />
      <updated>1752646599711</updated>
    </task>
    <task id="LOCAL-00078" summary="[team] dev1.1 team detail">
      <created>1752677784585</created>
      <option name="number" value="00078" />
      <option name="presentableId" value="LOCAL-00078" />
      <option name="project" value="LOCAL" />
      <updated>1752677784585</updated>
    </task>
    <task id="LOCAL-00079" summary="[team] dev1.1 team detail comment">
      <created>1752767380614</created>
      <option name="number" value="00079" />
      <option name="presentableId" value="LOCAL-00079" />
      <option name="project" value="LOCAL" />
      <updated>1752767380615</updated>
    </task>
    <task id="LOCAL-00080" summary="[team] dev1.1 team detail comment">
      <created>1752771255371</created>
      <option name="number" value="00080" />
      <option name="presentableId" value="LOCAL-00080" />
      <option name="project" value="LOCAL" />
      <updated>1752771255371</updated>
    </task>
    <task id="LOCAL-00081" summary="[team] dev1.1 team detail comment">
      <created>1752772284408</created>
      <option name="number" value="00081" />
      <option name="presentableId" value="LOCAL-00081" />
      <option name="project" value="LOCAL" />
      <updated>1752772284408</updated>
    </task>
    <task id="LOCAL-00082" summary="[team] dev1.1 team detail comment">
      <created>1752820104618</created>
      <option name="number" value="00082" />
      <option name="presentableId" value="LOCAL-00082" />
      <option name="project" value="LOCAL" />
      <updated>1752820104619</updated>
    </task>
    <task id="LOCAL-00083" summary="[team] dev1.1 team detail">
      <created>1752834613449</created>
      <option name="number" value="00083" />
      <option name="presentableId" value="LOCAL-00083" />
      <option name="project" value="LOCAL" />
      <updated>1752834613449</updated>
    </task>
    <task id="LOCAL-00084" summary="[team] dev1.1 done">
      <created>1752851226049</created>
      <option name="number" value="00084" />
      <option name="presentableId" value="LOCAL-00084" />
      <option name="project" value="LOCAL" />
      <updated>1752851226049</updated>
    </task>
    <task id="LOCAL-00085" summary="[team] dev1.1 done">
      <created>1753026234919</created>
      <option name="number" value="00085" />
      <option name="presentableId" value="LOCAL-00085" />
      <option name="project" value="LOCAL" />
      <updated>1753026234919</updated>
    </task>
    <task id="LOCAL-00086" summary="[team] dev1.1 附近排序">
      <created>1753028887370</created>
      <option name="number" value="00086" />
      <option name="presentableId" value="LOCAL-00086" />
      <option name="project" value="LOCAL" />
      <updated>1753028887370</updated>
    </task>
    <option name="localTasksCounter" value="87" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="feature/20250709-team" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="[notes] 增加定时任务" />
    <MESSAGE value="[notes] 修改排序" />
    <MESSAGE value="[notes] 修改路径" />
    <MESSAGE value="[notes] token" />
    <MESSAGE value="[notes] 他有自己的有效期" />
    <MESSAGE value="[notes] bug" />
    <MESSAGE value="[notes] 文件修改" />
    <MESSAGE value="[notes] 文件修改及安全验证接口开发" />
    <MESSAGE value="[notes] 删除无用文件" />
    <MESSAGE value="[notes] 增加幂等校验" />
    <MESSAGE value="[notes] 修改安全校验" />
    <MESSAGE value="[picture] 增加系统消息发送及笔记照片放到钓点图片中" />
    <MESSAGE value="[waterStation] generate code" />
    <MESSAGE value="[waterStation] Decryption success" />
    <MESSAGE value="[waterStation] develop" />
    <MESSAGE value="[waterStation] develop done" />
    <MESSAGE value="[waterStation] 重庆江河" />
    <MESSAGE value="[waterStation] 重庆湖库" />
    <MESSAGE value="[team] dev1.1" />
    <MESSAGE value="[team] dev1.1 list" />
    <MESSAGE value="[team] dev1.1 team" />
    <MESSAGE value="[team] dev1.1 team detail comment" />
    <MESSAGE value="[team] dev1.1 team detail" />
    <MESSAGE value="[team] dev1.1 done" />
    <MESSAGE value="[team] dev1.1 附近排序" />
    <option name="LAST_COMMIT_MESSAGE" value="[team] dev1.1 附近排序" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/yudao-module-diaoyuba/yudao-module-diaoyuba-biz/src/main/java/cn/iocoder/yudao/module/diaoyuba/job/WaterLevelChongQingJob.java</url>
          <line>78</line>
          <option name="timeStamp" value="15" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
    <pin-to-top-manager>
      <pinned-members>
        <PinnedItemInfo parentTag="java.util.LinkedHashMap" memberName="head" />
        <PinnedItemInfo parentTag="java.util.LinkedHashMap" memberName="tail" />
      </pinned-members>
    </pin-to-top-manager>
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/ccc_diaoyuba$AppFileController.ic" NAME="AppFileController Coverage Results" MODIFIED="1717579525734" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="idea" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false">
      <FILTER>cn.iocoder.yudao.module.infra.controller.app.file.*</FILTER>
    </SUITE>
    <SUITE FILE_PATH="coverage/ccc_diaoyuba$IPUtils.ic" NAME="IPUtils 覆盖结果" MODIFIED="1716537039361" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="idea" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false">
      <FILTER>cn.iocoder.yudao.framework.ip.core.utils.*</FILTER>
    </SUITE>
  </component>
</project>