package cn.iocoder.yudao.module.diaoyuba.convert.waterstation;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.diaoyuba.controller.app.waterstation.vo.AppWaterStationCreateReqVO;
import cn.iocoder.yudao.module.diaoyuba.controller.app.waterstation.vo.AppWaterStationExcelVO;
import cn.iocoder.yudao.module.diaoyuba.controller.app.waterstation.vo.AppWaterStationRespVO;
import cn.iocoder.yudao.module.diaoyuba.controller.app.waterstation.vo.AppWaterStationSaveReqVO;
import cn.iocoder.yudao.module.diaoyuba.controller.app.waterstation.vo.AppWaterStationUpdateReqVO;
import cn.iocoder.yudao.module.diaoyuba.dal.dataobject.waterstation.WaterStationDO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-21T09:34:05+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_181 (Oracle Corporation)"
)
public class WaterStationConvertImpl implements WaterStationConvert {

    @Override
    public WaterStationDO convert(AppWaterStationCreateReqVO bean) {
        if ( bean == null ) {
            return null;
        }

        WaterStationDO.WaterStationDOBuilder waterStationDO = WaterStationDO.builder();

        waterStationDO.name( bean.getName() );
        waterStationDO.riverName( bean.getRiverName() );
        waterStationDO.drainageBasin( bean.getDrainageBasin() );
        waterStationDO.riverSystem( bean.getRiverSystem() );
        waterStationDO.waterLevel( bean.getWaterLevel() );
        waterStationDO.warningLevel( bean.getWarningLevel() );
        waterStationDO.waterFlow( bean.getWaterFlow() );
        waterStationDO.waterUpdateTime( bean.getWaterUpdateTime() );
        waterStationDO.cityCode( bean.getCityCode() );
        waterStationDO.latitude( bean.getLatitude() );
        waterStationDO.longitude( bean.getLongitude() );
        waterStationDO.address( bean.getAddress() );
        waterStationDO.province( bean.getProvince() );
        waterStationDO.city( bean.getCity() );
        waterStationDO.county( bean.getCounty() );
        waterStationDO.type( bean.getType() );
        waterStationDO.idNo( bean.getIdNo() );

        return waterStationDO.build();
    }

    @Override
    public WaterStationDO convert(AppWaterStationSaveReqVO bean) {
        if ( bean == null ) {
            return null;
        }

        WaterStationDO.WaterStationDOBuilder waterStationDO = WaterStationDO.builder();

        waterStationDO.id( bean.getId() );
        waterStationDO.name( bean.getName() );
        waterStationDO.riverName( bean.getRiverName() );
        waterStationDO.drainageBasin( bean.getDrainageBasin() );
        waterStationDO.riverSystem( bean.getRiverSystem() );
        waterStationDO.waterLevel( bean.getWaterLevel() );
        waterStationDO.warningLevel( bean.getWarningLevel() );
        waterStationDO.waterFlow( bean.getWaterFlow() );
        waterStationDO.waterUpdateTime( bean.getWaterUpdateTime() );
        waterStationDO.cityCode( bean.getCityCode() );
        waterStationDO.latitude( bean.getLatitude() );
        waterStationDO.longitude( bean.getLongitude() );
        waterStationDO.address( bean.getAddress() );
        waterStationDO.province( bean.getProvince() );
        waterStationDO.city( bean.getCity() );
        waterStationDO.county( bean.getCounty() );
        waterStationDO.type( bean.getType() );
        waterStationDO.idNo( bean.getIdNo() );

        return waterStationDO.build();
    }

    @Override
    public WaterStationDO convert(AppWaterStationUpdateReqVO bean) {
        if ( bean == null ) {
            return null;
        }

        WaterStationDO.WaterStationDOBuilder waterStationDO = WaterStationDO.builder();

        waterStationDO.id( bean.getId() );
        waterStationDO.name( bean.getName() );
        waterStationDO.riverName( bean.getRiverName() );
        waterStationDO.drainageBasin( bean.getDrainageBasin() );
        waterStationDO.riverSystem( bean.getRiverSystem() );
        waterStationDO.waterLevel( bean.getWaterLevel() );
        waterStationDO.warningLevel( bean.getWarningLevel() );
        waterStationDO.waterFlow( bean.getWaterFlow() );
        waterStationDO.waterUpdateTime( bean.getWaterUpdateTime() );
        waterStationDO.cityCode( bean.getCityCode() );
        waterStationDO.latitude( bean.getLatitude() );
        waterStationDO.longitude( bean.getLongitude() );
        waterStationDO.address( bean.getAddress() );
        waterStationDO.province( bean.getProvince() );
        waterStationDO.city( bean.getCity() );
        waterStationDO.county( bean.getCounty() );
        waterStationDO.type( bean.getType() );
        waterStationDO.idNo( bean.getIdNo() );

        return waterStationDO.build();
    }

    @Override
    public AppWaterStationRespVO convert(WaterStationDO bean) {
        if ( bean == null ) {
            return null;
        }

        AppWaterStationRespVO appWaterStationRespVO = new AppWaterStationRespVO();

        appWaterStationRespVO.setName( bean.getName() );
        appWaterStationRespVO.setRiverName( bean.getRiverName() );
        appWaterStationRespVO.setWaterLevel( bean.getWaterLevel() );
        appWaterStationRespVO.setWarningLevel( bean.getWarningLevel() );
        appWaterStationRespVO.setWaterFlow( bean.getWaterFlow() );
        appWaterStationRespVO.setWaterUpdateTime( bean.getWaterUpdateTime() );
        appWaterStationRespVO.setCityCode( bean.getCityCode() );
        appWaterStationRespVO.setLatitude( bean.getLatitude() );
        appWaterStationRespVO.setLongitude( bean.getLongitude() );
        appWaterStationRespVO.setAddress( bean.getAddress() );
        appWaterStationRespVO.setProvince( bean.getProvince() );
        appWaterStationRespVO.setCity( bean.getCity() );
        appWaterStationRespVO.setCounty( bean.getCounty() );
        appWaterStationRespVO.setType( bean.getType() );
        appWaterStationRespVO.setDrainageBasin( bean.getDrainageBasin() );
        appWaterStationRespVO.setRiverSystem( bean.getRiverSystem() );
        appWaterStationRespVO.setIdNo( bean.getIdNo() );
        appWaterStationRespVO.setId( bean.getId() );

        return appWaterStationRespVO;
    }

    @Override
    public List<AppWaterStationRespVO> convertList(List<WaterStationDO> list) {
        if ( list == null ) {
            return null;
        }

        List<AppWaterStationRespVO> list1 = new ArrayList<AppWaterStationRespVO>( list.size() );
        for ( WaterStationDO waterStationDO : list ) {
            list1.add( convert( waterStationDO ) );
        }

        return list1;
    }

    @Override
    public PageResult<AppWaterStationRespVO> convertPage(PageResult<WaterStationDO> page) {
        if ( page == null ) {
            return null;
        }

        PageResult<AppWaterStationRespVO> pageResult = new PageResult<AppWaterStationRespVO>();

        pageResult.setList( convertList( page.getList() ) );
        pageResult.setTotal( page.getTotal() );

        return pageResult;
    }

    @Override
    public List<AppWaterStationExcelVO> convertList02(List<WaterStationDO> list) {
        if ( list == null ) {
            return null;
        }

        List<AppWaterStationExcelVO> list1 = new ArrayList<AppWaterStationExcelVO>( list.size() );
        for ( WaterStationDO waterStationDO : list ) {
            list1.add( waterStationDOToAppWaterStationExcelVO( waterStationDO ) );
        }

        return list1;
    }

    protected AppWaterStationExcelVO waterStationDOToAppWaterStationExcelVO(WaterStationDO waterStationDO) {
        if ( waterStationDO == null ) {
            return null;
        }

        AppWaterStationExcelVO appWaterStationExcelVO = new AppWaterStationExcelVO();

        appWaterStationExcelVO.setId( waterStationDO.getId() );
        appWaterStationExcelVO.setName( waterStationDO.getName() );
        appWaterStationExcelVO.setRiverName( waterStationDO.getRiverName() );
        appWaterStationExcelVO.setWaterLevel( waterStationDO.getWaterLevel() );
        appWaterStationExcelVO.setWarningLevel( waterStationDO.getWarningLevel() );
        appWaterStationExcelVO.setWaterFlow( waterStationDO.getWaterFlow() );
        appWaterStationExcelVO.setWaterUpdateTime( waterStationDO.getWaterUpdateTime() );
        appWaterStationExcelVO.setCityCode( waterStationDO.getCityCode() );
        appWaterStationExcelVO.setLatitude( waterStationDO.getLatitude() );
        appWaterStationExcelVO.setLongitude( waterStationDO.getLongitude() );
        appWaterStationExcelVO.setAddress( waterStationDO.getAddress() );
        appWaterStationExcelVO.setProvince( waterStationDO.getProvince() );
        appWaterStationExcelVO.setCity( waterStationDO.getCity() );
        appWaterStationExcelVO.setCounty( waterStationDO.getCounty() );
        appWaterStationExcelVO.setType( waterStationDO.getType() );
        appWaterStationExcelVO.setDrainageBasin( waterStationDO.getDrainageBasin() );
        appWaterStationExcelVO.setRiverSystem( waterStationDO.getRiverSystem() );
        appWaterStationExcelVO.setCreateTime( waterStationDO.getCreateTime() );

        return appWaterStationExcelVO;
    }
}
