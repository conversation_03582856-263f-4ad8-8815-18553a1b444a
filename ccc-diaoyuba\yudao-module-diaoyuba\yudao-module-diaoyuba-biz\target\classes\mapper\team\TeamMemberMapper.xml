<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iocoder.yudao.module.diaoyuba.dal.mysql.team.TeamMemberMapper">

    <!-- 批量更新成员状态 -->
    <update id="updateStatusByTeamId">
        UPDATE diaoyuba_team_member 
        SET status = #{status}, 
            leave_time = NOW(),
            update_time = NOW()
        WHERE team_id = #{teamId} 
          AND deleted = 0
    </update>

    <!-- 更新成员状态 -->
    <update id="updateMemberStatus">
        UPDATE diaoyuba_team_member 
        SET status = #{status}, 
            leave_time = NOW(),
            leave_reason = #{leaveReason},
            update_time = NOW()
        WHERE team_id = #{teamId} 
          AND user_id = #{userId}
          AND deleted = 0
    </update>

</mapper>
