import{ao as r,__tla as _}from"./index-97fffa0c.js";let e,p,a,u,o,l,d,s,c,y,g,i,m=Promise.all([(()=>{try{return _}catch{}})()]).then(async()=>{o=t=>r.post({url:"/product/property/create",data:t}),c=t=>r.put({url:"/product/property/update",data:t}),g=t=>r.delete({url:`/product/property/delete?id=${t}`}),s=t=>r.get({url:`/product/property/get?id=${t}`}),y=t=>r.get({url:"/product/property/page",params:t}),d=t=>r.get({url:"/product/property/list",params:t}),p=t=>r.post({url:"/product/property/get-value-list",data:t}),e=t=>r.get({url:"/product/property/value/page",params:t}),l=t=>r.get({url:`/product/property/value/get?id=${t}`}),a=t=>r.post({url:"/product/property/value/create",data:t}),i=t=>r.put({url:"/product/property/value/update",data:t}),u=t=>r.delete({url:`/product/property/value/delete?id=${t}`})});export{m as __tla,e as a,p as b,a as c,u as d,o as e,l as f,d as g,s as h,c as i,y as j,g as k,i as u};
