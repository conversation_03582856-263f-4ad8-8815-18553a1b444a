import{__tla as _}from"./SkuList.vue_vue_type_script_setup_true_lang-e19721f1.js";let d,h=Promise.all([(()=>{try{return _}catch{}})()]).then(async()=>{d=r=>{var s;const e=[];return r.specType&&((s=r.skus)==null||s.forEach(i=>{var o;(o=i.properties)==null||o.forEach(({propertyId:t,propertyName:m,valueId:l,valueName:c})=>{var u,n;e!=null&&e.some(a=>a.id===t)||e.push({id:t,name:m,values:[]});const p=e==null?void 0:e.findIndex(a=>a.id===t);(u=e[p].values)!=null&&u.some(a=>a.id===l)||((n=e[p].values)==null||n.push({id:l,name:c}))})})),e}});export{h as __tla,d as g};
