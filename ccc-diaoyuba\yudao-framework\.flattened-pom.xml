<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>cn.iocoder.boot</groupId>
    <artifactId>yudao</artifactId>
    <version>1.8.3-snapshot</version>
  </parent>
  <groupId>cn.iocoder.boot</groupId>
  <artifactId>yudao-framework</artifactId>
  <version>1.8.3-snapshot</version>
  <packaging>pom</packaging>
  <description>该包是技术组件，每个子包，代表一个组件。每个组件包括两部分：
            1. core 包：是该组件的核心封装
            2. config 包：是该组件基于 Spring 的配置

        技术组件，也分成两类：
            1. 框架组件：和我们熟悉的 MyBatis、Redis 等等的拓展
            2. 业务组件：和业务相关的组件的封装，例如说数据字典、操作日志等等。
        如果是业务组件，Maven 名字会包含 biz</description>
  <url>https://github.com/YunaiV/ruoyi-vue-pro</url>
  <modules>
    <module>yudao-common</module>
    <module>yudao-spring-boot-starter-banner</module>
    <module>yudao-spring-boot-starter-mybatis</module>
    <module>yudao-spring-boot-starter-redis</module>
    <module>yudao-spring-boot-starter-web</module>
    <module>yudao-spring-boot-starter-security</module>
    <module>yudao-spring-boot-starter-file</module>
    <module>yudao-spring-boot-starter-monitor</module>
    <module>yudao-spring-boot-starter-protection</module>
    <module>yudao-spring-boot-starter-job</module>
    <module>yudao-spring-boot-starter-mq</module>
    <module>yudao-spring-boot-starter-excel</module>
    <module>yudao-spring-boot-starter-test</module>
    <module>yudao-spring-boot-starter-biz-operatelog</module>
    <module>yudao-spring-boot-starter-biz-dict</module>
    <module>yudao-spring-boot-starter-biz-sms</module>
    <module>yudao-spring-boot-starter-biz-pay</module>
    <module>yudao-spring-boot-starter-biz-weixin</module>
    <module>yudao-spring-boot-starter-biz-social</module>
    <module>yudao-spring-boot-starter-biz-tenant</module>
    <module>yudao-spring-boot-starter-biz-data-permission</module>
    <module>yudao-spring-boot-starter-biz-error-code</module>
    <module>yudao-spring-boot-starter-biz-ip</module>
    <module>yudao-spring-boot-starter-flowable</module>
    <module>yudao-spring-boot-starter-captcha</module>
    <module>yudao-spring-boot-starter-websocket</module>
    <module>yudao-spring-boot-starter-desensitize</module>
  </modules>
</project>
