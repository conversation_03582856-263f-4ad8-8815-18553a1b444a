package cn.iocoder.yudao.module.diaoyuba.convert.rating;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.diaoyuba.controller.app.rating.vo.AppRatingCreateReqVO;
import cn.iocoder.yudao.module.diaoyuba.controller.app.rating.vo.AppRatingExcelVO;
import cn.iocoder.yudao.module.diaoyuba.controller.app.rating.vo.AppRatingRespVO;
import cn.iocoder.yudao.module.diaoyuba.controller.app.rating.vo.AppRatingUpdateReqVO;
import cn.iocoder.yudao.module.diaoyuba.dal.dataobject.rating.RatingDO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-21T09:34:05+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_181 (Oracle Corporation)"
)
public class RatingConvertImpl implements RatingConvert {

    @Override
    public RatingDO convert(AppRatingCreateReqVO bean) {
        if ( bean == null ) {
            return null;
        }

        RatingDO.RatingDOBuilder ratingDO = RatingDO.builder();

        ratingDO.userId( bean.getUserId() );
        ratingDO.score( bean.getScore() );
        ratingDO.positionId( bean.getPositionId() );

        return ratingDO.build();
    }

    @Override
    public RatingDO convert(AppRatingUpdateReqVO bean) {
        if ( bean == null ) {
            return null;
        }

        RatingDO.RatingDOBuilder ratingDO = RatingDO.builder();

        ratingDO.id( bean.getId() );
        ratingDO.userId( bean.getUserId() );
        ratingDO.score( bean.getScore() );
        ratingDO.positionId( bean.getPositionId() );

        return ratingDO.build();
    }

    @Override
    public AppRatingRespVO convert(RatingDO bean) {
        if ( bean == null ) {
            return null;
        }

        AppRatingRespVO appRatingRespVO = new AppRatingRespVO();

        appRatingRespVO.setUserId( bean.getUserId() );
        appRatingRespVO.setScore( bean.getScore() );
        appRatingRespVO.setPositionId( bean.getPositionId() );
        appRatingRespVO.setId( bean.getId() );
        appRatingRespVO.setCreateTime( bean.getCreateTime() );

        return appRatingRespVO;
    }

    @Override
    public List<AppRatingRespVO> convertList(List<RatingDO> list) {
        if ( list == null ) {
            return null;
        }

        List<AppRatingRespVO> list1 = new ArrayList<AppRatingRespVO>( list.size() );
        for ( RatingDO ratingDO : list ) {
            list1.add( convert( ratingDO ) );
        }

        return list1;
    }

    @Override
    public PageResult<AppRatingRespVO> convertPage(PageResult<RatingDO> page) {
        if ( page == null ) {
            return null;
        }

        PageResult<AppRatingRespVO> pageResult = new PageResult<AppRatingRespVO>();

        pageResult.setList( convertList( page.getList() ) );
        pageResult.setTotal( page.getTotal() );

        return pageResult;
    }

    @Override
    public List<AppRatingExcelVO> convertList02(List<RatingDO> list) {
        if ( list == null ) {
            return null;
        }

        List<AppRatingExcelVO> list1 = new ArrayList<AppRatingExcelVO>( list.size() );
        for ( RatingDO ratingDO : list ) {
            list1.add( ratingDOToAppRatingExcelVO( ratingDO ) );
        }

        return list1;
    }

    protected AppRatingExcelVO ratingDOToAppRatingExcelVO(RatingDO ratingDO) {
        if ( ratingDO == null ) {
            return null;
        }

        AppRatingExcelVO appRatingExcelVO = new AppRatingExcelVO();

        appRatingExcelVO.setId( ratingDO.getId() );
        appRatingExcelVO.setUserId( ratingDO.getUserId() );
        appRatingExcelVO.setScore( ratingDO.getScore() );
        appRatingExcelVO.setPositionId( ratingDO.getPositionId() );
        appRatingExcelVO.setCreateTime( ratingDO.getCreateTime() );

        return appRatingExcelVO;
    }
}
