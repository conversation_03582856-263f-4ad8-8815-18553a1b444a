package cn.iocoder.yudao.module.diaoyuba.convert.notelike;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.diaoyuba.controller.app.notelike.vo.AppNoteLikeCreateReqVO;
import cn.iocoder.yudao.module.diaoyuba.controller.app.notelike.vo.AppNoteLikeExcelVO;
import cn.iocoder.yudao.module.diaoyuba.controller.app.notelike.vo.AppNoteLikeRespVO;
import cn.iocoder.yudao.module.diaoyuba.controller.app.notelike.vo.AppNoteLikeUpdateReqVO;
import cn.iocoder.yudao.module.diaoyuba.dal.dataobject.notelike.NoteLikeDO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-21T09:34:05+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_181 (Oracle Corporation)"
)
public class NoteLikeConvertImpl implements NoteLikeConvert {

    @Override
    public NoteLikeDO convert(AppNoteLikeCreateReqVO bean) {
        if ( bean == null ) {
            return null;
        }

        NoteLikeDO.NoteLikeDOBuilder noteLikeDO = NoteLikeDO.builder();

        noteLikeDO.noteId( bean.getNoteId() );

        return noteLikeDO.build();
    }

    @Override
    public NoteLikeDO convert(AppNoteLikeUpdateReqVO bean) {
        if ( bean == null ) {
            return null;
        }

        NoteLikeDO.NoteLikeDOBuilder noteLikeDO = NoteLikeDO.builder();

        noteLikeDO.id( bean.getId() );
        noteLikeDO.noteId( bean.getNoteId() );

        return noteLikeDO.build();
    }

    @Override
    public AppNoteLikeRespVO convert(NoteLikeDO bean) {
        if ( bean == null ) {
            return null;
        }

        AppNoteLikeRespVO appNoteLikeRespVO = new AppNoteLikeRespVO();

        appNoteLikeRespVO.setNoteId( bean.getNoteId() );
        appNoteLikeRespVO.setId( bean.getId() );
        appNoteLikeRespVO.setCreateTime( bean.getCreateTime() );

        return appNoteLikeRespVO;
    }

    @Override
    public List<AppNoteLikeRespVO> convertList(List<NoteLikeDO> list) {
        if ( list == null ) {
            return null;
        }

        List<AppNoteLikeRespVO> list1 = new ArrayList<AppNoteLikeRespVO>( list.size() );
        for ( NoteLikeDO noteLikeDO : list ) {
            list1.add( convert( noteLikeDO ) );
        }

        return list1;
    }

    @Override
    public PageResult<AppNoteLikeRespVO> convertPage(PageResult<NoteLikeDO> page) {
        if ( page == null ) {
            return null;
        }

        PageResult<AppNoteLikeRespVO> pageResult = new PageResult<AppNoteLikeRespVO>();

        pageResult.setList( convertList( page.getList() ) );
        pageResult.setTotal( page.getTotal() );

        return pageResult;
    }

    @Override
    public List<AppNoteLikeExcelVO> convertList02(List<NoteLikeDO> list) {
        if ( list == null ) {
            return null;
        }

        List<AppNoteLikeExcelVO> list1 = new ArrayList<AppNoteLikeExcelVO>( list.size() );
        for ( NoteLikeDO noteLikeDO : list ) {
            list1.add( noteLikeDOToAppNoteLikeExcelVO( noteLikeDO ) );
        }

        return list1;
    }

    protected AppNoteLikeExcelVO noteLikeDOToAppNoteLikeExcelVO(NoteLikeDO noteLikeDO) {
        if ( noteLikeDO == null ) {
            return null;
        }

        AppNoteLikeExcelVO appNoteLikeExcelVO = new AppNoteLikeExcelVO();

        appNoteLikeExcelVO.setId( noteLikeDO.getId() );
        appNoteLikeExcelVO.setUserId( noteLikeDO.getUserId() );
        appNoteLikeExcelVO.setNoteId( noteLikeDO.getNoteId() );
        appNoteLikeExcelVO.setCreateTime( noteLikeDO.getCreateTime() );

        return appNoteLikeExcelVO;
    }
}
