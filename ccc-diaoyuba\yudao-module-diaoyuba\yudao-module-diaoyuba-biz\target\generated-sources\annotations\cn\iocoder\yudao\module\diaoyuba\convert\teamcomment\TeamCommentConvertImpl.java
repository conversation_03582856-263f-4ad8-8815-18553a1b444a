package cn.iocoder.yudao.module.diaoyuba.convert.teamcomment;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.diaoyuba.controller.app.teamcomment.vo.AppTeamCommentCreateReqVO;
import cn.iocoder.yudao.module.diaoyuba.controller.app.teamcomment.vo.AppTeamCommentRespVO;
import cn.iocoder.yudao.module.diaoyuba.controller.app.teamcomment.vo.AppTeamSubCommentRespVO;
import cn.iocoder.yudao.module.diaoyuba.dal.dataobject.teamcomment.TeamCommentDO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-21T09:34:05+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_181 (Oracle Corporation)"
)
public class TeamCommentConvertImpl implements TeamCommentConvert {

    @Override
    public TeamCommentDO convert(AppTeamCommentCreateReqVO bean) {
        if ( bean == null ) {
            return null;
        }

        TeamCommentDO.TeamCommentDOBuilder teamCommentDO = TeamCommentDO.builder();

        teamCommentDO.teamId( bean.getTeamId() );
        teamCommentDO.commentContent( bean.getCommentContent() );
        teamCommentDO.commentType( bean.getCommentType() );
        teamCommentDO.replyCommentId( bean.getReplyCommentId() );
        teamCommentDO.replyUserId( bean.getReplyUserId() );
        teamCommentDO.rootCommentId( bean.getRootCommentId() );

        return teamCommentDO.build();
    }

    @Override
    public AppTeamCommentRespVO convert(TeamCommentDO bean) {
        if ( bean == null ) {
            return null;
        }

        AppTeamCommentRespVO appTeamCommentRespVO = new AppTeamCommentRespVO();

        appTeamCommentRespVO.setId( bean.getId() );
        appTeamCommentRespVO.setTeamId( bean.getTeamId() );
        appTeamCommentRespVO.setUserId( bean.getUserId() );
        appTeamCommentRespVO.setCommentContent( bean.getCommentContent() );
        appTeamCommentRespVO.setCommentType( bean.getCommentType() );
        appTeamCommentRespVO.setReplyCommentId( bean.getReplyCommentId() );
        appTeamCommentRespVO.setReplyUserId( bean.getReplyUserId() );
        appTeamCommentRespVO.setRootCommentId( bean.getRootCommentId() );
        appTeamCommentRespVO.setLikeCount( bean.getLikeCount() );
        appTeamCommentRespVO.setSubCommentCount( bean.getSubCommentCount() );
        appTeamCommentRespVO.setCommentIp( bean.getCommentIp() );
        appTeamCommentRespVO.setCityName( bean.getCityName() );
        appTeamCommentRespVO.setImageUrl( bean.getImageUrl() );
        appTeamCommentRespVO.setCreateTime( bean.getCreateTime() );

        appTeamCommentRespVO.setUserInfo( createUserInfo(bean.getUserId()) );
        appTeamCommentRespVO.setReplyUserInfo( createUserInfo(bean.getReplyUserId()) );

        return appTeamCommentRespVO;
    }

    @Override
    public List<AppTeamCommentRespVO> convertList(List<TeamCommentDO> list) {
        if ( list == null ) {
            return null;
        }

        List<AppTeamCommentRespVO> list1 = new ArrayList<AppTeamCommentRespVO>( list.size() );
        for ( TeamCommentDO teamCommentDO : list ) {
            list1.add( convert( teamCommentDO ) );
        }

        return list1;
    }

    @Override
    public PageResult<AppTeamCommentRespVO> convertPage(PageResult<TeamCommentDO> page) {
        if ( page == null ) {
            return null;
        }

        PageResult<AppTeamCommentRespVO> pageResult = new PageResult<AppTeamCommentRespVO>();

        pageResult.setList( convertList( page.getList() ) );
        pageResult.setTotal( page.getTotal() );

        return pageResult;
    }

    @Override
    public AppTeamSubCommentRespVO convertToSub(TeamCommentDO bean) {
        if ( bean == null ) {
            return null;
        }

        AppTeamSubCommentRespVO appTeamSubCommentRespVO = new AppTeamSubCommentRespVO();

        appTeamSubCommentRespVO.setId( bean.getId() );
        appTeamSubCommentRespVO.setTeamId( bean.getTeamId() );
        appTeamSubCommentRespVO.setUserId( bean.getUserId() );
        appTeamSubCommentRespVO.setCommentContent( bean.getCommentContent() );
        appTeamSubCommentRespVO.setCommentType( bean.getCommentType() );
        appTeamSubCommentRespVO.setReplyCommentId( bean.getReplyCommentId() );
        appTeamSubCommentRespVO.setReplyUserId( bean.getReplyUserId() );
        appTeamSubCommentRespVO.setRootCommentId( bean.getRootCommentId() );
        appTeamSubCommentRespVO.setLikeCount( bean.getLikeCount() );
        appTeamSubCommentRespVO.setCommentIp( bean.getCommentIp() );
        appTeamSubCommentRespVO.setCityName( bean.getCityName() );
        appTeamSubCommentRespVO.setImageUrl( bean.getImageUrl() );
        appTeamSubCommentRespVO.setCreateTime( bean.getCreateTime() );

        appTeamSubCommentRespVO.setUserInfo( createSubUserInfo(bean.getUserId()) );
        appTeamSubCommentRespVO.setReplyUserInfo( createSubUserInfo(bean.getReplyUserId()) );

        return appTeamSubCommentRespVO;
    }

    @Override
    public List<AppTeamSubCommentRespVO> convertToSubList(List<TeamCommentDO> list) {
        if ( list == null ) {
            return null;
        }

        List<AppTeamSubCommentRespVO> list1 = new ArrayList<AppTeamSubCommentRespVO>( list.size() );
        for ( TeamCommentDO teamCommentDO : list ) {
            list1.add( convertToSub( teamCommentDO ) );
        }

        return list1;
    }

    @Override
    public PageResult<AppTeamSubCommentRespVO> convertSubPage(PageResult<TeamCommentDO> page) {
        if ( page == null ) {
            return null;
        }

        PageResult<AppTeamSubCommentRespVO> pageResult = new PageResult<AppTeamSubCommentRespVO>();

        pageResult.setList( convertToSubList( page.getList() ) );
        pageResult.setTotal( page.getTotal() );

        return pageResult;
    }
}
