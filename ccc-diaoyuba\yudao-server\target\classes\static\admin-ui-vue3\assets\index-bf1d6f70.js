import{d as j,l as A,r as i,f as B,A as G,O as J,o as d,c as O,i as e,w as n,a,P as x,j as m,B as f,q as y,F as E,T as Q,D as W,G as X,_ as Z,H as $,I as ee,J as ae,K as le,L as te,__tla as re}from"./index-97fffa0c.js";import{_ as oe,__tla as ne}from"./index.vue_vue_type_script_setup_true_lang-d31568cf.js";import{_ as pe,__tla as ce}from"./ContentWrap.vue_vue_type_script_setup_true_lang-a574ccef.js";import{d as b,__tla as se}from"./formatTime-9d54d2c5.js";import{d as ue}from"./download-20922b56.js";import{_ as ie,g as de,d as me,e as _e,__tla as fe}from"./ContractForm.vue_vue_type_script_setup_true_lang-1112d752.js";import{u as ye,__tla as be}from"./useMessage-18385d4a.js";import{__tla as ge}from"./index-8d6db4ce.js";import{__tla as he}from"./el-card-6c7c099d.js";import{__tla as we}from"./Dialog.vue_vue_type_style_index_0_lang-5af0bc2b.js";let S,xe=Promise.all([(()=>{try{return re}catch{}})(),(()=>{try{return ne}catch{}})(),(()=>{try{return ce}catch{}})(),(()=>{try{return se}catch{}})(),(()=>{try{return fe}catch{}})(),(()=>{try{return be}catch{}})(),(()=>{try{return ge}catch{}})(),(()=>{try{return he}catch{}})(),(()=>{try{return we}catch{}})()]).then(async()=>{S=j({name:"Contract",__name:"index",setup(Ie){const I=ye(),{t:T}=A(),V=i(!0),C=i(0),D=i([]),t=B({pageNo:1,pageSize:10,name:null,customerId:null,businessId:null,orderDate:[],no:null,discountPercent:null,productPrice:null}),v=i(),k=i(!1),_=async()=>{V.value=!0;try{const p=await de(t);D.value=p.list,C.value=p.total}finally{V.value=!1}},c=()=>{t.pageNo=1,_()},z=()=>{v.value.resetFields(),c()},U=i(),K=(p,r)=>{U.value.open(p,r)},N=async()=>{try{await I.exportConfirm(),k.value=!0;const p=await _e(t);ue.excel(p,"\u5408\u540C.xls")}catch{}finally{k.value=!1}};return G(()=>{_()}),(p,r)=>{const g=Q,s=W,Y=X,h=Z,u=$,F=ee,P=pe,l=ae,H=le,q=oe,w=J("hasPermi"),L=te;return d(),O(E,null,[e(P,null,{default:n(()=>[e(F,{class:"-mb-15px",model:a(t),ref_key:"queryFormRef",ref:v,inline:!0,"label-width":"68px"},{default:n(()=>[e(s,{label:"\u5408\u540C\u540D\u79F0",prop:"name"},{default:n(()=>[e(g,{modelValue:a(t).name,"onUpdate:modelValue":r[0]||(r[0]=o=>a(t).name=o),placeholder:"\u8BF7\u8F93\u5165\u5408\u540C\u540D\u79F0",clearable:"",onKeyup:x(c,["enter"]),class:"!w-240px"},null,8,["modelValue","onKeyup"])]),_:1}),e(s,{label:"\u5BA2\u6237\u7F16\u53F7",prop:"customerId"},{default:n(()=>[e(g,{modelValue:a(t).customerId,"onUpdate:modelValue":r[1]||(r[1]=o=>a(t).customerId=o),placeholder:"\u8BF7\u8F93\u5165\u5BA2\u6237\u7F16\u53F7",clearable:"",onKeyup:x(c,["enter"]),class:"!w-240px"},null,8,["modelValue","onKeyup"])]),_:1}),e(s,{label:"\u5546\u673A\u7F16\u53F7",prop:"businessId"},{default:n(()=>[e(g,{modelValue:a(t).businessId,"onUpdate:modelValue":r[2]||(r[2]=o=>a(t).businessId=o),placeholder:"\u8BF7\u8F93\u5165\u5546\u673A\u7F16\u53F7",clearable:"",onKeyup:x(c,["enter"]),class:"!w-240px"},null,8,["modelValue","onKeyup"])]),_:1}),e(s,{label:"\u4E0B\u5355\u65E5\u671F",prop:"orderDate"},{default:n(()=>[e(Y,{modelValue:a(t).orderDate,"onUpdate:modelValue":r[3]||(r[3]=o=>a(t).orderDate=o),"value-format":"YYYY-MM-DD HH:mm:ss",type:"daterange","start-placeholder":"\u5F00\u59CB\u65E5\u671F","end-placeholder":"\u7ED3\u675F\u65E5\u671F","default-time":[new Date("1 00:00:00"),new Date("1 23:59:59")],class:"!w-240px"},null,8,["modelValue","default-time"])]),_:1}),e(s,{label:"\u5408\u540C\u7F16\u53F7",prop:"no"},{default:n(()=>[e(g,{modelValue:a(t).no,"onUpdate:modelValue":r[4]||(r[4]=o=>a(t).no=o),placeholder:"\u8BF7\u8F93\u5165\u5408\u540C\u7F16\u53F7",clearable:"",onKeyup:x(c,["enter"]),class:"!w-240px"},null,8,["modelValue","onKeyup"])]),_:1}),e(s,null,{default:n(()=>[e(u,{onClick:c},{default:n(()=>[e(h,{icon:"ep:search",class:"mr-5px"}),m(" \u641C\u7D22")]),_:1}),e(u,{onClick:z},{default:n(()=>[e(h,{icon:"ep:refresh",class:"mr-5px"}),m(" \u91CD\u7F6E")]),_:1}),f((d(),y(u,{type:"primary",onClick:r[5]||(r[5]=o=>K("create"))},{default:n(()=>[e(h,{icon:"ep:plus",class:"mr-5px"}),m(" \u65B0\u589E ")]),_:1})),[[w,["crm:contract:create"]]]),f((d(),y(u,{type:"success",plain:"",onClick:N,loading:a(k)},{default:n(()=>[e(h,{icon:"ep:download",class:"mr-5px"}),m(" \u5BFC\u51FA ")]),_:1},8,["loading"])),[[w,["crm:contract:export"]]])]),_:1})]),_:1},8,["model"])]),_:1}),e(P,null,{default:n(()=>[f((d(),y(H,{data:a(D),stripe:!0,"show-overflow-tooltip":!0},{default:n(()=>[e(l,{label:"\u5408\u540C\u7F16\u53F7",align:"center",prop:"id"}),e(l,{label:"\u5408\u540C\u540D\u79F0",align:"center",prop:"name"}),e(l,{label:"\u5BA2\u6237\u540D\u79F0",align:"center",prop:"customerId"}),e(l,{label:"\u5546\u673A\u540D\u79F0",align:"center",prop:"businessId"}),e(l,{label:"\u5DE5\u4F5C\u6D41\u540D\u79F0",align:"center",prop:"processInstanceId"}),e(l,{label:"\u4E0B\u5355\u65F6\u95F4",align:"center",prop:"orderDate",formatter:a(b),width:"180px"},null,8,["formatter"]),e(l,{label:"\u8D1F\u8D23\u4EBA",align:"center",prop:"ownerUserId"}),e(l,{label:"\u5408\u540C\u7F16\u53F7",align:"center",prop:"no"}),e(l,{label:"\u5F00\u59CB\u65F6\u95F4",align:"center",prop:"startTime",formatter:a(b),width:"180px"},null,8,["formatter"]),e(l,{label:"\u7ED3\u675F\u65F6\u95F4",align:"center",prop:"endTime",formatter:a(b),width:"180px"},null,8,["formatter"]),e(l,{label:"\u5408\u540C\u91D1\u989D",align:"center",prop:"price"}),e(l,{label:"\u6574\u5355\u6298\u6263",align:"center",prop:"discountPercent"}),e(l,{label:"\u4EA7\u54C1\u603B\u91D1\u989D",align:"center",prop:"productPrice"}),e(l,{label:"\u8054\u7CFB\u4EBA",align:"center",prop:"contactId"}),e(l,{label:"\u516C\u53F8\u7B7E\u7EA6\u4EBA",align:"center",prop:"signUserId"}),e(l,{label:"\u6700\u540E\u8DDF\u8FDB\u65F6\u95F4",align:"center",prop:"contactLastTime",formatter:a(b),width:"180px"},null,8,["formatter"]),e(l,{label:"\u521B\u5EFA\u65F6\u95F4",align:"center",prop:"createTime",formatter:a(b),width:"180px"},null,8,["formatter"]),e(l,{label:"\u5907\u6CE8",align:"center",prop:"remark"}),e(l,{label:"\u64CD\u4F5C",width:"120px"},{default:n(o=>[f((d(),y(u,{link:"",type:"primary",onClick:M=>K("update",o.row.id)},{default:n(()=>[m(" \u7F16\u8F91 ")]),_:2},1032,["onClick"])),[[w,["crm:contract:update"]]]),f((d(),y(u,{link:"",type:"danger",onClick:M=>(async R=>{try{await I.delConfirm(),await me(R),I.success(T("common.delSuccess")),await _()}catch{}})(o.row.id)},{default:n(()=>[m(" \u5220\u9664 ")]),_:2},1032,["onClick"])),[[w,["crm:contract:delete"]]])]),_:1})]),_:1},8,["data"])),[[L,a(V)]]),e(q,{total:a(C),page:a(t).pageNo,"onUpdate:page":r[6]||(r[6]=o=>a(t).pageNo=o),limit:a(t).pageSize,"onUpdate:limit":r[7]||(r[7]=o=>a(t).pageSize=o),onPagination:_},null,8,["total","page","limit"])]),_:1}),e(ie,{ref_key:"formRef",ref:U,onSuccess:_},null,512)],64)}}})});export{xe as __tla,S as default};
