package cn.iocoder.yudao.module.diaoyuba.convert.usercollection;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.diaoyuba.controller.app.usercollection.vo.AppUserCollectionCreateReqVO;
import cn.iocoder.yudao.module.diaoyuba.controller.app.usercollection.vo.AppUserCollectionExcelVO;
import cn.iocoder.yudao.module.diaoyuba.controller.app.usercollection.vo.AppUserCollectionRespVO;
import cn.iocoder.yudao.module.diaoyuba.controller.app.usercollection.vo.AppUserCollectionUpdateReqVO;
import cn.iocoder.yudao.module.diaoyuba.dal.dataobject.usercollection.UserCollectionDO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-21T09:34:05+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_181 (Oracle Corporation)"
)
public class UserCollectionConvertImpl implements UserCollectionConvert {

    @Override
    public UserCollectionDO convert(AppUserCollectionCreateReqVO bean) {
        if ( bean == null ) {
            return null;
        }

        UserCollectionDO.UserCollectionDOBuilder userCollectionDO = UserCollectionDO.builder();

        userCollectionDO.userId( bean.getUserId() );
        userCollectionDO.positionId( bean.getPositionId() );

        return userCollectionDO.build();
    }

    @Override
    public UserCollectionDO convert(AppUserCollectionUpdateReqVO bean) {
        if ( bean == null ) {
            return null;
        }

        UserCollectionDO.UserCollectionDOBuilder userCollectionDO = UserCollectionDO.builder();

        userCollectionDO.id( bean.getId() );
        userCollectionDO.userId( bean.getUserId() );
        userCollectionDO.positionId( bean.getPositionId() );

        return userCollectionDO.build();
    }

    @Override
    public AppUserCollectionRespVO convert(UserCollectionDO bean) {
        if ( bean == null ) {
            return null;
        }

        AppUserCollectionRespVO appUserCollectionRespVO = new AppUserCollectionRespVO();

        appUserCollectionRespVO.setUserId( bean.getUserId() );
        appUserCollectionRespVO.setPositionId( bean.getPositionId() );
        appUserCollectionRespVO.setId( bean.getId() );
        appUserCollectionRespVO.setCreateTime( bean.getCreateTime() );

        return appUserCollectionRespVO;
    }

    @Override
    public List<AppUserCollectionRespVO> convertList(List<UserCollectionDO> list) {
        if ( list == null ) {
            return null;
        }

        List<AppUserCollectionRespVO> list1 = new ArrayList<AppUserCollectionRespVO>( list.size() );
        for ( UserCollectionDO userCollectionDO : list ) {
            list1.add( convert( userCollectionDO ) );
        }

        return list1;
    }

    @Override
    public PageResult<AppUserCollectionRespVO> convertPage(PageResult<UserCollectionDO> page) {
        if ( page == null ) {
            return null;
        }

        PageResult<AppUserCollectionRespVO> pageResult = new PageResult<AppUserCollectionRespVO>();

        pageResult.setList( convertList( page.getList() ) );
        pageResult.setTotal( page.getTotal() );

        return pageResult;
    }

    @Override
    public List<AppUserCollectionExcelVO> convertList02(List<UserCollectionDO> list) {
        if ( list == null ) {
            return null;
        }

        List<AppUserCollectionExcelVO> list1 = new ArrayList<AppUserCollectionExcelVO>( list.size() );
        for ( UserCollectionDO userCollectionDO : list ) {
            list1.add( userCollectionDOToAppUserCollectionExcelVO( userCollectionDO ) );
        }

        return list1;
    }

    protected AppUserCollectionExcelVO userCollectionDOToAppUserCollectionExcelVO(UserCollectionDO userCollectionDO) {
        if ( userCollectionDO == null ) {
            return null;
        }

        AppUserCollectionExcelVO appUserCollectionExcelVO = new AppUserCollectionExcelVO();

        appUserCollectionExcelVO.setId( userCollectionDO.getId() );
        appUserCollectionExcelVO.setUserId( userCollectionDO.getUserId() );
        appUserCollectionExcelVO.setPositionId( userCollectionDO.getPositionId() );
        appUserCollectionExcelVO.setCreateTime( userCollectionDO.getCreateTime() );

        return appUserCollectionExcelVO;
    }
}
