#!/bin/bash

# INFO日志输出测试脚本

echo "=========================================="
echo "INFO日志输出测试"
echo "=========================================="

USER_HOME=$(echo ~)
LOG_FILE="$USER_HOME/logs/yudao-server.log"

echo "1. 检查日志文件位置:"
echo "   日志文件: $LOG_FILE"

if [ -f "$LOG_FILE" ]; then
    echo "   ✅ 日志文件存在"
    echo "   文件大小: $(du -h "$LOG_FILE" | cut -f1)"
    echo "   最后修改: $(stat -c %y "$LOG_FILE" 2>/dev/null | cut -d'.' -f1)"
else
    echo "   ❌ 日志文件不存在"
    echo "   请确保应用已启动并运行一段时间"
    exit 1
fi

echo ""
echo "2. 检查日志级别分布:"

# 统计各级别日志数量
INFO_COUNT=$(grep -c " INFO " "$LOG_FILE" 2>/dev/null || echo "0")
WARN_COUNT=$(grep -c " WARN " "$LOG_FILE" 2>/dev/null || echo "0")
ERROR_COUNT=$(grep -c " ERROR " "$LOG_FILE" 2>/dev/null || echo "0")
DEBUG_COUNT=$(grep -c " DEBUG " "$LOG_FILE" 2>/dev/null || echo "0")

echo "   INFO 日志数量: $INFO_COUNT"
echo "   WARN 日志数量: $WARN_COUNT"
echo "   ERROR 日志数量: $ERROR_COUNT"
echo "   DEBUG 日志数量: $DEBUG_COUNT"

if [ $INFO_COUNT -eq 0 ]; then
    echo "   ❌ 没有发现INFO级别日志！"
else
    echo "   ✅ 发现 $INFO_COUNT 条INFO日志"
fi

echo ""
echo "3. 最近的INFO日志示例:"
if [ $INFO_COUNT -gt 0 ]; then
    echo "   最新的5条INFO日志:"
    grep " INFO " "$LOG_FILE" | tail -5 | while read line; do
        echo "   $line"
    done
else
    echo "   ❌ 没有INFO日志"
fi

echo ""
echo "4. 检查日志格式:"
RECENT_LOGS=$(tail -10 "$LOG_FILE")
TIME_FORMAT_COUNT=$(echo "$RECENT_LOGS" | grep -c "^[0-9]\{4\}-[0-9]\{2\}-[0-9]\{2\} [0-9]\{2\}:[0-9]\{2\}:[0-9]\{2\}")
TOTAL_LINES=$(echo "$RECENT_LOGS" | wc -l)

if [ $TIME_FORMAT_COUNT -gt 0 ]; then
    echo "   ✅ 时间格式正确 ($TIME_FORMAT_COUNT/$TOTAL_LINES 行)"
else
    echo "   ❌ 时间格式错误"
fi

echo ""
echo "5. 应用启动相关日志:"
if grep -q "Started.*Application" "$LOG_FILE"; then
    echo "   ✅ 发现应用启动日志"
    grep "Started.*Application" "$LOG_FILE" | tail -1
else
    echo "   ⚠️ 未发现应用启动日志"
fi

echo ""
echo "6. 业务日志检查:"
# 检查是否有业务相关的日志
BUSINESS_LOGS=$(grep -i "diaoyuba\|team\|controller\|service" "$LOG_FILE" | wc -l)
echo "   业务相关日志数量: $BUSINESS_LOGS"

if [ $BUSINESS_LOGS -gt 0 ]; then
    echo "   最近的业务日志:"
    grep -i "diaoyuba\|team\|controller\|service" "$LOG_FILE" | tail -3 | while read line; do
        echo "   $line"
    done
fi

echo ""
echo "7. 实时监控命令:"
echo "   实时查看所有日志: tail -f $LOG_FILE"
echo "   只看INFO日志: tail -f $LOG_FILE | grep ' INFO '"
echo "   搜索特定内容: grep '关键词' $LOG_FILE"

echo ""
echo "8. 问题诊断:"
if [ $INFO_COUNT -eq 0 ]; then
    echo "   ❌ INFO日志缺失，可能的原因:"
    echo "      1. 日志级别配置错误"
    echo "      2. 应用没有产生INFO级别日志"
    echo "      3. 日志配置文件有问题"
    echo ""
    echo "   建议检查:"
    echo "      1. 重启应用: kill \$(pgrep -f yudao-server.jar) && 重新启动"
    echo "      2. 检查应用是否正常运行: ps aux | grep yudao-server.jar"
    echo "      3. 检查启动日志是否有错误"
else
    echo "   ✅ INFO日志正常输出"
fi

echo ""
echo "=========================================="
echo "测试完成"
echo "=========================================="
