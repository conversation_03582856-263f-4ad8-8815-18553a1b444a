import{ao as t,__tla as o}from"./index-97fffa0c.js";let c,e,s,u,l,n,r,p,m=Promise.all([(()=>{try{return o}catch{}})()]).then(async()=>{s=async a=>await t.post({url:"/mp/account/create",data:a}),p=async a=>t.put({url:"/mp/account/update",data:a}),u=async a=>t.delete({url:"/mp/account/delete?id="+a,method:"delete"}),r=async a=>t.get({url:"/mp/account/get?id="+a}),c=async a=>t.get({url:"/mp/account/page",params:a}),n=async()=>t.get({url:"/mp/account/list-all-simple"}),e=async a=>t.put({url:"/mp/account/generate-qr-code?id="+a}),l=async a=>t.put({url:"/mp/account/clear-quota?id="+a})});export{m as __tla,c as a,e as b,s as c,u as d,l as e,n as f,r as g,p as u};
