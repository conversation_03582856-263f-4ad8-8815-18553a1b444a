#!/bin/bash

# 高级网络测试脚本 - 深度分析网络连接问题

TARGET_DOMAIN="xxfb.mwr.cn"
TARGET_URL="http://$TARGET_DOMAIN"

echo "=========================================="
echo "高级网络连接分析"
echo "=========================================="

echo "1. 多DNS服务器解析测试:"
DNS_SERVERS=("*******" "***************" "*******" "*********")
for dns in "${DNS_SERVERS[@]}"; do
    echo "   使用DNS $dns:"
    nslookup $TARGET_DOMAIN $dns 2>&1 | grep -E "Address|Non-authoritative" | head -3
    echo ""
done

echo "2. 网络路径追踪:"
echo "   traceroute到目标域名:"
traceroute $TARGET_DOMAIN 2>&1 | head -15
echo ""

echo "3. 端口连通性测试:"
echo "   测试80端口:"
timeout 10 telnet $TARGET_DOMAIN 80 2>&1 | head -5
echo ""
echo "   使用nc测试80端口:"
timeout 10 nc -zv $TARGET_DOMAIN 80 2>&1
echo ""

echo "4. 不同协议测试:"
echo "   HTTP请求 (详细):"
curl -v --connect-timeout 10 --max-time 30 $TARGET_URL 2>&1 | head -20
echo ""

echo "5. 获取真实IP地址:"
echo "   从多个来源获取IP:"
# 方法1: dig
if command -v dig > /dev/null 2>&1; then
    echo "   dig结果:"
    dig $TARGET_DOMAIN A +short
fi

# 方法2: host
if command -v host > /dev/null 2>&1; then
    echo "   host结果:"
    host $TARGET_DOMAIN | grep "has address"
fi

# 方法3: nslookup
echo "   nslookup结果:"
nslookup $TARGET_DOMAIN | grep -A1 "Name:" | grep "Address"
echo ""

echo "6. 网络接口和路由检查:"
echo "   默认路由:"
ip route show default 2>/dev/null || route -n | grep "^0.0.0.0"
echo ""
echo "   网络接口状态:"
ip link show 2>/dev/null | grep -E "UP|DOWN" || ifconfig | grep -E "UP|DOWN"
echo ""

echo "7. 防火墙详细检查:"
echo "   iptables OUTPUT链:"
iptables -L OUTPUT -n -v 2>/dev/null | head -10
echo ""
echo "   iptables FORWARD链:"
iptables -L FORWARD -n -v 2>/dev/null | head -10
echo ""

echo "8. 系统网络配置:"
echo "   /etc/hosts内容:"
grep -v "^#" /etc/hosts | grep -v "^$"
echo ""
echo "   /etc/resolv.conf内容:"
cat /etc/resolv.conf
echo ""

echo "9. 进程网络使用情况:"
echo "   当前网络连接:"
netstat -tuln | grep ":80\|:443\|:53" | head -10
echo ""

echo "10. 测试其他政府网站:"
echo "    测试其他.gov.cn域名:"
TEST_DOMAINS=("www.gov.cn" "www.mwr.gov.cn" "www.cma.gov.cn")
for domain in "${TEST_DOMAINS[@]}"; do
    echo "    测试 $domain:"
    timeout 5 curl -I http://$domain 2>&1 | head -2
done
echo ""

echo "11. 网络代理检查:"
echo "    环境变量:"
env | grep -i proxy
echo ""
echo "    系统代理配置:"
if [ -f /etc/environment ]; then
    grep -i proxy /etc/environment
fi
echo ""

echo "12. 运营商和地理位置:"
echo "    本机公网IP:"
curl -s --connect-timeout 5 ifconfig.me 2>/dev/null || curl -s --connect-timeout 5 ipinfo.io/ip 2>/dev/null || echo "无法获取公网IP"
echo ""
echo "    IP地理位置:"
curl -s --connect-timeout 5 ipinfo.io 2>/dev/null | head -10 || echo "无法获取地理位置信息"
echo ""

echo "13. 建议的解决方案:"
echo "    方案1 - 添加hosts映射:"
echo "      1. 在本地获取IP: nslookup $TARGET_DOMAIN"
echo "      2. 在服务器添加: echo 'IP地址 $TARGET_DOMAIN' >> /etc/hosts"
echo ""
echo "    方案2 - 使用代理:"
echo "      1. 配置HTTP代理"
echo "      2. 使用VPN连接"
echo ""
echo "    方案3 - 更换DNS:"
echo "      echo 'nameserver *******' > /etc/resolv.conf"
echo "      echo 'nameserver ***************' >> /etc/resolv.conf"
echo ""
echo "    方案4 - 联系运营商:"
echo "      如果是运营商限制，需要联系解除"
echo ""

echo "=========================================="
echo "高级测试完成"
echo "=========================================="
