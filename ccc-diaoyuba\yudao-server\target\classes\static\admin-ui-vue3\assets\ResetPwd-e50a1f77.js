import{_ as t,__tla as r}from"./ResetPwd.vue_vue_type_script_setup_true_lang-343723cf.js";import{__tla as _}from"./index-97fffa0c.js";import{__tla as a}from"./XButton-dd4d8780.js";import"./_plugin-vue_export-helper-1b428a4d.js";import{__tla as l}from"./InputPassword-8eb3866f.js";import{__tla as o}from"./profile-9d2d9ae0.js";import{__tla as m}from"./useMessage-18385d4a.js";let c=Promise.all([(()=>{try{return r}catch{}})(),(()=>{try{return _}catch{}})(),(()=>{try{return a}catch{}})(),(()=>{try{return l}catch{}})(),(()=>{try{return o}catch{}})(),(()=>{try{return m}catch{}})()]).then(async()=>{});export{c as __tla,t as default};
