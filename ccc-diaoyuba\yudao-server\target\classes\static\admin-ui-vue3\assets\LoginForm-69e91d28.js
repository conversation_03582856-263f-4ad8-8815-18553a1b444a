import{d as re,r as u,u as ie,a1 as se,b as de,a as t,f as pe,ax as ce,A as ue,B as me,a2 as fe,o as b,q as P,w as a,i as e,a3 as ge,P as _e,j as p,t as F,g as j,c as he,k as ye,F as we,l as xe,a4 as be,a8 as Fe,a9 as ve,aa as ke,ab as Ve,a6 as De,D as Ee,E as Ne,T as Ce,ac as Le,n as Re,m as Ie,I as Me,ad as S,ae as U,ay as Pe,_ as je,__tla as Se}from"./index-97fffa0c.js";import{_ as Ue,__tla as ze}from"./Verify-903d1672.js";import{_ as Oe,__tla as Te}from"./XButton-dd4d8780.js";import{E as Ae,__tla as qe}from"./el-link-f00f9c89.js";import{u as Be,L as _,_ as Ge,a as Qe,__tla as $e}from"./LoginFormTitle.vue_vue_type_script_setup_true_lang-f56773d4.js";import{u as v,__tla as Ke}from"./useIcon-4b1d730a.js";import{u as We,__tla as He}from"./useMessage-18385d4a.js";import{r as k,__tla as Je}from"./formRules-8010a921.js";import{_ as Xe}from"./_plugin-vue_export-helper-1b428a4d.js";let z,Ye=Promise.all([(()=>{try{return Se}catch{}})(),(()=>{try{return ze}catch{}})(),(()=>{try{return Te}catch{}})(),(()=>{try{return qe}catch{}})(),(()=>{try{return $e}catch{}})(),(()=>{try{return Ke}catch{}})(),(()=>{try{return He}catch{}})(),(()=>{try{return Je}catch{}})()]).then(async()=>{let V,D;V={class:"w-[100%] flex justify-between"},D={class:"w-[100%] flex justify-between"},z=Xe(re({name:"LoginForm",__name:"LoginForm",setup(Ze){const{t:s}=xe(),E=We(),O=v({icon:"ep:house"}),T=v({icon:"ep:avatar"}),A=v({icon:"ep:lock"}),N=u(),{validForm:q}=Qe(N),{setLoginState:h,getLoginState:B}=Be(),{currentRoute:G,push:Q}=ie(),$=se(),c=u(""),f=u(!1),C=u(),K=u("blockPuzzle"),W=de(()=>t(B)===_.LOGIN),H={tenantName:[k],username:[k],password:[k]},n=pe({isShowPassword:!1,captchaEnable:"true",tenantEnable:"true",loginForm:{tenantName:"\u828B\u9053\u6E90\u7801",username:"admin",password:"admin123",captchaVerification:"",rememberMe:!1}}),J=[{icon:"ant-design:github-filled",type:0},{icon:"ant-design:wechat-filled",type:30},{icon:"ant-design:alipay-circle-filled",type:0},{icon:"ant-design:dingtalk-circle-filled",type:20}],L=async()=>{n.captchaEnable==="false"?await I({}):C.value.show()},R=u(),I=async o=>{f.value=!0;try{if(await(async()=>{if(n.tenantEnable==="true"){const d=await S(n.loginForm.tenantName);U(d)}})(),!await q())return;n.loginForm.captchaVerification=o.captchaVerification;const l=await Fe(n.loginForm);if(!l)return;R.value=ve.service({lock:!0,text:"\u6B63\u5728\u52A0\u8F7D\u7CFB\u7EDF\u4E2D...",background:"rgba(0, 0, 0, 0.7)"}),n.loginForm.rememberMe?ke(n.loginForm):Ve(),De(l),c.value||(c.value="/"),c.value.indexOf("sso")!==-1?window.location.href=window.location.href.replace("/login?redirect=",""):Q({path:c.value||$.addRouters[0].path})}finally{f.value=!1,R.value.close()}};return ce(()=>G.value,o=>{var l;c.value=(l=o==null?void 0:o.query)==null?void 0:l.redirect},{immediate:!0}),ue(()=>{(()=>{const o=be();o&&(n.loginForm={...n.loginForm,username:o.username?o.username:n.loginForm.username,password:o.password?o.password:n.loginForm.password,rememberMe:!!o.rememberMe,tenantName:o.tenantName?o.tenantName:n.loginForm.tenantName})})()}),(o,l)=>{const d=Ee,i=Ne,y=Ce,X=Le,m=Ae,w=Re,g=Oe,Y=Ue,M=Ie,Z=je,ee=Me;return me((b(),P(ee,{ref_key:"formLogin",ref:N,model:t(n).loginForm,rules:H,class:"login-form","label-position":"top","label-width":"120px",size:"large"},{default:a(()=>[e(w,{style:{"margin-right":"-10px","margin-left":"-10px"}},{default:a(()=>[e(i,{span:24,style:{"padding-right":"10px","padding-left":"10px"}},{default:a(()=>[e(d,null,{default:a(()=>[e(Ge,{style:{width:"100%"}})]),_:1})]),_:1}),e(i,{span:24,style:{"padding-right":"10px","padding-left":"10px"}},{default:a(()=>[t(n).tenantEnable==="true"?(b(),P(d,{key:0,prop:"tenantName"},{default:a(()=>[e(y,{modelValue:t(n).loginForm.tenantName,"onUpdate:modelValue":l[0]||(l[0]=r=>t(n).loginForm.tenantName=r),placeholder:t(s)("login.tenantNamePlaceholder"),"prefix-icon":t(O),link:"",type:"primary"},null,8,["modelValue","placeholder","prefix-icon"])]),_:1})):ge("",!0)]),_:1}),e(i,{span:24,style:{"padding-right":"10px","padding-left":"10px"}},{default:a(()=>[e(d,{prop:"username"},{default:a(()=>[e(y,{modelValue:t(n).loginForm.username,"onUpdate:modelValue":l[1]||(l[1]=r=>t(n).loginForm.username=r),placeholder:t(s)("login.usernamePlaceholder"),"prefix-icon":t(T)},null,8,["modelValue","placeholder","prefix-icon"])]),_:1})]),_:1}),e(i,{span:24,style:{"padding-right":"10px","padding-left":"10px"}},{default:a(()=>[e(d,{prop:"password"},{default:a(()=>[e(y,{modelValue:t(n).loginForm.password,"onUpdate:modelValue":l[2]||(l[2]=r=>t(n).loginForm.password=r),placeholder:t(s)("login.passwordPlaceholder"),"prefix-icon":t(A),"show-password":"",type:"password",onKeyup:l[3]||(l[3]=_e(r=>L(),["enter"]))},null,8,["modelValue","placeholder","prefix-icon"])]),_:1})]),_:1}),e(i,{span:24,style:{"padding-right":"10px","padding-left":"10px","margin-top":"-20px","margin-bottom":"-20px"}},{default:a(()=>[e(d,null,{default:a(()=>[e(w,{justify:"space-between",style:{width:"100%"}},{default:a(()=>[e(i,{span:6},{default:a(()=>[e(X,{modelValue:t(n).loginForm.rememberMe,"onUpdate:modelValue":l[4]||(l[4]=r=>t(n).loginForm.rememberMe=r)},{default:a(()=>[p(F(t(s)("login.remember")),1)]),_:1},8,["modelValue"])]),_:1}),e(i,{offset:6,span:12},{default:a(()=>[e(m,{style:{float:"right"},type:"primary"},{default:a(()=>[p(F(t(s)("login.forgetPassword")),1)]),_:1})]),_:1})]),_:1})]),_:1})]),_:1}),e(i,{span:24,style:{"padding-right":"10px","padding-left":"10px"}},{default:a(()=>[e(d,null,{default:a(()=>[e(g,{loading:t(f),title:t(s)("login.login"),class:"w-[100%]",type:"primary",onClick:l[5]||(l[5]=r=>L())},null,8,["loading","title"])]),_:1})]),_:1}),e(Y,{ref_key:"verify",ref:C,captchaType:t(K),imgSize:{width:"400px",height:"200px"},mode:"pop",onSuccess:I},null,8,["captchaType"]),e(i,{span:24,style:{"padding-right":"10px","padding-left":"10px"}},{default:a(()=>[e(d,null,{default:a(()=>[e(w,{gutter:5,justify:"space-between",style:{width:"100%"}},{default:a(()=>[e(i,{span:8},{default:a(()=>[e(g,{title:t(s)("login.btnMobile"),class:"w-[100%]",onClick:l[6]||(l[6]=r=>t(h)(t(_).MOBILE))},null,8,["title"])]),_:1}),e(i,{span:8},{default:a(()=>[e(g,{title:t(s)("login.btnQRCode"),class:"w-[100%]",onClick:l[7]||(l[7]=r=>t(h)(t(_).QR_CODE))},null,8,["title"])]),_:1}),e(i,{span:8},{default:a(()=>[e(g,{title:t(s)("login.btnRegister"),class:"w-[100%]",onClick:l[8]||(l[8]=r=>t(h)(t(_).REGISTER))},null,8,["title"])]),_:1})]),_:1})]),_:1})]),_:1}),e(M,{"content-position":"center"},{default:a(()=>[p(F(t(s)("login.otherLogin")),1)]),_:1}),e(i,{span:24,style:{"padding-right":"10px","padding-left":"10px"}},{default:a(()=>[e(d,null,{default:a(()=>[j("div",V,[(b(),he(we,null,ye(J,(r,ae)=>e(Z,{key:ae,icon:r.icon,size:30,class:"anticon cursor-pointer",color:"#999",onClick:ea=>(async x=>{if(x===0)E.error("\u6B64\u65B9\u5F0F\u672A\u914D\u7F6E");else{f.value=!0,n.tenantEnable==="true"&&await E.prompt("\u8BF7\u8F93\u5165\u79DF\u6237\u540D\u79F0",s("common.reminder")).then(async({value:ne})=>{const oe=await S(ne);U(oe)});const te=location.origin+"/social-login?"+encodeURIComponent(`type=${x}&redirect=${c.value||"/"}`),le=await Pe(x,encodeURIComponent(te));window.location.href=le}})(r.type)},null,8,["icon","onClick"])),64))])]),_:1})]),_:1}),e(M,{"content-position":"center"},{default:a(()=>[p("\u840C\u65B0\u5FC5\u8BFB")]),_:1}),e(i,{span:24,style:{"padding-right":"10px","padding-left":"10px"}},{default:a(()=>[e(d,null,{default:a(()=>[j("div",D,[e(m,{href:"https://doc.iocoder.cn/",target:"_blank"},{default:a(()=>[p("\u{1F4DA}\u5F00\u53D1\u6307\u5357")]),_:1}),e(m,{href:"https://doc.iocoder.cn/video/",target:"_blank"},{default:a(()=>[p("\u{1F525}\u89C6\u9891\u6559\u7A0B")]),_:1}),e(m,{href:"https://www.iocoder.cn/Interview/good-collection/",target:"_blank"},{default:a(()=>[p(" \u26A1\u9762\u8BD5\u624B\u518C ")]),_:1}),e(m,{href:"http://static.yudao.iocoder.cn/mp/Aix9975.jpeg",target:"_blank"},{default:a(()=>[p(" \u{1F91D}\u5916\u5305\u54A8\u8BE2 ")]),_:1})])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])),[[fe,t(W)]])}}}),[["__scopeId","data-v-d08be094"]])});export{Ye as __tla,z as default};
