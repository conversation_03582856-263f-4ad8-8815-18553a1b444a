<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>cn.iocoder.boot</groupId>
  <artifactId>yudao-dependencies</artifactId>
  <version>1.8.3-snapshot</version>
  <packaging>pom</packaging>
  <name>${project.artifactId}</name>
  <description>基础 bom 文件，管理整个项目的依赖版本</description>
  <url>https://github.com/YunaiV/ruoyi-vue-pro</url>
  <properties>
    <mybatis-plus-join.version>1.4.7</mybatis-plus-join.version>
    <podam.version>7.2.11.RELEASE</podam.version>
    <velocity.version>2.3</velocity.version>
    <spring.boot.version>2.7.17</spring.boot.version>
    <tika-core.version>2.7.0</tika-core.version>
    <lock4j.version>2.2.5</lock4j.version>
    <okio.version>3.5.0</okio.version>
    <guice.version>5.1.0</guice.version>
    <lombok.version>1.18.30</lombok.version>
    <xercesImpl.version>2.12.2</xercesImpl.version>
    <jsoup.version>1.16.2</jsoup.version>
    <fastjson.version>1.2.83</fastjson.version>
    <commons-net.version>3.10.0</commons-net.version>
    <hutool.version>5.8.22</hutool.version>
    <resilience4j.version>1.7.1</resilience4j.version>
    <transmittable-thread-local.version>2.14.2</transmittable-thread-local.version>
    <flowable.version>6.8.0</flowable.version>
    <redisson.version>3.18.0</redisson.version>
    <okhttp3.version>4.11.0</okhttp3.version>
    <mybatis-plus.version>3.5.4</mybatis-plus.version>
    <druid.version>1.2.20</druid.version>
    <commons-io.version>2.11.0</commons-io.version>
    <easyexcel.verion>3.3.2</easyexcel.verion>
    <springdoc.version>1.6.15</springdoc.version>
    <minio.version>8.5.6</minio.version>
    <mockito-inline.version>4.11.0</mockito-inline.version>
    <jedis-mock.version>1.0.7</jedis-mock.version>
    <tencentcloud-sdk-java.version>3.1.880</tencentcloud-sdk-java.version>
    <dm8.jdbc.version>********</dm8.jdbc.version>
    <rocketmq-spring.version>2.2.3</rocketmq-spring.version>
    <ip2region.version>2.7.0</ip2region.version>
    <jsch.version>0.1.55</jsch.version>
    <weixin-java.version>4.5.0</weixin-java.version>
    <aliyun-java-sdk-core.version>4.6.4</aliyun-java-sdk-core.version>
    <captcha-plus.version>1.0.10</captcha-plus.version>
    <dynamic-datasource.version>3.6.1</dynamic-datasource.version>
    <servlet.versoin>2.5</servlet.versoin>
    <knife4j.version>4.3.0</knife4j.version>
    <spring-boot-admin.version>2.7.11</spring-boot-admin.version>
    <mapstruct.version>1.5.5.Final</mapstruct.version>
    <jimureport.version>1.6.1</jimureport.version>
    <aliyun-java-sdk-dysmsapi.version>2.2.1</aliyun-java-sdk-dysmsapi.version>
    <justauth.version>1.0.7</justauth.version>
    <screw.version>1.0.5</screw.version>
    <guava.version>32.1.3-jre</guava.version>
    <skywalking.version>8.12.0</skywalking.version>
    <opentracing.version>0.33.0</opentracing.version>
    <mybatis-plus-generator.version>3.5.4</mybatis-plus-generator.version>
    <flatten-maven-plugin.version>1.5.0</flatten-maven-plugin.version>
    <revision>1.8.3-snapshot</revision>
  </properties>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-dependencies</artifactId>
        <version>${spring.boot.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>cn.iocoder.boot</groupId>
        <artifactId>yudao-spring-boot-starter-banner</artifactId>
        <version>1.8.3-snapshot</version>
      </dependency>
      <dependency>
        <groupId>cn.iocoder.boot</groupId>
        <artifactId>yudao-spring-boot-starter-biz-operatelog</artifactId>
        <version>1.8.3-snapshot</version>
      </dependency>
      <dependency>
        <groupId>cn.iocoder.boot</groupId>
        <artifactId>yudao-spring-boot-starter-biz-dict</artifactId>
        <version>1.8.3-snapshot</version>
      </dependency>
      <dependency>
        <groupId>cn.iocoder.boot</groupId>
        <artifactId>yudao-spring-boot-starter-biz-sms</artifactId>
        <version>1.8.3-snapshot</version>
      </dependency>
      <dependency>
        <groupId>cn.iocoder.boot</groupId>
        <artifactId>yudao-spring-boot-starter-biz-pay</artifactId>
        <version>1.8.3-snapshot</version>
      </dependency>
      <dependency>
        <groupId>cn.iocoder.boot</groupId>
        <artifactId>yudao-spring-boot-starter-biz-weixin</artifactId>
        <version>1.8.3-snapshot</version>
      </dependency>
      <dependency>
        <groupId>cn.iocoder.boot</groupId>
        <artifactId>yudao-spring-boot-starter-biz-tenant</artifactId>
        <version>1.8.3-snapshot</version>
      </dependency>
      <dependency>
        <groupId>cn.iocoder.boot</groupId>
        <artifactId>yudao-spring-boot-starter-biz-data-permission</artifactId>
        <version>1.8.3-snapshot</version>
      </dependency>
      <dependency>
        <groupId>cn.iocoder.boot</groupId>
        <artifactId>yudao-spring-boot-starter-biz-social</artifactId>
        <version>1.8.3-snapshot</version>
      </dependency>
      <dependency>
        <groupId>cn.iocoder.boot</groupId>
        <artifactId>yudao-spring-boot-starter-biz-error-code</artifactId>
        <version>1.8.3-snapshot</version>
      </dependency>
      <dependency>
        <groupId>cn.iocoder.boot</groupId>
        <artifactId>yudao-spring-boot-starter-biz-ip</artifactId>
        <version>1.8.3-snapshot</version>
      </dependency>
      <dependency>
        <groupId>cn.iocoder.boot</groupId>
        <artifactId>yudao-spring-boot-starter-captcha</artifactId>
        <version>1.8.3-snapshot</version>
      </dependency>
      <dependency>
        <groupId>cn.iocoder.boot</groupId>
        <artifactId>yudao-spring-boot-starter-desensitize</artifactId>
        <version>1.8.3-snapshot</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-configuration-processor</artifactId>
        <version>${spring.boot.version}</version>
      </dependency>
      <dependency>
        <groupId>cn.iocoder.boot</groupId>
        <artifactId>yudao-spring-boot-starter-web</artifactId>
        <version>1.8.3-snapshot</version>
      </dependency>
      <dependency>
        <groupId>cn.iocoder.boot</groupId>
        <artifactId>yudao-spring-boot-starter-security</artifactId>
        <version>1.8.3-snapshot</version>
      </dependency>
      <dependency>
        <groupId>cn.iocoder.boot</groupId>
        <artifactId>yudao-spring-boot-starter-websocket</artifactId>
        <version>1.8.3-snapshot</version>
      </dependency>
      <dependency>
        <groupId>com.github.xiaoymin</groupId>
        <artifactId>knife4j-openapi3-spring-boot-starter</artifactId>
        <version>${knife4j.version}</version>
      </dependency>
      <dependency>
        <groupId>org.springdoc</groupId>
        <artifactId>springdoc-openapi-ui</artifactId>
        <version>${springdoc.version}</version>
      </dependency>
      <dependency>
        <groupId>cn.iocoder.boot</groupId>
        <artifactId>yudao-spring-boot-starter-mybatis</artifactId>
        <version>1.8.3-snapshot</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba</groupId>
        <artifactId>druid-spring-boot-starter</artifactId>
        <version>${druid.version}</version>
      </dependency>
      <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>mybatis-plus-boot-starter</artifactId>
        <version>${mybatis-plus.version}</version>
      </dependency>
      <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>mybatis-plus-generator</artifactId>
        <version>${mybatis-plus-generator.version}</version>
      </dependency>
      <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
        <version>${dynamic-datasource.version}</version>
      </dependency>
      <dependency>
        <groupId>com.github.yulichang</groupId>
        <artifactId>mybatis-plus-join-boot-starter</artifactId>
        <version>${mybatis-plus-join.version}</version>
      </dependency>
      <dependency>
        <groupId>cn.iocoder.boot</groupId>
        <artifactId>yudao-spring-boot-starter-redis</artifactId>
        <version>1.8.3-snapshot</version>
      </dependency>
      <dependency>
        <groupId>org.redisson</groupId>
        <artifactId>redisson-spring-boot-starter</artifactId>
        <version>${redisson.version}</version>
        <exclusions>
          <exclusion>
            <artifactId>spring-boot-starter-actuator</artifactId>
            <groupId>org.springframework.boot</groupId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.dameng</groupId>
        <artifactId>DmJdbcDriver18</artifactId>
        <version>${dm8.jdbc.version}</version>
      </dependency>
      <dependency>
        <groupId>cn.iocoder.boot</groupId>
        <artifactId>yudao-spring-boot-starter-job</artifactId>
        <version>1.8.3-snapshot</version>
      </dependency>
      <dependency>
        <groupId>cn.iocoder.boot</groupId>
        <artifactId>yudao-spring-boot-starter-mq</artifactId>
        <version>1.8.3-snapshot</version>
      </dependency>
      <dependency>
        <groupId>org.apache.rocketmq</groupId>
        <artifactId>rocketmq-spring-boot-starter</artifactId>
        <version>${rocketmq-spring.version}</version>
      </dependency>
      <dependency>
        <groupId>cn.iocoder.boot</groupId>
        <artifactId>yudao-spring-boot-starter-protection</artifactId>
        <version>1.8.3-snapshot</version>
      </dependency>
      <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>lock4j-redisson-spring-boot-starter</artifactId>
        <version>${lock4j.version}</version>
        <exclusions>
          <exclusion>
            <artifactId>redisson-spring-boot-starter</artifactId>
            <groupId>org.redisson</groupId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>io.github.resilience4j</groupId>
        <artifactId>resilience4j-ratelimiter</artifactId>
        <version>${resilience4j.version}</version>
      </dependency>
      <dependency>
        <groupId>io.github.resilience4j</groupId>
        <artifactId>resilience4j-spring-boot2</artifactId>
        <version>${resilience4j.version}</version>
      </dependency>
      <dependency>
        <groupId>cn.iocoder.boot</groupId>
        <artifactId>yudao-spring-boot-starter-monitor</artifactId>
        <version>1.8.3-snapshot</version>
      </dependency>
      <dependency>
        <groupId>org.apache.skywalking</groupId>
        <artifactId>apm-toolkit-trace</artifactId>
        <version>${skywalking.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.skywalking</groupId>
        <artifactId>apm-toolkit-logback-1.x</artifactId>
        <version>${skywalking.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.skywalking</groupId>
        <artifactId>apm-toolkit-opentracing</artifactId>
        <version>${skywalking.version}</version>
      </dependency>
      <dependency>
        <groupId>io.opentracing</groupId>
        <artifactId>opentracing-api</artifactId>
        <version>${opentracing.version}</version>
      </dependency>
      <dependency>
        <groupId>io.opentracing</groupId>
        <artifactId>opentracing-util</artifactId>
        <version>${opentracing.version}</version>
      </dependency>
      <dependency>
        <groupId>io.opentracing</groupId>
        <artifactId>opentracing-noop</artifactId>
        <version>${opentracing.version}</version>
      </dependency>
      <dependency>
        <groupId>de.codecentric</groupId>
        <artifactId>spring-boot-admin-starter-server</artifactId>
        <version>${spring-boot-admin.version}</version>
        <exclusions>
          <exclusion>
            <artifactId>spring-boot-admin-server-cloud</artifactId>
            <groupId>de.codecentric</groupId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>de.codecentric</groupId>
        <artifactId>spring-boot-admin-starter-client</artifactId>
        <version>${spring-boot-admin.version}</version>
      </dependency>
      <dependency>
        <groupId>cn.iocoder.boot</groupId>
        <artifactId>yudao-spring-boot-starter-test</artifactId>
        <version>1.8.3-snapshot</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.mockito</groupId>
        <artifactId>mockito-inline</artifactId>
        <version>${mockito-inline.version}</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-test</artifactId>
        <version>${spring.boot.version}</version>
        <exclusions>
          <exclusion>
            <artifactId>asm</artifactId>
            <groupId>org.ow2.asm</groupId>
          </exclusion>
          <exclusion>
            <artifactId>mockito-core</artifactId>
            <groupId>org.mockito</groupId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.github.fppt</groupId>
        <artifactId>jedis-mock</artifactId>
        <version>${jedis-mock.version}</version>
      </dependency>
      <dependency>
        <groupId>uk.co.jemos.podam</groupId>
        <artifactId>podam</artifactId>
        <version>${podam.version}</version>
      </dependency>
      <dependency>
        <groupId>cn.iocoder.boot</groupId>
        <artifactId>yudao-spring-boot-starter-flowable</artifactId>
        <version>1.8.3-snapshot</version>
      </dependency>
      <dependency>
        <groupId>org.flowable</groupId>
        <artifactId>flowable-spring-boot-starter-process</artifactId>
        <version>${flowable.version}</version>
      </dependency>
      <dependency>
        <groupId>org.flowable</groupId>
        <artifactId>flowable-spring-boot-starter-actuator</artifactId>
        <version>${flowable.version}</version>
      </dependency>
      <dependency>
        <groupId>cn.iocoder.boot</groupId>
        <artifactId>yudao-common</artifactId>
        <version>1.8.3-snapshot</version>
      </dependency>
      <dependency>
        <groupId>cn.iocoder.boot</groupId>
        <artifactId>yudao-spring-boot-starter-excel</artifactId>
        <version>1.8.3-snapshot</version>
      </dependency>
      <dependency>
        <groupId>org.projectlombok</groupId>
        <artifactId>lombok</artifactId>
        <version>${lombok.version}</version>
      </dependency>
      <dependency>
        <groupId>org.mapstruct</groupId>
        <artifactId>mapstruct</artifactId>
        <version>${mapstruct.version}</version>
      </dependency>
      <dependency>
        <groupId>org.mapstruct</groupId>
        <artifactId>mapstruct-jdk8</artifactId>
        <version>${mapstruct.version}</version>
      </dependency>
      <dependency>
        <groupId>org.mapstruct</groupId>
        <artifactId>mapstruct-processor</artifactId>
        <version>${mapstruct.version}</version>
      </dependency>
      <dependency>
        <groupId>cn.hutool</groupId>
        <artifactId>hutool-all</artifactId>
        <version>${hutool.version}</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba</groupId>
        <artifactId>easyexcel</artifactId>
        <version>${easyexcel.verion}</version>
      </dependency>
      <dependency>
        <groupId>commons-io</groupId>
        <artifactId>commons-io</artifactId>
        <version>${commons-io.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.tika</groupId>
        <artifactId>tika-core</artifactId>
        <version>${tika-core.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.velocity</groupId>
        <artifactId>velocity-engine-core</artifactId>
        <version>${velocity.version}</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba</groupId>
        <artifactId>fastjson</artifactId>
        <version>${fastjson.version}</version>
      </dependency>
      <dependency>
        <groupId>cn.smallbun.screw</groupId>
        <artifactId>screw-core</artifactId>
        <version>${screw.version}</version>
        <exclusions>
          <exclusion>
            <artifactId>freemarker</artifactId>
            <groupId>org.freemarker</groupId>
          </exclusion>
          <exclusion>
            <artifactId>fastjson</artifactId>
            <groupId>com.alibaba</groupId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.google.guava</groupId>
        <artifactId>guava</artifactId>
        <version>${guava.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.inject</groupId>
        <artifactId>guice</artifactId>
        <version>${guice.version}</version>
      </dependency>
      <dependency>
        <groupId>com.alibaba</groupId>
        <artifactId>transmittable-thread-local</artifactId>
        <version>${transmittable-thread-local.version}</version>
      </dependency>
      <dependency>
        <groupId>commons-net</groupId>
        <artifactId>commons-net</artifactId>
        <version>${commons-net.version}</version>
      </dependency>
      <dependency>
        <groupId>com.jcraft</groupId>
        <artifactId>jsch</artifactId>
        <version>${jsch.version}</version>
      </dependency>
      <dependency>
        <groupId>com.xingyuv</groupId>
        <artifactId>spring-boot-starter-captcha-plus</artifactId>
        <version>${captcha-plus.version}</version>
      </dependency>
      <dependency>
        <groupId>org.lionsoul</groupId>
        <artifactId>ip2region</artifactId>
        <version>${ip2region.version}</version>
      </dependency>
      <dependency>
        <groupId>org.jsoup</groupId>
        <artifactId>jsoup</artifactId>
        <version>${jsoup.version}</version>
      </dependency>
      <dependency>
        <groupId>com.squareup.okio</groupId>
        <artifactId>okio</artifactId>
        <version>${okio.version}</version>
      </dependency>
      <dependency>
        <groupId>com.squareup.okhttp3</groupId>
        <artifactId>okhttp</artifactId>
        <version>${okhttp3.version}</version>
      </dependency>
      <dependency>
        <groupId>cn.iocoder.boot</groupId>
        <artifactId>yudao-spring-boot-starter-file</artifactId>
        <version>1.8.3-snapshot</version>
      </dependency>
      <dependency>
        <groupId>io.minio</groupId>
        <artifactId>minio</artifactId>
        <version>${minio.version}</version>
      </dependency>
      <dependency>
        <groupId>com.aliyun</groupId>
        <artifactId>aliyun-java-sdk-core</artifactId>
        <version>${aliyun-java-sdk-core.version}</version>
        <exclusions>
          <exclusion>
            <artifactId>opentracing-api</artifactId>
            <groupId>io.opentracing</groupId>
          </exclusion>
          <exclusion>
            <artifactId>opentracing-util</artifactId>
            <groupId>io.opentracing</groupId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.aliyun</groupId>
        <artifactId>aliyun-java-sdk-dysmsapi</artifactId>
        <version>${aliyun-java-sdk-dysmsapi.version}</version>
      </dependency>
      <dependency>
        <groupId>com.tencentcloudapi</groupId>
        <artifactId>tencentcloud-sdk-java-sms</artifactId>
        <version>${tencentcloud-sdk-java.version}</version>
      </dependency>
      <dependency>
        <groupId>com.xingyuv</groupId>
        <artifactId>spring-boot-starter-justauth</artifactId>
        <version>${justauth.version}</version>
      </dependency>
      <dependency>
        <groupId>com.github.binarywang</groupId>
        <artifactId>weixin-java-pay</artifactId>
        <version>${weixin-java.version}</version>
      </dependency>
      <dependency>
        <groupId>com.github.binarywang</groupId>
        <artifactId>weixin-java-mp</artifactId>
        <version>${weixin-java.version}</version>
      </dependency>
      <dependency>
        <groupId>com.github.binarywang</groupId>
        <artifactId>wx-java-mp-spring-boot-starter</artifactId>
        <version>${weixin-java.version}</version>
      </dependency>
      <dependency>
        <groupId>com.github.binarywang</groupId>
        <artifactId>wx-java-miniapp-spring-boot-starter</artifactId>
        <version>${weixin-java.version}</version>
      </dependency>
      <dependency>
        <groupId>org.jeecgframework.jimureport</groupId>
        <artifactId>jimureport-spring-boot-starter</artifactId>
        <version>${jimureport.version}</version>
        <exclusions>
          <exclusion>
            <artifactId>druid</artifactId>
            <groupId>com.alibaba</groupId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>xerces</groupId>
        <artifactId>xercesImpl</artifactId>
        <version>${xercesImpl.version}</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-websocket</artifactId>
        <version>${spring.boot.version}</version>
      </dependency>
    </dependencies>
  </dependencyManagement>
  <build>
    <plugins>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>flatten-maven-plugin</artifactId>
        <version>${flatten-maven-plugin.version}</version>
        <executions>
          <execution>
            <id>flatten</id>
            <phase>process-resources</phase>
            <goals>
              <goal>flatten</goal>
            </goals>
          </execution>
          <execution>
            <id>flatten.clean</id>
            <phase>clean</phase>
            <goals>
              <goal>clean</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <flattenMode>resolveCiFriendliesOnly</flattenMode>
          <updatePomFile>true</updatePomFile>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>
