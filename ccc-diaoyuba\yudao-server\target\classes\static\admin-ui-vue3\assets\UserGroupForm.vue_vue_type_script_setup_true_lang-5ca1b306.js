import{d as O,l as j,r as o,f as H,o as i,q as _,w as s,i as r,a,j as f,B as P,c as h,F as k,k as w,t as R,x as z,T as J,D as K,M as Q,C as W,ag as X,af as Y,I as Z,H as $,L as ee,__tla as ae}from"./index-97fffa0c.js";import{_ as le,__tla as te}from"./Dialog.vue_vue_type_style_index_0_lang-5af0bc2b.js";import{a as se,D as re,__tla as ue}from"./dict-6a82eb12.js";import{C as I}from"./constants-3933cd3a.js";import{a as oe,c as de,u as me,__tla as ie}from"./index-fc52dc1e.js";import{g as ne,__tla as ce}from"./index-e6297252.js";import{u as _e,__tla as pe}from"./useMessage-18385d4a.js";let q,fe=Promise.all([(()=>{try{return ae}catch{}})(),(()=>{try{return te}catch{}})(),(()=>{try{return ue}catch{}})(),(()=>{try{return ie}catch{}})(),(()=>{try{return ce}catch{}})(),(()=>{try{return pe}catch{}})()]).then(async()=>{q=O({name:"UserGroupForm",__name:"UserGroupForm",emits:["success"],setup(ve,{expose:x,emit:C}){const{t:p}=j(),v=_e(),d=o(!1),b=o(""),m=o(!1),y=o(""),t=o({id:void 0,name:void 0,description:void 0,memberUserIds:void 0,status:I.ENABLE}),E=H({name:[{required:!0,message:"\u7EC4\u540D\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],description:[{required:!0,message:"\u63CF\u8FF0\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],memberUserIds:[{required:!0,message:"\u6210\u5458\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}],status:[{required:!0,message:"\u72B6\u6001\u4E0D\u80FD\u4E3A\u7A7A",trigger:"blur"}]}),n=o(),V=o([]);x({open:async(u,l)=>{if(d.value=!0,b.value=p("action."+u),y.value=u,M(),l){m.value=!0;try{t.value=await oe(l)}finally{m.value=!1}}V.value=await ne()}});const F=C,S=async()=>{if(n&&await n.value.validate()){m.value=!0;try{const u=t.value;y.value==="create"?(await de(u),v.success(p("common.createSuccess"))):(await me(u),v.success(p("common.updateSuccess"))),d.value=!1,F("success")}finally{m.value=!1}}},M=()=>{var u;t.value={id:void 0,name:void 0,description:void 0,memberUserIds:void 0,status:I.ENABLE},(u=n.value)==null||u.resetFields()};return(u,l)=>{const g=J,c=K,N=Q,A=W,B=X,L=Y,T=Z,U=$,D=le,G=ee;return i(),_(D,{modelValue:a(d),"onUpdate:modelValue":l[5]||(l[5]=e=>z(d)?d.value=e:null),title:a(b)},{footer:s(()=>[r(U,{disabled:a(m),type:"primary",onClick:S},{default:s(()=>[f("\u786E \u5B9A")]),_:1},8,["disabled"]),r(U,{onClick:l[4]||(l[4]=e=>d.value=!1)},{default:s(()=>[f("\u53D6 \u6D88")]),_:1})]),default:s(()=>[P((i(),_(T,{ref_key:"formRef",ref:n,model:a(t),rules:a(E),"label-width":"100px"},{default:s(()=>[r(c,{label:"\u7EC4\u540D",prop:"name"},{default:s(()=>[r(g,{modelValue:a(t).name,"onUpdate:modelValue":l[0]||(l[0]=e=>a(t).name=e),placeholder:"\u8BF7\u8F93\u5165\u7EC4\u540D"},null,8,["modelValue"])]),_:1}),r(c,{label:"\u63CF\u8FF0"},{default:s(()=>[r(g,{modelValue:a(t).description,"onUpdate:modelValue":l[1]||(l[1]=e=>a(t).description=e),placeholder:"\u8BF7\u8F93\u5165\u63CF\u8FF0",type:"textarea"},null,8,["modelValue"])]),_:1}),r(c,{label:"\u6210\u5458",prop:"memberUserIds"},{default:s(()=>[r(A,{modelValue:a(t).memberUserIds,"onUpdate:modelValue":l[2]||(l[2]=e=>a(t).memberUserIds=e),multiple:"",placeholder:"\u8BF7\u9009\u62E9\u6210\u5458"},{default:s(()=>[(i(!0),h(k,null,w(a(V),e=>(i(),_(N,{key:e.id,label:e.nickname,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),r(c,{label:"\u72B6\u6001",prop:"status"},{default:s(()=>[r(L,{modelValue:a(t).status,"onUpdate:modelValue":l[3]||(l[3]=e=>a(t).status=e)},{default:s(()=>[(i(!0),h(k,null,w(a(se)(a(re).COMMON_STATUS),e=>(i(),_(B,{key:e.value,label:e.value},{default:s(()=>[f(R(e.label),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])),[[G,a(m)]])]),_:1},8,["modelValue","title"])}}})});export{q as _,fe as __tla};
