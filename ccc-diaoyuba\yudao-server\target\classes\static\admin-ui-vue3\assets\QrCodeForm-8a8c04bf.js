import{_ as t,__tla as r}from"./QrCodeForm.vue_vue_type_script_setup_true_lang-914af937.js";import{__tla as _}from"./index-97fffa0c.js";import{__tla as a}from"./XButton-dd4d8780.js";import"./_plugin-vue_export-helper-1b428a4d.js";import{__tla as l}from"./el-card-6c7c099d.js";import{__tla as o}from"./Qrcode-e6549950.js";import"./logo-13933b22.js";import{__tla as m}from"./LoginFormTitle.vue_vue_type_script_setup_true_lang-f56773d4.js";let c=Promise.all([(()=>{try{return r}catch{}})(),(()=>{try{return _}catch{}})(),(()=>{try{return a}catch{}})(),(()=>{try{return l}catch{}})(),(()=>{try{return o}catch{}})(),(()=>{try{return m}catch{}})()]).then(async()=>{});export{c as __tla,t as default};
