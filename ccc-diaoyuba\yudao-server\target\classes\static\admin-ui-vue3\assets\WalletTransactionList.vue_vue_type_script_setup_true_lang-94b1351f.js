import{ao as T,d as x,r as p,f as z,A as S,o as m,q as d,w as o,B as j,a,i as l,j as g,t as w,at as f,J as q,K as P,L as U,__tla as W}from"./index-97fffa0c.js";import{_ as A,__tla as B}from"./ContentWrap.vue_vue_type_script_setup_true_lang-a574ccef.js";import{_ as J,__tla as K}from"./index.vue_vue_type_script_setup_true_lang-d31568cf.js";import{d as k,__tla as C}from"./formatTime-9d54d2c5.js";let y,D=Promise.all([(()=>{try{return W}catch{}})(),(()=>{try{return B}catch{}})(),(()=>{try{return K}catch{}})(),(()=>{try{return C}catch{}})()]).then(async()=>{y=x({name:"WalletTransactionList",__name:"WalletTransactionList",props:{walletId:{type:Number,required:!1}},setup(b){const{walletId:h}=b,s=p(!0),_=p(0),e=z({pageNo:1,pageSize:10,walletId:null}),c=p([]),u=async()=>{s.value=!0;try{e.walletId=h;const i=await(async r=>await T.get({url:"/pay/wallet-transaction/page",params:r}))(e);c.value=i.list,_.value=i.total}finally{s.value=!1}};return S(()=>{u()}),(i,r)=>{const n=q,v=P,I=J,N=A,L=U;return m(),d(N,null,{default:o(()=>[j((m(),d(v,{data:a(c),stripe:!0,"show-overflow-tooltip":!0},{default:o(()=>[l(n,{label:"\u7F16\u53F7",align:"center",prop:"id"}),l(n,{label:"\u94B1\u5305\u7F16\u53F7",align:"center",prop:"walletId"}),l(n,{label:"\u5173\u8054\u4E1A\u52A1\u6807\u9898",align:"center",prop:"title"}),l(n,{label:"\u4EA4\u6613\u91D1\u989D",align:"center",prop:"price"},{default:o(({row:t})=>[g(w(a(f)(t.price))+" \u5143",1)]),_:1}),l(n,{label:"\u94B1\u5305\u4F59\u989D",align:"center",prop:"balance"},{default:o(({row:t})=>[g(w(a(f)(t.balance))+" \u5143",1)]),_:1}),l(n,{label:"\u4EA4\u6613\u65F6\u95F4",align:"center",prop:"createTime",formatter:a(k),width:"180px"},null,8,["formatter"])]),_:1},8,["data"])),[[L,a(s)]]),l(I,{total:a(_),page:a(e).pageNo,"onUpdate:page":r[0]||(r[0]=t=>a(e).pageNo=t),limit:a(e).pageSize,"onUpdate:limit":r[1]||(r[1]=t=>a(e).pageSize=t),onPagination:u},null,8,["total","page","limit"])]),_:1})}}})});export{y as _,D as __tla};
