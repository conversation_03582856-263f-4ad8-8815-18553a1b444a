<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="ALL" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="1051ef31-9e84-463a-a4f4-8c5f087dc14c" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="diaoyuba" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
    <option name="RESET_MODE" value="HARD" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="localRepository" value="D:\develop\maven_repository" />
        <option name="mavenHome" value="$PROJECT_DIR$/../../../developSoftWare/maven/apache-maven-3.6.3" />
        <option name="userSettingsFile" value="D:\developSoftWare\maven\apache-maven-3.6.3\conf\settings-local.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectId" id="2ZkiG0xE5ySeZ7xjP7J2KrqMze5" />
  <component name="ProjectViewState">
    <option name="autoscrollToSource" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;WebServerToolWindowFactoryState&quot;: &quot;false&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/project/study/heima-shop&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;pnpm&quot;,
    &quot;ts.external.directory.path&quot;: &quot;D:\\project\\study\\diaodianba\\heima-shop\\node_modules\\typescript\\lib&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RebelAgentSelection">
    <selection>jr</selection>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="SvnConfiguration">
    <configuration>C:\Users\<USER>\AppData\Roaming\Subversion</configuration>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="1051ef31-9e84-463a-a4f4-8c5f087dc14c" name="Changes" comment="" />
      <created>1702972378426</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1702972378426</updated>
      <workItem from="1702972379495" duration="2090000" />
      <workItem from="1717491716034" duration="35000" />
      <workItem from="1717491829150" duration="4000" />
      <workItem from="1718186450643" duration="164000" />
      <workItem from="1718616603890" duration="18000" />
      <workItem from="1718616626307" duration="29000" />
      <workItem from="1718679814215" duration="4545000" />
      <workItem from="1718784090521" duration="1888000" />
      <workItem from="1718847326224" duration="679000" />
      <workItem from="1718854971707" duration="852000" />
      <workItem from="1719821302574" duration="188000" />
      <workItem from="1719821502258" duration="120000" />
      <workItem from="1719910920375" duration="775000" />
      <workItem from="1719970821785" duration="3216000" />
      <workItem from="1719996810498" duration="889000" />
      <workItem from="1720056547067" duration="3353000" />
      <workItem from="1720143847398" duration="5183000" />
      <workItem from="1720402547606" duration="594000" />
      <workItem from="1720488844915" duration="1872000" />
      <workItem from="1720575266925" duration="1178000" />
      <workItem from="1721015320215" duration="412000" />
      <workItem from="1752031741987" duration="707000" />
      <workItem from="1752126473309" duration="2866000" />
      <workItem from="1752163800946" duration="903000" />
      <workItem from="1752197006395" duration="1344000" />
      <workItem from="1752415541802" duration="4288000" />
      <workItem from="1752424116537" duration="471000" />
      <workItem from="1752457044787" duration="654000" />
      <workItem from="1752543279599" duration="4325000" />
      <workItem from="1752629692536" duration="6990000" />
      <workItem from="1752716326786" duration="9007000" />
      <workItem from="1752802238357" duration="15000" />
      <workItem from="1752818989519" duration="2579000" />
      <workItem from="1752829173671" duration="457000" />
      <workItem from="1752830449637" duration="1591000" />
      <workItem from="1752849278994" duration="1932000" />
      <workItem from="1753018687557" duration="4704000" />
      <workItem from="1753061306044" duration="97000" />
    </task>
    <task id="LOCAL-00001" summary="[note] 近期打卡">
      <created>1718186564802</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1718186564802</updated>
    </task>
    <task id="LOCAL-00002" summary="[note] 是否免费修改">
      <created>1718696698394</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1718696698394</updated>
    </task>
    <task id="LOCAL-00003" summary="[note] 免费">
      <created>1718855030231</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1718855030231</updated>
    </task>
    <task id="LOCAL-00004" summary="[note] push test">
      <created>1719911007141</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1719911007141</updated>
    </task>
    <task id="LOCAL-00005" summary="[team] list1">
      <created>1752420534899</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1752420534899</updated>
    </task>
    <task id="LOCAL-00006" summary="[team] list1">
      <created>1752423942558</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1752423942558</updated>
    </task>
    <task id="LOCAL-00007" summary="[team] list1">
      <created>1752424269540</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1752424269540</updated>
    </task>
    <task id="LOCAL-00008" summary="[team] list3">
      <created>1752424339417</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1752424339417</updated>
    </task>
    <task id="LOCAL-00009" summary="[team] list 提交">
      <created>1752543527633</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1752543527633</updated>
    </task>
    <task id="LOCAL-00010" summary="[team] list 提交">
      <created>1752581781663</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1752581781663</updated>
    </task>
    <task id="LOCAL-00011" summary="[team] 交互细节处理 提交">
      <created>1752677645988</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1752677645988</updated>
    </task>
    <task id="LOCAL-00012" summary="[team] 独立team 提交">
      <created>1752684567682</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1752684567682</updated>
    </task>
    <task id="LOCAL-00013" summary="[team] 独立team 提交">
      <created>1752685005852</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1752685005852</updated>
    </task>
    <task id="LOCAL-00014" summary="[team] 独立team 提交">
      <created>1752688082600</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1752688082600</updated>
    </task>
    <task id="LOCAL-00015" summary="[team] 独立team 提交">
      <created>1752688528803</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1752688528803</updated>
    </task>
    <task id="LOCAL-00016" summary="[team] 独立team 提交">
      <created>1752755760682</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>1752755760682</updated>
    </task>
    <task id="LOCAL-00017" summary="[team] 评论 提交">
      <created>1752758152031</created>
      <option name="number" value="00017" />
      <option name="presentableId" value="LOCAL-00017" />
      <option name="project" value="LOCAL" />
      <updated>1752758152031</updated>
    </task>
    <task id="LOCAL-00018" summary="[team] 评论调试">
      <created>1752759228411</created>
      <option name="number" value="00018" />
      <option name="presentableId" value="LOCAL-00018" />
      <option name="project" value="LOCAL" />
      <updated>1752759228411</updated>
    </task>
    <task id="LOCAL-00019" summary="[team] 评论调试">
      <created>1752767415074</created>
      <option name="number" value="00019" />
      <option name="presentableId" value="LOCAL-00019" />
      <option name="project" value="LOCAL" />
      <updated>1752767415074</updated>
    </task>
    <task id="LOCAL-00020" summary="[team] 评论调试">
      <created>1752768670560</created>
      <option name="number" value="00020" />
      <option name="presentableId" value="LOCAL-00020" />
      <option name="project" value="LOCAL" />
      <updated>1752768670560</updated>
    </task>
    <task id="LOCAL-00021" summary="[team] 评论调试2">
      <created>1752771429770</created>
      <option name="number" value="00021" />
      <option name="presentableId" value="LOCAL-00021" />
      <option name="project" value="LOCAL" />
      <updated>1752771429770</updated>
    </task>
    <task id="LOCAL-00022" summary="[team] token">
      <created>1752771553406</created>
      <option name="number" value="00022" />
      <option name="presentableId" value="LOCAL-00022" />
      <option name="project" value="LOCAL" />
      <updated>1752771553406</updated>
    </task>
    <task id="LOCAL-00023" summary="[team] Detail adjustment">
      <created>1752772281122</created>
      <option name="number" value="00023" />
      <option name="presentableId" value="LOCAL-00023" />
      <option name="project" value="LOCAL" />
      <updated>1752772281122</updated>
    </task>
    <task id="LOCAL-00024" summary="[team] Detail adjustment 1">
      <created>1752821957477</created>
      <option name="number" value="00024" />
      <option name="presentableId" value="LOCAL-00024" />
      <option name="project" value="LOCAL" />
      <updated>1752821957477</updated>
    </task>
    <task id="LOCAL-00025" summary="[team] Detail adjustment 1">
      <created>1752830569065</created>
      <option name="number" value="00025" />
      <option name="presentableId" value="LOCAL-00025" />
      <option name="project" value="LOCAL" />
      <updated>1752830569065</updated>
    </task>
    <task id="LOCAL-00026" summary="[team] Detail">
      <created>1752834614105</created>
      <option name="number" value="00026" />
      <option name="presentableId" value="LOCAL-00026" />
      <option name="project" value="LOCAL" />
      <updated>1752834614105</updated>
    </task>
    <task id="LOCAL-00027" summary="[team] Detail v1">
      <created>1752851061560</created>
      <option name="number" value="00027" />
      <option name="presentableId" value="LOCAL-00027" />
      <option name="project" value="LOCAL" />
      <updated>1752851061560</updated>
    </task>
    <task id="LOCAL-00028" summary="[team] Detail v1">
      <created>1753020371450</created>
      <option name="number" value="00028" />
      <option name="presentableId" value="LOCAL-00028" />
      <option name="project" value="LOCAL" />
      <updated>1753020371450</updated>
    </task>
    <task id="LOCAL-00029" summary="[team] Detail v1.1">
      <created>1753024603784</created>
      <option name="number" value="00029" />
      <option name="presentableId" value="LOCAL-00029" />
      <option name="project" value="LOCAL" />
      <updated>1753024603784</updated>
    </task>
    <task id="LOCAL-00030" summary="[team] Detail v1.1 environment switching">
      <created>1753028430737</created>
      <option name="number" value="00030" />
      <option name="presentableId" value="LOCAL-00030" />
      <option name="project" value="LOCAL" />
      <updated>1753028430737</updated>
    </task>
    <task id="LOCAL-00031" summary="[team] Detail v1.1 deleted radios">
      <created>1753028934099</created>
      <option name="number" value="00031" />
      <option name="presentableId" value="LOCAL-00031" />
      <option name="project" value="LOCAL" />
      <updated>1753028934099</updated>
    </task>
    <option name="localTasksCounter" value="32" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.History.Properties">
    <option name="COLUMN_ID_ORDER">
      <list>
        <option value="Default.Root" />
        <option value="Default.Author" />
        <option value="Default.Date" />
        <option value="Default.Subject" />
        <option value="Space.CommitStatus" />
      </list>
    </option>
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="CUSTOM_BOOLEAN_PROPERTIES">
                <map>
                  <entry key="Show.Git.Branches" value="true" />
                </map>
              </option>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="20250710-diaoyuba" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="[note] 空格" />
    <MESSAGE value="[note] space" />
    <MESSAGE value="[note] 近期打卡" />
    <MESSAGE value="[note] push test" />
    <MESSAGE value="[note] 增加两个空格，" />
    <MESSAGE value="[note] 增加空格" />
    <MESSAGE value="[team] list" />
    <MESSAGE value="[team] list1" />
    <MESSAGE value="[team] list2" />
    <MESSAGE value="[team] list3" />
    <MESSAGE value="[team] list6" />
    <MESSAGE value="[team] list 提交" />
    <MESSAGE value="[team] 交互细节处理 提交" />
    <MESSAGE value="[team] 独立team 提交" />
    <MESSAGE value="[team] 评论 提交" />
    <MESSAGE value="[team] 评论调试" />
    <MESSAGE value="[team] 评论调试2" />
    <MESSAGE value="[team] token" />
    <MESSAGE value="[team] Detail adjustment" />
    <MESSAGE value="[team] Detail adjustment 1" />
    <MESSAGE value="[team] Detail" />
    <MESSAGE value="[team] Detail v1" />
    <MESSAGE value="[team] Detail v1.1" />
    <MESSAGE value="[team] Detail v1.1 environment switching" />
    <MESSAGE value="[team] Detail v1.1 deleted radios" />
    <option name="LAST_COMMIT_MESSAGE" value="[team] Detail v1.1 deleted radios" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="javascript">
          <url>file://$PROJECT_DIR$/dist/dev/mp-weixin/pagesTeam/detail/index.js</url>
          <line>408</line>
          <option name="timeStamp" value="1" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>