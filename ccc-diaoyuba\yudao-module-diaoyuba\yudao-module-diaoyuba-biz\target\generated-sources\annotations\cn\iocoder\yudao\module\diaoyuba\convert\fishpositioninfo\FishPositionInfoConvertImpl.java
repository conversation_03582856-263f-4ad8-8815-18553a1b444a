package cn.iocoder.yudao.module.diaoyuba.convert.fishpositioninfo;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.diaoyuba.controller.admin.fishpositioninfo.vo.FishPositionInfoCreateReqVO;
import cn.iocoder.yudao.module.diaoyuba.controller.admin.fishpositioninfo.vo.FishPositionInfoExcelVO;
import cn.iocoder.yudao.module.diaoyuba.controller.admin.fishpositioninfo.vo.FishPositionInfoRespVO;
import cn.iocoder.yudao.module.diaoyuba.controller.admin.fishpositioninfo.vo.FishPositionInfoUpdateReqVO;
import cn.iocoder.yudao.module.diaoyuba.controller.app.fishpositioninfo.vo.AppFishPositionInfoListRespVO;
import cn.iocoder.yudao.module.diaoyuba.controller.app.fishpositioninfo.vo.AppFishPositionInfoRespVO;
import cn.iocoder.yudao.module.diaoyuba.dal.dataobject.fishpositioninfo.FishPositionInfoDO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-21T09:34:05+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_181 (Oracle Corporation)"
)
public class FishPositionInfoConvertImpl implements FishPositionInfoConvert {

    @Override
    public FishPositionInfoDO convert(FishPositionInfoCreateReqVO bean) {
        if ( bean == null ) {
            return null;
        }

        FishPositionInfoDO.FishPositionInfoDOBuilder fishPositionInfoDO = FishPositionInfoDO.builder();

        fishPositionInfoDO.title( bean.getTitle() );
        fishPositionInfoDO.latitude( bean.getLatitude() );
        fishPositionInfoDO.longitude( bean.getLongitude() );
        fishPositionInfoDO.address( bean.getAddress() );
        fishPositionInfoDO.addressDetail( bean.getAddressDetail() );
        fishPositionInfoDO.content( bean.getContent() );
        fishPositionInfoDO.province( bean.getProvince() );
        fishPositionInfoDO.city( bean.getCity() );
        fishPositionInfoDO.county( bean.getCounty() );
        fishPositionInfoDO.type( bean.getType() );
        fishPositionInfoDO.itime( bean.getItime() );
        fishPositionInfoDO.utime( bean.getUtime() );
        fishPositionInfoDO.isPass( bean.getIsPass() );
        fishPositionInfoDO.luKuang( bean.getLuKuang() );
        fishPositionInfoDO.shuiShen( bean.getShuiShen() );
        fishPositionInfoDO.shuiBian( bean.getShuiBian() );
        fishPositionInfoDO.zhiLu( bean.getZhiLu() );
        fishPositionInfoDO.isCamp( bean.getIsCamp() );
        fishPositionInfoDO.isFree( bean.getIsFree() );
        fishPositionInfoDO.price( bean.getPrice() );
        fishPositionInfoDO.jianCheng( bean.getJianCheng() );
        fishPositionInfoDO.isCan( bean.getIsCan() );
        fishPositionInfoDO.userId( bean.getUserId() );
        fishPositionInfoDO.fishType( bean.getFishType() );
        fishPositionInfoDO.positionType( bean.getPositionType() );
        fishPositionInfoDO.playType( bean.getPlayType() );
        if ( bean.getCollectionQty() != null ) {
            fishPositionInfoDO.collectionQty( Long.parseLong( bean.getCollectionQty() ) );
        }
        fishPositionInfoDO.rating( bean.getRating() );

        return fishPositionInfoDO.build();
    }

    @Override
    public FishPositionInfoDO convert(FishPositionInfoUpdateReqVO bean) {
        if ( bean == null ) {
            return null;
        }

        FishPositionInfoDO.FishPositionInfoDOBuilder fishPositionInfoDO = FishPositionInfoDO.builder();

        fishPositionInfoDO.id( bean.getId() );
        fishPositionInfoDO.title( bean.getTitle() );
        fishPositionInfoDO.latitude( bean.getLatitude() );
        fishPositionInfoDO.longitude( bean.getLongitude() );
        fishPositionInfoDO.address( bean.getAddress() );
        fishPositionInfoDO.addressDetail( bean.getAddressDetail() );
        fishPositionInfoDO.content( bean.getContent() );
        fishPositionInfoDO.province( bean.getProvince() );
        fishPositionInfoDO.city( bean.getCity() );
        fishPositionInfoDO.county( bean.getCounty() );
        fishPositionInfoDO.type( bean.getType() );
        fishPositionInfoDO.itime( bean.getItime() );
        fishPositionInfoDO.utime( bean.getUtime() );
        fishPositionInfoDO.isPass( bean.getIsPass() );
        fishPositionInfoDO.luKuang( bean.getLuKuang() );
        fishPositionInfoDO.shuiShen( bean.getShuiShen() );
        fishPositionInfoDO.shuiBian( bean.getShuiBian() );
        fishPositionInfoDO.zhiLu( bean.getZhiLu() );
        fishPositionInfoDO.isCamp( bean.getIsCamp() );
        fishPositionInfoDO.isFree( bean.getIsFree() );
        fishPositionInfoDO.price( bean.getPrice() );
        fishPositionInfoDO.jianCheng( bean.getJianCheng() );
        fishPositionInfoDO.isCan( bean.getIsCan() );
        fishPositionInfoDO.userId( bean.getUserId() );
        fishPositionInfoDO.fishType( bean.getFishType() );
        fishPositionInfoDO.positionType( bean.getPositionType() );
        fishPositionInfoDO.playType( bean.getPlayType() );
        if ( bean.getCollectionQty() != null ) {
            fishPositionInfoDO.collectionQty( Long.parseLong( bean.getCollectionQty() ) );
        }
        fishPositionInfoDO.rating( bean.getRating() );

        return fishPositionInfoDO.build();
    }

    @Override
    public FishPositionInfoRespVO convert(FishPositionInfoDO bean) {
        if ( bean == null ) {
            return null;
        }

        FishPositionInfoRespVO fishPositionInfoRespVO = new FishPositionInfoRespVO();

        fishPositionInfoRespVO.setTitle( bean.getTitle() );
        fishPositionInfoRespVO.setLatitude( bean.getLatitude() );
        fishPositionInfoRespVO.setLongitude( bean.getLongitude() );
        fishPositionInfoRespVO.setAddress( bean.getAddress() );
        fishPositionInfoRespVO.setAddressDetail( bean.getAddressDetail() );
        fishPositionInfoRespVO.setContent( bean.getContent() );
        fishPositionInfoRespVO.setProvince( bean.getProvince() );
        fishPositionInfoRespVO.setCity( bean.getCity() );
        fishPositionInfoRespVO.setCounty( bean.getCounty() );
        fishPositionInfoRespVO.setType( bean.getType() );
        fishPositionInfoRespVO.setItime( bean.getItime() );
        fishPositionInfoRespVO.setUtime( bean.getUtime() );
        fishPositionInfoRespVO.setIsPass( bean.getIsPass() );
        fishPositionInfoRespVO.setLuKuang( bean.getLuKuang() );
        fishPositionInfoRespVO.setShuiShen( bean.getShuiShen() );
        fishPositionInfoRespVO.setShuiBian( bean.getShuiBian() );
        fishPositionInfoRespVO.setZhiLu( bean.getZhiLu() );
        fishPositionInfoRespVO.setIsCamp( bean.getIsCamp() );
        fishPositionInfoRespVO.setIsFree( bean.getIsFree() );
        fishPositionInfoRespVO.setPrice( bean.getPrice() );
        fishPositionInfoRespVO.setJianCheng( bean.getJianCheng() );
        fishPositionInfoRespVO.setIsCan( bean.getIsCan() );
        fishPositionInfoRespVO.setUserId( bean.getUserId() );
        fishPositionInfoRespVO.setFishType( bean.getFishType() );
        fishPositionInfoRespVO.setPositionType( bean.getPositionType() );
        fishPositionInfoRespVO.setPlayType( bean.getPlayType() );
        if ( bean.getCollectionQty() != null ) {
            fishPositionInfoRespVO.setCollectionQty( String.valueOf( bean.getCollectionQty() ) );
        }
        fishPositionInfoRespVO.setRating( bean.getRating() );
        fishPositionInfoRespVO.setId( bean.getId() );
        fishPositionInfoRespVO.setCreateTime( bean.getCreateTime() );

        return fishPositionInfoRespVO;
    }

    @Override
    public AppFishPositionInfoRespVO convertApp(FishPositionInfoDO bean) {
        if ( bean == null ) {
            return null;
        }

        AppFishPositionInfoRespVO appFishPositionInfoRespVO = new AppFishPositionInfoRespVO();

        appFishPositionInfoRespVO.setTitle( bean.getTitle() );
        appFishPositionInfoRespVO.setLatitude( bean.getLatitude() );
        appFishPositionInfoRespVO.setLongitude( bean.getLongitude() );
        appFishPositionInfoRespVO.setAddress( bean.getAddress() );
        appFishPositionInfoRespVO.setAddressDetail( bean.getAddressDetail() );
        appFishPositionInfoRespVO.setContent( bean.getContent() );
        appFishPositionInfoRespVO.setProvince( bean.getProvince() );
        appFishPositionInfoRespVO.setCity( bean.getCity() );
        appFishPositionInfoRespVO.setCounty( bean.getCounty() );
        appFishPositionInfoRespVO.setType( bean.getType() );
        appFishPositionInfoRespVO.setIsPass( bean.getIsPass() );
        appFishPositionInfoRespVO.setLuKuang( bean.getLuKuang() );
        appFishPositionInfoRespVO.setShuiShen( bean.getShuiShen() );
        appFishPositionInfoRespVO.setShuiBian( bean.getShuiBian() );
        appFishPositionInfoRespVO.setZhiLu( bean.getZhiLu() );
        appFishPositionInfoRespVO.setIsCamp( bean.getIsCamp() );
        appFishPositionInfoRespVO.setIsFree( bean.getIsFree() );
        appFishPositionInfoRespVO.setPrice( bean.getPrice() );
        appFishPositionInfoRespVO.setJianCheng( bean.getJianCheng() );
        appFishPositionInfoRespVO.setIsCan( bean.getIsCan() );
        appFishPositionInfoRespVO.setUserId( bean.getUserId() );
        appFishPositionInfoRespVO.setFishType( bean.getFishType() );
        appFishPositionInfoRespVO.setPositionType( bean.getPositionType() );
        appFishPositionInfoRespVO.setPlayType( bean.getPlayType() );
        appFishPositionInfoRespVO.setCollectionQty( bean.getCollectionQty() );
        appFishPositionInfoRespVO.setLikeQty( bean.getLikeQty() );
        appFishPositionInfoRespVO.setViewCount( bean.getViewCount() );
        appFishPositionInfoRespVO.setCheckinQty( bean.getCheckinQty() );
        appFishPositionInfoRespVO.setRating( bean.getRating() );
        appFishPositionInfoRespVO.setId( bean.getId() );
        appFishPositionInfoRespVO.setCreateTime( bean.getCreateTime() );

        return appFishPositionInfoRespVO;
    }

    @Override
    public List<FishPositionInfoRespVO> convertList(List<FishPositionInfoDO> list) {
        if ( list == null ) {
            return null;
        }

        List<FishPositionInfoRespVO> list1 = new ArrayList<FishPositionInfoRespVO>( list.size() );
        for ( FishPositionInfoDO fishPositionInfoDO : list ) {
            list1.add( convert( fishPositionInfoDO ) );
        }

        return list1;
    }

    @Override
    public List<AppFishPositionInfoListRespVO> convertListApp(List<FishPositionInfoDO> list) {
        if ( list == null ) {
            return null;
        }

        List<AppFishPositionInfoListRespVO> list1 = new ArrayList<AppFishPositionInfoListRespVO>( list.size() );
        for ( FishPositionInfoDO fishPositionInfoDO : list ) {
            list1.add( convertListInfoApp( fishPositionInfoDO ) );
        }

        return list1;
    }

    @Override
    public AppFishPositionInfoListRespVO convertListInfoApp(FishPositionInfoDO bean) {
        if ( bean == null ) {
            return null;
        }

        AppFishPositionInfoListRespVO appFishPositionInfoListRespVO = new AppFishPositionInfoListRespVO();

        appFishPositionInfoListRespVO.setId( bean.getId() );
        appFishPositionInfoListRespVO.setTitle( bean.getTitle() );
        appFishPositionInfoListRespVO.setLatitude( bean.getLatitude() );
        appFishPositionInfoListRespVO.setLongitude( bean.getLongitude() );
        appFishPositionInfoListRespVO.setIsCamp( bean.getIsCamp() );
        appFishPositionInfoListRespVO.setIsFree( bean.getIsFree() );

        return appFishPositionInfoListRespVO;
    }

    @Override
    public PageResult<FishPositionInfoRespVO> convertPage(PageResult<FishPositionInfoDO> page) {
        if ( page == null ) {
            return null;
        }

        PageResult<FishPositionInfoRespVO> pageResult = new PageResult<FishPositionInfoRespVO>();

        pageResult.setList( convertList( page.getList() ) );
        pageResult.setTotal( page.getTotal() );

        return pageResult;
    }

    @Override
    public PageResult<AppFishPositionInfoRespVO> convertPageApp(PageResult<FishPositionInfoDO> page) {
        if ( page == null ) {
            return null;
        }

        PageResult<AppFishPositionInfoRespVO> pageResult = new PageResult<AppFishPositionInfoRespVO>();

        pageResult.setList( fishPositionInfoDOListToAppFishPositionInfoRespVOList( page.getList() ) );
        pageResult.setTotal( page.getTotal() );

        return pageResult;
    }

    @Override
    public List<FishPositionInfoExcelVO> convertList02(List<FishPositionInfoDO> list) {
        if ( list == null ) {
            return null;
        }

        List<FishPositionInfoExcelVO> list1 = new ArrayList<FishPositionInfoExcelVO>( list.size() );
        for ( FishPositionInfoDO fishPositionInfoDO : list ) {
            list1.add( fishPositionInfoDOToFishPositionInfoExcelVO( fishPositionInfoDO ) );
        }

        return list1;
    }

    protected List<AppFishPositionInfoRespVO> fishPositionInfoDOListToAppFishPositionInfoRespVOList(List<FishPositionInfoDO> list) {
        if ( list == null ) {
            return null;
        }

        List<AppFishPositionInfoRespVO> list1 = new ArrayList<AppFishPositionInfoRespVO>( list.size() );
        for ( FishPositionInfoDO fishPositionInfoDO : list ) {
            list1.add( convertApp( fishPositionInfoDO ) );
        }

        return list1;
    }

    protected FishPositionInfoExcelVO fishPositionInfoDOToFishPositionInfoExcelVO(FishPositionInfoDO fishPositionInfoDO) {
        if ( fishPositionInfoDO == null ) {
            return null;
        }

        FishPositionInfoExcelVO fishPositionInfoExcelVO = new FishPositionInfoExcelVO();

        fishPositionInfoExcelVO.setId( fishPositionInfoDO.getId() );
        fishPositionInfoExcelVO.setTitle( fishPositionInfoDO.getTitle() );
        fishPositionInfoExcelVO.setLatitude( fishPositionInfoDO.getLatitude() );
        fishPositionInfoExcelVO.setLongitude( fishPositionInfoDO.getLongitude() );
        fishPositionInfoExcelVO.setAddress( fishPositionInfoDO.getAddress() );
        fishPositionInfoExcelVO.setAddressDetail( fishPositionInfoDO.getAddressDetail() );
        fishPositionInfoExcelVO.setContent( fishPositionInfoDO.getContent() );
        fishPositionInfoExcelVO.setProvince( fishPositionInfoDO.getProvince() );
        fishPositionInfoExcelVO.setCity( fishPositionInfoDO.getCity() );
        fishPositionInfoExcelVO.setCounty( fishPositionInfoDO.getCounty() );
        fishPositionInfoExcelVO.setType( fishPositionInfoDO.getType() );
        fishPositionInfoExcelVO.setItime( fishPositionInfoDO.getItime() );
        fishPositionInfoExcelVO.setUtime( fishPositionInfoDO.getUtime() );
        fishPositionInfoExcelVO.setIsPass( fishPositionInfoDO.getIsPass() );
        fishPositionInfoExcelVO.setLuKuang( fishPositionInfoDO.getLuKuang() );
        fishPositionInfoExcelVO.setShuiShen( fishPositionInfoDO.getShuiShen() );
        fishPositionInfoExcelVO.setShuiBian( fishPositionInfoDO.getShuiBian() );
        fishPositionInfoExcelVO.setZhiLu( fishPositionInfoDO.getZhiLu() );
        fishPositionInfoExcelVO.setIsCamp( fishPositionInfoDO.getIsCamp() );
        fishPositionInfoExcelVO.setIsFree( fishPositionInfoDO.getIsFree() );
        fishPositionInfoExcelVO.setPrice( fishPositionInfoDO.getPrice() );
        fishPositionInfoExcelVO.setJianCheng( fishPositionInfoDO.getJianCheng() );
        fishPositionInfoExcelVO.setIsCan( fishPositionInfoDO.getIsCan() );
        fishPositionInfoExcelVO.setUserId( fishPositionInfoDO.getUserId() );
        fishPositionInfoExcelVO.setFishType( fishPositionInfoDO.getFishType() );
        fishPositionInfoExcelVO.setPositionType( fishPositionInfoDO.getPositionType() );
        fishPositionInfoExcelVO.setPlayType( fishPositionInfoDO.getPlayType() );
        if ( fishPositionInfoDO.getCollectionQty() != null ) {
            fishPositionInfoExcelVO.setCollectionQty( String.valueOf( fishPositionInfoDO.getCollectionQty() ) );
        }
        fishPositionInfoExcelVO.setCreateTime( fishPositionInfoDO.getCreateTime() );

        return fishPositionInfoExcelVO;
    }
}
