import{ao as t,__tla as i}from"./index-97fffa0c.js";let s,e,l,r,p,o,y,m=Promise.all([(()=>{try{return i}catch{}})()]).then(async()=>{e=async a=>await t.get({url:"/system/post/page",params:a}),o=async()=>await t.get({url:"/system/post/list-all-simple"}),s=async a=>await t.get({url:"/system/post/get?id="+a}),l=async a=>await t.post({url:"/system/post/create",data:a}),y=async a=>await t.put({url:"/system/post/update",data:a}),r=async a=>await t.delete({url:"/system/post/delete?id="+a}),p=async a=>await t.download({url:"/system/post/export",params:a})});export{m as __tla,s as a,e as b,l as c,r as d,p as e,o as g,y as u};
