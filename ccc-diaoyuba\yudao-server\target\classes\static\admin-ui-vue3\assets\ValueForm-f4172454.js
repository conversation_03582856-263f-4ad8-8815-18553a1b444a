import{_ as t,__tla as _}from"./ValueForm.vue_vue_type_script_setup_true_lang-9d265c83.js";import{__tla as r}from"./index-97fffa0c.js";import{__tla as a}from"./Dialog.vue_vue_type_style_index_0_lang-5af0bc2b.js";import{__tla as l}from"./property-672c0f06.js";import{__tla as o}from"./useMessage-18385d4a.js";let c=Promise.all([(()=>{try{return _}catch{}})(),(()=>{try{return r}catch{}})(),(()=>{try{return a}catch{}})(),(()=>{try{return l}catch{}})(),(()=>{try{return o}catch{}})()]).then(async()=>{});export{c as __tla,t as default};
