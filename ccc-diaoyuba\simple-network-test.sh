#!/bin/bash

# 简单网络测试脚本 - 不依赖特殊命令

TARGET="xxfb.mwr.cn"
echo "=========================================="
echo "简单网络连接测试"
echo "=========================================="

echo "1. 基本信息:"
echo "   目标域名: $TARGET"
echo "   当前时间: $(date)"
echo "   当前用户: $(whoami)"
echo ""

echo "2. 网络配置检查:"
echo "   DNS配置 (/etc/resolv.conf):"
if [ -f /etc/resolv.conf ]; then
    cat /etc/resolv.conf
else
    echo "   DNS配置文件不存在"
fi
echo ""

echo "   Hosts文件 (/etc/hosts):"
if [ -f /etc/hosts ]; then
    grep -v "^#" /etc/hosts | grep -v "^$"
else
    echo "   Hosts文件不存在"
fi
echo ""

echo "3. 网络连通性测试:"
echo "   ping测试 (3次):"
if command -v ping > /dev/null 2>&1; then
    ping -c 3 $TARGET 2>&1
else
    echo "   ping命令不可用"
fi
echo ""

echo "4. HTTP连接测试:"
echo "   curl测试:"
if command -v curl > /dev/null 2>&1; then
    echo "   尝试连接..."
    curl -I --connect-timeout 10 --max-time 30 http://$TARGET 2>&1
    echo ""
    echo "   详细连接信息:"
    curl -v --connect-timeout 10 --max-time 30 http://$TARGET 2>&1 | head -15
else
    echo "   curl命令不可用"
fi
echo ""

echo "5. 端口连通性测试:"
if command -v nc > /dev/null 2>&1; then
    echo "   nc测试80端口:"
    timeout 10 nc -zv $TARGET 80 2>&1
elif command -v telnet > /dev/null 2>&1; then
    echo "   telnet测试80端口:"
    timeout 10 telnet $TARGET 80 2>&1 | head -5
else
    echo "   nc和telnet命令都不可用"
fi
echo ""

echo "6. 网络路由信息:"
if command -v ip > /dev/null 2>&1; then
    echo "   默认路由:"
    ip route show default
    echo ""
    echo "   网络接口:"
    ip addr show | grep -E "inet |UP" | head -5
elif command -v route > /dev/null 2>&1; then
    echo "   路由表:"
    route -n | head -5
else
    echo "   无法获取路由信息"
fi
echo ""

echo "7. 防火墙检查:"
if command -v iptables > /dev/null 2>&1; then
    echo "   iptables OUTPUT规则:"
    iptables -L OUTPUT 2>/dev/null | head -5 || echo "   无法查看iptables规则"
else
    echo "   iptables命令不可用"
fi
echo ""

echo "8. 系统信息:"
echo "   操作系统:"
if [ -f /etc/os-release ]; then
    grep PRETTY_NAME /etc/os-release
elif [ -f /etc/redhat-release ]; then
    cat /etc/redhat-release
else
    uname -a
fi
echo ""

echo "9. 可用的网络工具:"
TOOLS=("ping" "curl" "wget" "nc" "telnet" "dig" "nslookup" "host")
for tool in "${TOOLS[@]}"; do
    if command -v $tool > /dev/null 2>&1; then
        echo "   ✅ $tool"
    else
        echo "   ❌ $tool"
    fi
done
echo ""

echo "10. 建议的解决步骤:"
echo "    1. 如果ping失败 -> DNS解析问题"
echo "    2. 如果ping成功但curl失败 -> 防火墙或端口问题"
echo "    3. 如果都失败 -> 网络连接问题"
echo ""
echo "    解决方案:"
echo "    - 安装网络工具: yum install bind-utils curl -y"
echo "    - 更改DNS: echo 'nameserver *******' > /etc/resolv.conf"
echo "    - 添加hosts映射: echo 'IP地址 $TARGET' >> /etc/hosts"
echo ""

echo "=========================================="
echo "测试完成"
echo "=========================================="
