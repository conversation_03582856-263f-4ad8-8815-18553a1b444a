package cn.iocoder.yudao.module.diaoyuba.convert.notecomments;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.diaoyuba.controller.app.notecomments.vo.AppNoteCommentsCreateReqVO;
import cn.iocoder.yudao.module.diaoyuba.controller.app.notecomments.vo.AppNoteCommentsExcelVO;
import cn.iocoder.yudao.module.diaoyuba.controller.app.notecomments.vo.AppNoteCommentsRespVO;
import cn.iocoder.yudao.module.diaoyuba.controller.app.notecomments.vo.AppNoteCommentsUpdateReqVO;
import cn.iocoder.yudao.module.diaoyuba.controller.app.notecomments.vo.AppNoteSubCommentsRespVO;
import cn.iocoder.yudao.module.diaoyuba.controller.app.user.vo.UserInfoRespVO;
import cn.iocoder.yudao.module.diaoyuba.dal.dataobject.notecomments.NoteCommentsDO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-21T09:34:05+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_181 (Oracle Corporation)"
)
public class NoteCommentsConvertImpl implements NoteCommentsConvert {

    @Override
    public NoteCommentsDO convert(AppNoteCommentsCreateReqVO bean) {
        if ( bean == null ) {
            return null;
        }

        NoteCommentsDO.NoteCommentsDOBuilder noteCommentsDO = NoteCommentsDO.builder();

        noteCommentsDO.noteId( bean.getNoteId() );
        noteCommentsDO.commentContent( bean.getCommentContent() );
        noteCommentsDO.commentType( bean.getCommentType() );
        noteCommentsDO.replyUserId( bean.getReplyUserId() );
        noteCommentsDO.replyCommentId( bean.getReplyCommentId() );
        noteCommentsDO.imageUrl( bean.getImageUrl() );
        noteCommentsDO.rootCommentId( bean.getRootCommentId() );

        return noteCommentsDO.build();
    }

    @Override
    public NoteCommentsDO convert(AppNoteCommentsUpdateReqVO bean) {
        if ( bean == null ) {
            return null;
        }

        NoteCommentsDO.NoteCommentsDOBuilder noteCommentsDO = NoteCommentsDO.builder();

        noteCommentsDO.id( bean.getId() );
        noteCommentsDO.noteId( bean.getNoteId() );
        noteCommentsDO.userId( bean.getUserId() );
        noteCommentsDO.commentContent( bean.getCommentContent() );
        noteCommentsDO.likeCount( bean.getLikeCount() );
        noteCommentsDO.commentType( bean.getCommentType() );
        noteCommentsDO.replyUserId( bean.getReplyUserId() );
        noteCommentsDO.replyCommentId( bean.getReplyCommentId() );
        noteCommentsDO.commentIp( bean.getCommentIp() );
        noteCommentsDO.cityName( bean.getCityName() );
        noteCommentsDO.imageUrl( bean.getImageUrl() );
        noteCommentsDO.subCommentCount( bean.getSubCommentCount() );

        return noteCommentsDO.build();
    }

    @Override
    public AppNoteCommentsRespVO convert(NoteCommentsDO bean) {
        if ( bean == null ) {
            return null;
        }

        AppNoteCommentsRespVO appNoteCommentsRespVO = new AppNoteCommentsRespVO();

        appNoteCommentsRespVO.setUserInfo( noteCommentsDOToUserInfoRespVO( bean ) );
        appNoteCommentsRespVO.setId( bean.getId() );
        appNoteCommentsRespVO.setCreateTime( bean.getCreateTime() );
        appNoteCommentsRespVO.setNoteId( bean.getNoteId() );
        appNoteCommentsRespVO.setCommentContent( bean.getCommentContent() );
        appNoteCommentsRespVO.setLikeCount( bean.getLikeCount() );
        appNoteCommentsRespVO.setCityName( bean.getCityName() );
        appNoteCommentsRespVO.setSubCommentCount( bean.getSubCommentCount() );
        appNoteCommentsRespVO.setImageUrl( bean.getImageUrl() );

        return appNoteCommentsRespVO;
    }

    @Override
    public AppNoteSubCommentsRespVO convertToSub(NoteCommentsDO bean) {
        if ( bean == null ) {
            return null;
        }

        AppNoteSubCommentsRespVO appNoteSubCommentsRespVO = new AppNoteSubCommentsRespVO();

        appNoteSubCommentsRespVO.setUserInfo( noteCommentsDOToUserInfoRespVO1( bean ) );
        appNoteSubCommentsRespVO.setReplyUserInfo( noteCommentsDOToUserInfoRespVO2( bean ) );
        appNoteSubCommentsRespVO.setId( bean.getId() );
        appNoteSubCommentsRespVO.setCreateTime( bean.getCreateTime() );
        appNoteSubCommentsRespVO.setNoteId( bean.getNoteId() );
        appNoteSubCommentsRespVO.setCommentContent( bean.getCommentContent() );
        appNoteSubCommentsRespVO.setLikeCount( bean.getLikeCount() );
        appNoteSubCommentsRespVO.setCityName( bean.getCityName() );
        appNoteSubCommentsRespVO.setImageUrl( bean.getImageUrl() );
        appNoteSubCommentsRespVO.setReplyCommentId( bean.getReplyCommentId() );
        appNoteSubCommentsRespVO.setRootCommentId( bean.getRootCommentId() );

        return appNoteSubCommentsRespVO;
    }

    @Override
    public List<AppNoteCommentsRespVO> convertList(List<NoteCommentsDO> list) {
        if ( list == null ) {
            return null;
        }

        List<AppNoteCommentsRespVO> list1 = new ArrayList<AppNoteCommentsRespVO>( list.size() );
        for ( NoteCommentsDO noteCommentsDO : list ) {
            list1.add( convert( noteCommentsDO ) );
        }

        return list1;
    }

    @Override
    public List<AppNoteSubCommentsRespVO> convertToSubList(List<NoteCommentsDO> list) {
        if ( list == null ) {
            return null;
        }

        List<AppNoteSubCommentsRespVO> list1 = new ArrayList<AppNoteSubCommentsRespVO>( list.size() );
        for ( NoteCommentsDO noteCommentsDO : list ) {
            list1.add( convertToSub( noteCommentsDO ) );
        }

        return list1;
    }

    @Override
    public PageResult<AppNoteCommentsRespVO> convertPage(PageResult<NoteCommentsDO> page) {
        if ( page == null ) {
            return null;
        }

        PageResult<AppNoteCommentsRespVO> pageResult = new PageResult<AppNoteCommentsRespVO>();

        pageResult.setList( convertList( page.getList() ) );
        pageResult.setTotal( page.getTotal() );

        return pageResult;
    }

    @Override
    public PageResult<AppNoteSubCommentsRespVO> convertPageToSub(PageResult<NoteCommentsDO> page) {
        if ( page == null ) {
            return null;
        }

        PageResult<AppNoteSubCommentsRespVO> pageResult = new PageResult<AppNoteSubCommentsRespVO>();

        pageResult.setList( convertToSubList( page.getList() ) );
        pageResult.setTotal( page.getTotal() );

        return pageResult;
    }

    @Override
    public List<AppNoteCommentsExcelVO> convertList02(List<NoteCommentsDO> list) {
        if ( list == null ) {
            return null;
        }

        List<AppNoteCommentsExcelVO> list1 = new ArrayList<AppNoteCommentsExcelVO>( list.size() );
        for ( NoteCommentsDO noteCommentsDO : list ) {
            list1.add( noteCommentsDOToAppNoteCommentsExcelVO( noteCommentsDO ) );
        }

        return list1;
    }

    protected UserInfoRespVO noteCommentsDOToUserInfoRespVO(NoteCommentsDO noteCommentsDO) {
        if ( noteCommentsDO == null ) {
            return null;
        }

        UserInfoRespVO userInfoRespVO = new UserInfoRespVO();

        userInfoRespVO.setId( noteCommentsDO.getUserId() );

        return userInfoRespVO;
    }

    protected UserInfoRespVO noteCommentsDOToUserInfoRespVO1(NoteCommentsDO noteCommentsDO) {
        if ( noteCommentsDO == null ) {
            return null;
        }

        UserInfoRespVO userInfoRespVO = new UserInfoRespVO();

        userInfoRespVO.setId( noteCommentsDO.getUserId() );

        return userInfoRespVO;
    }

    protected UserInfoRespVO noteCommentsDOToUserInfoRespVO2(NoteCommentsDO noteCommentsDO) {
        if ( noteCommentsDO == null ) {
            return null;
        }

        UserInfoRespVO userInfoRespVO = new UserInfoRespVO();

        userInfoRespVO.setId( noteCommentsDO.getReplyUserId() );

        return userInfoRespVO;
    }

    protected AppNoteCommentsExcelVO noteCommentsDOToAppNoteCommentsExcelVO(NoteCommentsDO noteCommentsDO) {
        if ( noteCommentsDO == null ) {
            return null;
        }

        AppNoteCommentsExcelVO appNoteCommentsExcelVO = new AppNoteCommentsExcelVO();

        appNoteCommentsExcelVO.setId( noteCommentsDO.getId() );
        appNoteCommentsExcelVO.setNoteId( noteCommentsDO.getNoteId() );
        appNoteCommentsExcelVO.setUserId( noteCommentsDO.getUserId() );
        appNoteCommentsExcelVO.setCommentContent( noteCommentsDO.getCommentContent() );
        appNoteCommentsExcelVO.setLikeCount( noteCommentsDO.getLikeCount() );
        appNoteCommentsExcelVO.setCommentType( noteCommentsDO.getCommentType() );
        appNoteCommentsExcelVO.setReplyUserId( noteCommentsDO.getReplyUserId() );
        appNoteCommentsExcelVO.setReplyCommentId( noteCommentsDO.getReplyCommentId() );
        appNoteCommentsExcelVO.setCommentIp( noteCommentsDO.getCommentIp() );
        appNoteCommentsExcelVO.setCreateTime( noteCommentsDO.getCreateTime() );

        return appNoteCommentsExcelVO;
    }
}
