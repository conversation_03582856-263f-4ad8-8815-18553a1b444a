package cn.iocoder.yudao.module.system.convert.tenant;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantCreateReqVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantExcelVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantRespVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantSimpleRespVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantUpdateReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantDO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-21T09:32:49+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_181 (Oracle Corporation)"
)
public class TenantConvertImpl implements TenantConvert {

    @Override
    public TenantDO convert(TenantCreateReqVO bean) {
        if ( bean == null ) {
            return null;
        }

        TenantDO.TenantDOBuilder tenantDO = TenantDO.builder();

        tenantDO.name( bean.getName() );
        tenantDO.contactName( bean.getContactName() );
        tenantDO.contactMobile( bean.getContactMobile() );
        tenantDO.status( bean.getStatus() );
        tenantDO.website( bean.getWebsite() );
        tenantDO.packageId( bean.getPackageId() );
        tenantDO.expireTime( bean.getExpireTime() );
        tenantDO.accountCount( bean.getAccountCount() );

        return tenantDO.build();
    }

    @Override
    public TenantDO convert(TenantUpdateReqVO bean) {
        if ( bean == null ) {
            return null;
        }

        TenantDO.TenantDOBuilder tenantDO = TenantDO.builder();

        tenantDO.id( bean.getId() );
        tenantDO.name( bean.getName() );
        tenantDO.contactName( bean.getContactName() );
        tenantDO.contactMobile( bean.getContactMobile() );
        tenantDO.status( bean.getStatus() );
        tenantDO.website( bean.getWebsite() );
        tenantDO.packageId( bean.getPackageId() );
        tenantDO.expireTime( bean.getExpireTime() );
        tenantDO.accountCount( bean.getAccountCount() );

        return tenantDO.build();
    }

    @Override
    public TenantRespVO convert(TenantDO bean) {
        if ( bean == null ) {
            return null;
        }

        TenantRespVO tenantRespVO = new TenantRespVO();

        tenantRespVO.setName( bean.getName() );
        tenantRespVO.setContactName( bean.getContactName() );
        tenantRespVO.setContactMobile( bean.getContactMobile() );
        tenantRespVO.setStatus( bean.getStatus() );
        tenantRespVO.setWebsite( bean.getWebsite() );
        tenantRespVO.setPackageId( bean.getPackageId() );
        tenantRespVO.setExpireTime( bean.getExpireTime() );
        tenantRespVO.setAccountCount( bean.getAccountCount() );
        tenantRespVO.setId( bean.getId() );
        tenantRespVO.setCreateTime( bean.getCreateTime() );

        return tenantRespVO;
    }

    @Override
    public TenantSimpleRespVO convert03(TenantDO bean) {
        if ( bean == null ) {
            return null;
        }

        TenantSimpleRespVO tenantSimpleRespVO = new TenantSimpleRespVO();

        tenantSimpleRespVO.setContactName( bean.getContactName() );
        tenantSimpleRespVO.setContactMobile( bean.getContactMobile() );
        tenantSimpleRespVO.setStatus( bean.getStatus() );
        tenantSimpleRespVO.setWebsite( bean.getWebsite() );
        tenantSimpleRespVO.setPackageId( bean.getPackageId() );
        tenantSimpleRespVO.setExpireTime( bean.getExpireTime() );
        tenantSimpleRespVO.setAccountCount( bean.getAccountCount() );
        tenantSimpleRespVO.setId( bean.getId() );
        tenantSimpleRespVO.setName( bean.getName() );

        return tenantSimpleRespVO;
    }

    @Override
    public List<TenantRespVO> convertList(List<TenantDO> list) {
        if ( list == null ) {
            return null;
        }

        List<TenantRespVO> list1 = new ArrayList<TenantRespVO>( list.size() );
        for ( TenantDO tenantDO : list ) {
            list1.add( convert( tenantDO ) );
        }

        return list1;
    }

    @Override
    public PageResult<TenantRespVO> convertPage(PageResult<TenantDO> page) {
        if ( page == null ) {
            return null;
        }

        PageResult<TenantRespVO> pageResult = new PageResult<TenantRespVO>();

        pageResult.setList( convertList( page.getList() ) );
        pageResult.setTotal( page.getTotal() );

        return pageResult;
    }

    @Override
    public List<TenantExcelVO> convertList02(List<TenantDO> list) {
        if ( list == null ) {
            return null;
        }

        List<TenantExcelVO> list1 = new ArrayList<TenantExcelVO>( list.size() );
        for ( TenantDO tenantDO : list ) {
            list1.add( tenantDOToTenantExcelVO( tenantDO ) );
        }

        return list1;
    }

    protected TenantExcelVO tenantDOToTenantExcelVO(TenantDO tenantDO) {
        if ( tenantDO == null ) {
            return null;
        }

        TenantExcelVO tenantExcelVO = new TenantExcelVO();

        tenantExcelVO.setId( tenantDO.getId() );
        tenantExcelVO.setName( tenantDO.getName() );
        tenantExcelVO.setContactName( tenantDO.getContactName() );
        tenantExcelVO.setContactMobile( tenantDO.getContactMobile() );
        tenantExcelVO.setStatus( tenantDO.getStatus() );
        tenantExcelVO.setCreateTime( tenantDO.getCreateTime() );

        return tenantExcelVO;
    }
}
