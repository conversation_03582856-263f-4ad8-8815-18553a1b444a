package cn.iocoder.yudao.module.diaoyuba.convert.fishpositionimg;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.diaoyuba.controller.admin.fishpositionimg.vo.FishPositionImgCreateReqVO;
import cn.iocoder.yudao.module.diaoyuba.controller.admin.fishpositionimg.vo.FishPositionImgExcelVO;
import cn.iocoder.yudao.module.diaoyuba.controller.admin.fishpositionimg.vo.FishPositionImgRespVO;
import cn.iocoder.yudao.module.diaoyuba.controller.admin.fishpositionimg.vo.FishPositionImgUpdateReqVO;
import cn.iocoder.yudao.module.diaoyuba.dal.dataobject.fishpositionimg.FishPositionImgDO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-21T09:34:05+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_181 (Oracle Corporation)"
)
public class FishPositionImgConvertImpl implements FishPositionImgConvert {

    @Override
    public FishPositionImgDO convert(FishPositionImgCreateReqVO bean) {
        if ( bean == null ) {
            return null;
        }

        FishPositionImgDO.FishPositionImgDOBuilder fishPositionImgDO = FishPositionImgDO.builder();

        fishPositionImgDO.positionId( bean.getPositionId() );
        fishPositionImgDO.imagePath( bean.getImagePath() );
        fishPositionImgDO.copyTime( bean.getCopyTime() );
        fishPositionImgDO.sortNo( bean.getSortNo() );

        return fishPositionImgDO.build();
    }

    @Override
    public FishPositionImgDO convert(FishPositionImgUpdateReqVO bean) {
        if ( bean == null ) {
            return null;
        }

        FishPositionImgDO.FishPositionImgDOBuilder fishPositionImgDO = FishPositionImgDO.builder();

        fishPositionImgDO.id( bean.getId() );
        fishPositionImgDO.positionId( bean.getPositionId() );
        fishPositionImgDO.imagePath( bean.getImagePath() );
        fishPositionImgDO.copyTime( bean.getCopyTime() );
        fishPositionImgDO.sortNo( bean.getSortNo() );

        return fishPositionImgDO.build();
    }

    @Override
    public FishPositionImgRespVO convert(FishPositionImgDO bean) {
        if ( bean == null ) {
            return null;
        }

        FishPositionImgRespVO fishPositionImgRespVO = new FishPositionImgRespVO();

        fishPositionImgRespVO.setPositionId( bean.getPositionId() );
        fishPositionImgRespVO.setImagePath( bean.getImagePath() );
        fishPositionImgRespVO.setCopyTime( bean.getCopyTime() );
        fishPositionImgRespVO.setSortNo( bean.getSortNo() );
        fishPositionImgRespVO.setId( bean.getId() );
        fishPositionImgRespVO.setCreateTime( bean.getCreateTime() );

        return fishPositionImgRespVO;
    }

    @Override
    public List<FishPositionImgRespVO> convertList(List<FishPositionImgDO> list) {
        if ( list == null ) {
            return null;
        }

        List<FishPositionImgRespVO> list1 = new ArrayList<FishPositionImgRespVO>( list.size() );
        for ( FishPositionImgDO fishPositionImgDO : list ) {
            list1.add( convert( fishPositionImgDO ) );
        }

        return list1;
    }

    @Override
    public PageResult<FishPositionImgRespVO> convertPage(PageResult<FishPositionImgDO> page) {
        if ( page == null ) {
            return null;
        }

        PageResult<FishPositionImgRespVO> pageResult = new PageResult<FishPositionImgRespVO>();

        pageResult.setList( convertList( page.getList() ) );
        pageResult.setTotal( page.getTotal() );

        return pageResult;
    }

    @Override
    public List<FishPositionImgExcelVO> convertList02(List<FishPositionImgDO> list) {
        if ( list == null ) {
            return null;
        }

        List<FishPositionImgExcelVO> list1 = new ArrayList<FishPositionImgExcelVO>( list.size() );
        for ( FishPositionImgDO fishPositionImgDO : list ) {
            list1.add( fishPositionImgDOToFishPositionImgExcelVO( fishPositionImgDO ) );
        }

        return list1;
    }

    protected FishPositionImgExcelVO fishPositionImgDOToFishPositionImgExcelVO(FishPositionImgDO fishPositionImgDO) {
        if ( fishPositionImgDO == null ) {
            return null;
        }

        FishPositionImgExcelVO fishPositionImgExcelVO = new FishPositionImgExcelVO();

        fishPositionImgExcelVO.setId( fishPositionImgDO.getId() );
        fishPositionImgExcelVO.setPositionId( fishPositionImgDO.getPositionId() );
        fishPositionImgExcelVO.setImagePath( fishPositionImgDO.getImagePath() );
        fishPositionImgExcelVO.setCopyTime( fishPositionImgDO.getCopyTime() );
        fishPositionImgExcelVO.setSortNo( fishPositionImgDO.getSortNo() );
        fishPositionImgExcelVO.setCreateTime( fishPositionImgDO.getCreateTime() );

        return fishPositionImgExcelVO;
    }
}
